# Development Patterns - Technical Excellence Framework

## WORKSPACE ORGANIZATION PATTERNS

### Workspace Root Rule (ABSOLUTE)
**NO CODE, TESTS, SCRIPTS, OR DOCS IN WORKSPACE ROOT**
- Only allowed files in root: CLAUDE.md, README.md, configuration files
- All code → apps/
- All tests → apps/*/tests/
- All scripts → scripts/ or apps/*/scripts/
- All docs → docs/
- **Zero tolerance policy - no exceptions**

### File Structure Organization
- **MIS Platform Architecture**: apps/giki-ai-api (FastAPI) + apps/giki-ai-app (React)
- **Test Data Centralization**: ALL test data in `libs/test-data/` - NO scattered files
- **Script Organization**: Development scripts in scripts/, app-specific in apps/*/scripts/
- **Documentation Structure**: Organized in docs/ with numbered priority system

## COMMAND EXECUTION PATTERNS

### pnpm-First Principle (NX REMOVED)
**NEVER USE NX COMMANDS** - All development operations use direct pnpm scripts from workspace root

#### Command Execution Context
```bash
# ALWAYS execute from workspace root - verify before EVERY command sequence:
pwd  # Must show: /Users/<USER>/giki-ai-workspace/

# For ALL commands, use direct pnpm scripts from workspace root:
pnpm lint:api         # Runs ruff in api directory, returns to root
pnpm serve:api        # Starts uvicorn server, returns to root
pnpm build:app        # Runs vite build, returns to root
```

#### PNPM Scripts Quick Reference
```bash
# DEVELOPMENT - Direct execution (terminal output)
pnpm serve                 # Both API:8000 + frontend:4200 with concurrently
pnpm serve:api             # uvicorn FastAPI server
pnpm serve:app             # Vite development server

# QUALITY - Background execution (prevents timeouts)
pnpm lint:api:bg           # Python ruff linting in background
pnpm lint:app:bg           # Frontend ESLint linting in background

# TESTING - Background execution (prevents timeouts, auto-starts services)
pnpm test:api:bg [unit|integration|slow|all]  # API pytest in background
pnpm test:app:bg [unit|coverage|visual|integration|e2e]  # Frontend testing in background
pnpm test:visual                             # Visual regression tests (auto-starts servers)
pnpm test:e2e                               # E2E tests (auto-starts servers)

# BUILD - Background execution (prevents timeouts)
pnpm build:api             # FastAPI ready message (no build needed)
pnpm build:app:bg          # Vite production build in background

# LOG VIEWING - Real-time monitoring
pnpm logs                  # Status overview
pnpm logs:lint             # Follow linting logs
pnpm logs:test             # Follow testing logs
pnpm logs:build            # Follow build logs
pnpm logs:serve            # Follow server logs

# UTILITIES
pnpm db                    # Test PostgreSQL connection (giki_ai_dev database)
```

#### Critical Execution Rules
1. **ALWAYS use pnpm scripts** - Never bypass with direct commands
2. **BACKGROUND EXECUTION** - Use background scripts (lint:api:bg, test:app:bg, etc.) to avoid timeout issues
3. **LOG OVERWRITE** - All background scripts overwrite logs (>) not append (>>) to prevent massive files
4. **AUTO-START SERVICES** - Scripts automatically start PostgreSQL, Redis, API/Frontend servers when needed
5. **Workspace root execution** - All scripts run from workspace root
6. **IMPROVE pnpm commands** - When scripts fail, fix the scripts rather than bypassing them
7. **SUBSHELL PATTERN** - All pnpm scripts use `(cd dir && command)` to maintain workspace root

## CLAUDE CODE OPTIMIZATION PATTERNS

### Background-First Execution Strategy
**ALL SLOW OPERATIONS RUN IN BACKGROUND WITH LOG OVERWRITE**

```typescript
interface ClaudeCodeOptimizedWorkflow {
  // Background scripts prevent timeouts
  backgroundCommands: {
    "pnpm lint:app:bg": "Background frontend linting (never direct)",
    "pnpm test:api:bg unit": "Background API unit tests",
    "pnpm test:app:bg unit": "Background frontend unit tests",
    "pnpm test:visual": "Auto-starts all services, background execution",
    "pnpm test:e2e": "Auto-starts all services, background execution"
  };
  
  // Fast commands work directly
  fastCommands: {
    "pnpm lint:api": "Python linting is fast enough",
    "pnpm db": "Database test after auto-start",
    "pnpm logs": "Status monitoring"
  };
  
  // Auto-service startup
  smartServices: {
    "PostgreSQL": "Auto-started by serve:api and test scripts",
    "Redis": "Auto-started with graceful fallback if unavailable",
    "API/Frontend": "Auto-started by test scripts when needed"
  };
}
```

### Service Management Patterns
- **AUTO-START SERVICES**: All test commands automatically start PostgreSQL, Redis, servers as needed
- **PORT DETECTION**: Scripts intelligently detect running services to avoid conflicts
- **LOG OVERWRITE**: Background scripts overwrite logs (>) to keep files manageable for Claude
- **NO TIMEOUTS**: Background execution eliminates Claude Code timeout issues

### Eliminated Inefficient Practices
**Timeout-Prone Commands (NEVER USE):**
- ❌ `pnpm lint:app` → ✅ `pnpm lint:app:bg`
- ❌ `pnpm test:api` → ✅ `pnpm test:api:bg [unit|integration|slow|all]`
- ❌ `pnpm test:app` → ✅ `pnpm test:app:bg [unit|coverage|visual|integration|e2e]`
- ❌ `pnpm build:app` → ✅ `pnpm build:app:bg`

**Manual Service Management (ELIMINATED):**
- ❌ Manual PostgreSQL/Redis startup → ✅ Auto-started by scripts
- ❌ Manual API/Frontend startup → ✅ Auto-started when needed
- ❌ Log rotation → ✅ Log overwrite strategy
- ❌ Manual process cleanup → ✅ Automatic management

## MIS-FIRST ARCHITECTURE PATTERNS

### Core Platform Principles
- **UNIFIED PRODUCT**: Every customer gets a complete MIS with progressive enhancement opportunities
- **CATEGORIZATION**: Hierarchical categorization with AI-powered accuracy improvements
- **ACCURACY FOCUS**: Business value through accuracy improvements, not technical complexity

### Development Efficiency Patterns
- **AUTH HELPER**: Use scripts/auth-helper.py for automated authentication in testing
- **PARALLEL EXECUTION**: Run independent tools simultaneously for 40-60% time savings
- **SMART MONITORING**: Use pnpm logs commands for real-time monitoring
- **ZERO TOLERANCE FOR FAKE DATA**: NEVER create mock data, placeholder values, simulated responses

### Platform Detection & Branch Strategy
**CRITICAL**: Always detect platform first to determine correct development branch
- **master** - macOS/Darwin development (primary)
- **poppy** - Pop!_OS/Linux development 
- **windy** - Windows development (future)

```bash
uname -a                    # Darwin = master, Linux = poppy, Windows = windy
git branch --show-current   # Verify current branch matches platform
```

## ANTI-FRAGMENTATION PATTERNS

### Core Anti-Fragmentation Principle
**NEVER CREATE MULTIPLE VERSIONS - ALWAYS MODIFY EXISTING FILES**
Enhance existing files rather than creating simplified/alternate/fixed versions. This prevents workspace fragmentation and maintains single sources of truth across ALL file types: code, tests, scripts, configs, documentation.

### File Modification Patterns
- **Single Source of Truth**: One file per functionality, no duplicates
- **Enhancement Over Creation**: Improve existing files rather than creating new ones
- **Version Consolidation**: Merge multiple versions into single enhanced version
- **Documentation Evolution**: Update existing docs rather than creating new ones

## ENVIRONMENT CONFIGURATION PATTERNS

### Environment Management Strategy
- **Environment Files**: Keep development and production env files in `infrastructure/environments/`
- **Service Account Paths**: Store GCP service account file paths in env files (later replaced with secrets)
- **Auto-Loading**: Config system automatically loads from infrastructure directory based on ENVIRONMENT variable
- **Security**: Production secrets will be managed through Google Cloud Secret Manager

### Configuration Hierarchy
1. **Development**: Local development with test data and development services
2. **Staging**: Production-like environment for final validation
3. **Production**: Live environment with real data and production services

## SCREENSHOT ANALYSIS PATTERNS

### Unified Analysis Tool Usage
- **UNIFIED TOOL**: Use `mcp__screenshot__analyze_screenshots` for all visual analysis
- **PROBLEM-DISCOVERY FOCUS**: Tool configured to FIND PROBLEMS, not validate success
- **DOCUMENTATION CONTEXT**: Always include relevant docs (05-DESIGN-SYSTEM.md, etc.)
- **MANDATORY WORKFLOW**: Capture → Read images → Analyze with context

### Tool Parameters & Usage
```typescript
interface AnalyzeScreenshotsArgs {
  image_paths: string[];           // Array of screenshot file paths (required)
  markdown_paths?: string[];       // Optional documentation context files  
  query: string;                   // Specific analysis question (required)
}

// Example Usage Patterns
const SCREENSHOT_ANALYSIS_PATTERNS = {
  // Brand Compliance Validation  
  brandCompliance: {
    image_paths: ["/path/to/screenshots/development/page-current.png"],
    markdown_paths: ["/path/to/docs/05-DESIGN-SYSTEM.md"],
    query: "Audit for brand compliance violations: check for prohibited emoji icons (🎯🚀💡📊✨🔄⚡✓), non-#295343 colors, spacing violations, typography inconsistencies."
  }
};
```

### Visual Analysis Workflow
```typescript
interface ScreenshotAnalysisWorkflow {
  capture: "Take screenshots of current state";
  read: "Read screenshot files to see actual content";
  analyze: "Use MCP screenshot analysis with documentation context";
  document: "Record findings and required changes";
  implement: "Make necessary corrections based on analysis";
}
```

## TOOL SELECTION PATTERNS

### Intelligent Tool Selection Strategy
- **Parallel Execution**: Run independent tools simultaneously (40-60% time savings)
- **Task Tool**: Use for complex searches; avoid for known paths or simple operations
- **Thinking Depth**: ULTRATHINK (architecture), think harder (analysis), think (simple checks)

```typescript
interface ToolSelectionStrategy {
  // Use Task tool for complex, open-ended searches
  complexSearchScenarios: {
    keywordSearch: "When searching for 'config' or 'logger' across codebase";
    fileDiscovery: "When asking 'which file does X?' without specific location";
    patternIdentification: "When looking for usage patterns across multiple files";
    architectureAnalysis: "When understanding system-wide relationships";
  };
  
  // Use direct tools for specific, known targets
  directToolScenarios: {
    specificFiles: "When reading known file paths, use Read tool directly";
    classDefinitions: "When searching for 'class Foo', use Glob tool directly";
    limitedScope: "When searching within 2-3 specific files, use Read tool";
    knownPatterns: "When looking for specific code patterns in known locations";
  };
}
```

---

**TECHNICAL EXCELLENCE**: These patterns ensure consistent, high-quality development practices across all project work.