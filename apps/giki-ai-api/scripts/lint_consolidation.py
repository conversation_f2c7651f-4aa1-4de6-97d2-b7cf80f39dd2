#!/usr/bin/env python3
"""
Custom Linting Script for giki.ai Consolidation Enforcement
============================================================

This script enforces the unified architecture patterns and prevents
regressions back to old scattered patterns.

Checks for:
1. Deprecated router patterns
2. Old service patterns
3. Scattered configuration usage
4. Direct database access patterns
5. Non-unified middleware usage
6. Legacy error handling patterns
"""

import ast
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple


class ConsolidationLinter:
    """Custom linter to enforce unified architecture patterns."""
    
    def __init__(self, root_path: Path):
        self.root_path = root_path
        self.violations: List[Dict] = []
        
        # Patterns to detect
        self.deprecated_patterns = {
            "direct_router_import": [
                r"from fastapi import APIRouter",
                r"router = APIRouter\(",
            ],
            "legacy_router_creation": [
                r"router = APIRouter\(",
                r"app\.include_router\([^R]",  # Not using RouterFactory
            ],
            "direct_db_access": [
                r"from \.\.core\.database import get_db_session",
                r"from \.\.\.core\.database import get_db_session",
            ],
            "scattered_config": [
                r"from \.\.core\.config import settings",
                r"import os\.environ",
                r"os\.getenv\(",
            ],
            "old_service_patterns": [
                r"class \w+Service\(\):",  # Service with no inheritance - only flag empty inheritance
                r"def __init__\(self, conn: Connection\):",  # Very specific pattern - direct conn only
            ],
            "legacy_error_handling": [
                r"raise HTTPException\(",
                r"try:",  # Should use UnifiedErrorHandler
            ],
            "non_unified_middleware": [
                r"@app\.middleware\(",
                r"app\.add_middleware\(",
            ],
        }
        
        # Required patterns for new files - using simpler checks
        self.required_patterns = {}
        
        # Files that are exempt from these rules (legacy compatibility)
        self.exempt_files = {
            "**/legacy/**",
            "**/domains/*/router.py",  # Original routers during transition
            "**/domains/*/secure_router.py",  # Original secure routers
            "**/core/main.py",  # Main app file has special patterns
        }
        
        # Directories to exclude completely
        self.excluded_dirs = {
            ".venv",
            "site-packages", 
            "__pycache__",
            ".git",
            "node_modules",
            "dist",
            "build",
            "migrations",
            "htmlcov",
            "test-results",
            "coverage_html_report"
        }
    
    def check_file(self, file_path: Path) -> List[Dict]:
        """Check a single Python file for violations."""
        violations = []
        
        # Skip exempt files
        if self._is_exempt(file_path):
            return violations
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for deprecated patterns
            violations.extend(self._check_deprecated_patterns(file_path, content))
            
            # Check for required patterns in new files
            violations.extend(self._check_required_patterns(file_path, content))
            
            # Check AST for more complex patterns
            violations.extend(self._check_ast_patterns(file_path, content))
            
        except Exception as e:
            violations.append({
                "file": str(file_path),
                "line": 0,
                "type": "error",
                "message": f"Failed to lint file: {str(e)}",
                "severity": "error"
            })
        
        return violations
    
    def _is_exempt(self, file_path: Path) -> bool:
        """Check if file is exempt from linting rules."""
        file_str = str(file_path)
        
        # Check if file is in excluded directory
        for excluded_dir in self.excluded_dirs:
            if excluded_dir in file_str:
                return True
        
        # Check exempt file patterns
        for pattern in self.exempt_files:
            if self._match_glob_pattern(file_str, pattern):
                return True
        
        return False
    
    def _match_glob_pattern(self, file_str: str, pattern: str) -> bool:
        """Simple glob pattern matching."""
        # Convert glob pattern to regex
        regex_pattern = pattern.replace("**", ".*").replace("*", "[^/]*")
        return bool(re.search(regex_pattern, file_str))
    
    def _check_deprecated_patterns(self, file_path: Path, content: str) -> List[Dict]:
        """Check for deprecated patterns that should be avoided."""
        violations = []
        lines = content.split('\n')
        
        for pattern_type, patterns in self.deprecated_patterns.items():
            for pattern in patterns:
                for line_num, line in enumerate(lines, 1):
                    if re.search(pattern, line):
                        # Special handling for certain patterns
                        if self._is_acceptable_usage(file_path, line, pattern_type):
                            continue
                        
                        violations.append({
                            "file": str(file_path),
                            "line": line_num,
                            "type": "deprecated_pattern",
                            "pattern": pattern_type,
                            "message": f"Deprecated pattern '{pattern_type}' found: {line.strip()}",
                            "severity": "warning",
                            "suggestion": self._get_suggestion(pattern_type),
                        })
        
        return violations
    
    def _check_required_patterns(self, file_path: Path, content: str) -> List[Dict]:
        """Check that new files follow required patterns."""
        violations = []
        
        # Only check unified routers for proper inheritance
        if "unified" in str(file_path) and "router" in str(file_path):
            if "UnifiedBaseRouter" not in content:
                violations.append({
                    "file": str(file_path),
                    "line": 1,
                    "type": "missing_pattern",
                    "pattern": "unified_router_inheritance",
                    "message": "Unified router should inherit from UnifiedBaseRouter",
                    "severity": "error",
                    "suggestion": "Ensure router inherits from UnifiedBaseRouter",
                })
        
        return violations
    
    def _check_ast_patterns(self, file_path: Path, content: str) -> List[Dict]:
        """Check AST for complex patterns."""
        violations = []
        
        try:
            tree = ast.parse(content)
            
            # Check for direct APIRouter usage
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name == "fastapi" and hasattr(node, 'lineno'):
                            # Check if APIRouter is used directly
                            violations.append({
                                "file": str(file_path),
                                "line": node.lineno,
                                "type": "direct_import",
                                "message": "Direct FastAPI import found - use unified patterns",
                                "severity": "info",
                            })
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module == "fastapi" and any(alias.name == "APIRouter" for alias in node.names):
                        violations.append({
                            "file": str(file_path),
                            "line": node.lineno,
                            "type": "direct_router_import",
                            "message": "Direct APIRouter import - use UnifiedBaseRouter instead",
                            "severity": "warning",
                        })
        
        except SyntaxError:
            # File has syntax errors, skip AST checks
            pass
        
        return violations
    
    def _get_file_type(self, file_path: Path) -> str:
        """Determine the type of file for pattern checking."""
        file_str = str(file_path)
        file_name = file_path.name
        
        # Only check NEW unified files for required patterns
        if ("unified" in file_name and "router" in file_name) or file_name.endswith("_router.py"):
            # Skip legacy routers
            if any(legacy in file_str for legacy in ["api/v1/endpoints", "legacy"]):
                return ""
            return "routers"
        elif ("unified" in file_name and "service" in file_name) or file_name.endswith("_service.py"):
            # Skip certain service types that don't need to inherit from BaseService
            if any(skip in file_str for skip in ["temporal_accuracy_service", "schema_import_service", "legacy"]):
                return ""
            return "services"
        
        return ""
    
    def _is_acceptable_usage(self, file_path: Path, line: str, pattern_type: str) -> bool:
        """Check if a deprecated pattern usage is acceptable in context."""
        file_str = str(file_path)
        
        # Allow certain patterns in specific contexts
        if pattern_type == "legacy_error_handling":
            # Allow HTTPException in unified error handlers and legacy routers
            if any(term in file_str for term in ["error_handler", "unified_error", "legacy", "api/v1/endpoints"]):
                return True
            # Allow try/except for basic error handling - too broad to enforce everywhere
            if "try:" in line.strip():
                return True
        
        if pattern_type == "direct_db_access":
            # Allow in dependency modules and legacy files
            if any(term in file_str for term in ["dependencies.py", "legacy", "core/"]):
                return True
        
        if pattern_type == "non_unified_middleware":
            # Allow in main.py for app setup
            if "main.py" in file_str:
                return True
        
        if pattern_type == "direct_router_import":
            # Allow in legacy API endpoints that haven't been migrated yet
            if "api/v1/endpoints" in file_str:
                return True
        
        if pattern_type == "legacy_router_creation":
            # Allow in legacy API endpoints
            if "api/v1/endpoints" in file_str:
                return True
        
        if pattern_type == "scattered_config":
            # Allow in configuration files and legacy modules
            if any(term in file_str for term in ["config.py", "settings.py", "legacy", "core/"]):
                return True
        
        return False
    
    def _get_suggestion(self, pattern_type: str) -> str:
        """Get suggestion for fixing deprecated pattern."""
        suggestions = {
            "direct_router_import": "Use RouterFactory.create_*_router() instead of direct APIRouter",
            "legacy_router_creation": "Inherit from UnifiedBaseRouter instead of creating APIRouter directly",
            "direct_db_access": "Use dependency injection through UnifiedBaseRouter",
            "scattered_config": "Use unified configuration system from libs/config",
            "old_service_patterns": "Inherit from BaseService for standardized patterns",
            "legacy_error_handling": "Use UnifiedErrorHandler for consistent error handling",
            "non_unified_middleware": "Use UnifiedMiddleware for standardized middleware",
        }
        return suggestions.get(pattern_type, "Follow unified architecture patterns")
    
    def lint_directory(self, directory: Path) -> List[Dict]:
        """Lint all Python files in a directory."""
        all_violations = []
        
        for py_file in directory.rglob("*.py"):
            # Skip if file is in excluded directory
            if self._is_exempt(py_file):
                continue
                
            violations = self.check_file(py_file)
            all_violations.extend(violations)
        
        return all_violations
    
    def generate_report(self, violations: List[Dict]) -> str:
        """Generate a formatted report of violations."""
        if not violations:
            return "✅ No consolidation violations found!"
        
        report = ["🔍 Consolidation Linting Report", "=" * 50, ""]
        
        # Group by severity
        by_severity = {}
        for violation in violations:
            severity = violation.get("severity", "info")
            if severity not in by_severity:
                by_severity[severity] = []
            by_severity[severity].append(violation)
        
        # Report by severity
        for severity in ["error", "warning", "info"]:
            if severity not in by_severity:
                continue
            
            severity_violations = by_severity[severity]
            emoji = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}[severity]
            report.append(f"{emoji} {severity.upper()} ({len(severity_violations)} issues)")
            report.append("-" * 30)
            
            for violation in severity_violations:
                file_path = violation["file"]
                line = violation["line"]
                message = violation["message"]
                
                report.append(f"  {file_path}:{line}")
                report.append(f"    {message}")
                
                if "suggestion" in violation:
                    report.append(f"    💡 {violation['suggestion']}")
                
                report.append("")
        
        # Summary
        total = len(violations)
        errors = len(by_severity.get("error", []))
        warnings = len(by_severity.get("warning", []))
        
        report.extend([
            "📊 Summary:",
            f"  Total issues: {total}",
            f"  Errors: {errors}",
            f"  Warnings: {warnings}",
            f"  Info: {total - errors - warnings}",
        ])
        
        return "\n".join(report)


def main():
    """Main entry point for the consolidation linter."""
    if len(sys.argv) < 2:
        print("Usage: python lint_consolidation.py <directory>")
        sys.exit(1)
    
    directory = Path(sys.argv[1])
    if not directory.exists():
        print(f"Error: Directory {directory} does not exist")
        sys.exit(1)
    
    linter = ConsolidationLinter(directory)
    violations = linter.lint_directory(directory)
    
    # Generate and print report
    report = linter.generate_report(violations)
    print(report)
    
    # Exit with appropriate code
    error_count = sum(1 for v in violations if v.get("severity") == "error")
    if error_count > 0:
        print(f"\n❌ {error_count} errors found - consolidation requirements not met")
        sys.exit(1)
    
    warning_count = sum(1 for v in violations if v.get("severity") == "warning")
    if warning_count > 0:
        print(f"\n⚠️ {warning_count} warnings found - consider addressing these issues")
    
    print("\n✅ Consolidation linting passed!")
    sys.exit(0)


if __name__ == "__main__":
    main()