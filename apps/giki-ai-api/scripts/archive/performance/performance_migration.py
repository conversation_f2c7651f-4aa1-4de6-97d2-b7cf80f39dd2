#!/usr/bin/env python3
"""
Performance Migration: Add Critical Database Indexes
Performance Issue Resolution for REQ-PERF-001

This script adds missing database indexes that were causing 4+ second query times:
1. categories.tenant_id index - critical for category queries
2. Verification of existing indexes
"""

import asyncio
import logging
import os
import sys

from sqlalchemy import text

# Add the source directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from giki_ai_api.database import get_database_config


async def run_performance_migration():
    """Run the performance optimization migration."""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    try:
        # Get database configuration
        get_database_config()

        # Get the engine from the database configuration
        from giki_ai_api.database import engine

        logger.info("Starting performance migration...")

        async with engine.begin() as conn:
            # Check if the index already exists
            check_index_query = """
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = 'category' 
            AND indexname = 'ix_category_tenant_id';
            """

            result = await conn.execute(text(check_index_query))
            existing_index = result.fetchone()

            if existing_index:
                logger.info(
                    "Index ix_category_tenant_id already exists - skipping creation"
                )
            else:
                # Create the index for categories.tenant_id (removed CONCURRENTLY for transaction compatibility)
                create_index_query = """
                CREATE INDEX IF NOT EXISTS ix_category_tenant_id 
                ON category (tenant_id);
                """

                logger.info("Creating index ix_category_tenant_id...")
                await conn.execute(text(create_index_query))
                logger.info("Index created successfully")

            # Verify critical indexes exist
            verify_indexes_query = """
            SELECT 
                schemaname,
                tablename,
                indexname,
                indexdef
            FROM pg_indexes 
            WHERE tablename IN ('category', 'user', 'transaction')
            AND indexname LIKE '%tenant_id%'
            ORDER BY tablename, indexname;
            """

            logger.info("Verifying critical indexes...")
            result = await conn.execute(text(verify_indexes_query))
            indexes = result.fetchall()

            for index in indexes:
                logger.info(f"✓ {index.tablename}.{index.indexname}")

            # Check for any missing critical indexes
            missing_checks = [
                ("user", "id", "Primary key should be indexed"),
                ("user", "email", "Email should be indexed"),
                ("category", "tenant_id", "Categories by tenant should be indexed"),
                (
                    "transaction",
                    "tenant_id",
                    "Transactions by tenant should be indexed",
                ),
            ]

            for table, column, description in missing_checks:
                check_query = f"""
                SELECT indexname 
                FROM pg_indexes 
                WHERE tablename = '{table}' 
                AND indexdef LIKE '%{column}%';
                """

                result = await conn.execute(text(check_query))
                indexes = result.fetchall()

                if indexes:
                    logger.info(
                        f"✓ {table}.{column} is indexed: {[idx.indexname for idx in indexes]}"
                    )
                else:
                    logger.warning(f"⚠ {table}.{column} is NOT indexed: {description}")

            await conn.commit()
            logger.info("Performance migration completed successfully")

    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(run_performance_migration())
