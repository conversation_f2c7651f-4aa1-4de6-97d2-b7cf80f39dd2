#!/usr/bin/env python3
"""
Create critical performance indexes for giki.ai database

This script creates the essential indexes needed to improve query performance
and reduce database timeouts.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

import asyncpg

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.giki_ai_api.core.config import settings

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def create_indexes(db_url: str):
    """Create performance-critical indexes."""
    
    # Convert SQLAlchemy-style URLs to asyncpg format
    if "postgresql+asyncpg://" in db_url:
        db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
    
    conn = await asyncpg.connect(db_url)
    logger.info("Connected to database")
    
    try:
        # List of indexes to create
        indexes = [
            # Critical for authentication queries
            {
                'name': 'idx_users_email',
                'sql': 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users (email) WHERE is_active = true'
            },
            {
                'name': 'idx_users_tenant_active',
                'sql': 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_tenant_active ON users (tenant_id, is_active)'
            },
            
            # Critical for transaction queries
            {
                'name': 'idx_transactions_tenant_status',
                'sql': 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_tenant_status ON transactions (tenant_id, status)'
            },
            {
                'name': 'idx_transactions_tenant_date',
                'sql': 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_tenant_date ON transactions (tenant_id, date DESC)'
            },
            {
                'name': 'idx_transactions_ai_confidence',
                'sql': 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_ai_confidence ON transactions (tenant_id, ai_confidence) WHERE ai_confidence IS NOT NULL'
            },
            {
                'name': 'idx_transactions_needs_review',
                'sql': 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_needs_review ON transactions (tenant_id, ai_confidence) WHERE status = \'needs_review\''
            },
            
            # Critical for category lookups
            {
                'name': 'idx_categories_tenant_name',
                'sql': 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_tenant_name ON categories (tenant_id, name)'
            },
            {
                'name': 'idx_categories_tenant_level',
                'sql': 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_tenant_level ON categories (tenant_id, level, parent_id)'
            },
            {
                'name': 'idx_categories_gl_code',
                'sql': 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_gl_code ON categories (tenant_id, gl_code) WHERE gl_code IS NOT NULL'
            },
            
            # Critical for file operations
            {
                'name': 'idx_uploaded_files_tenant_status',
                'sql': 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_uploaded_files_tenant_status ON uploaded_files (tenant_id, status)'
            },
            {
                'name': 'idx_uploaded_files_processing',
                'sql': 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_uploaded_files_processing ON uploaded_files (tenant_id, created_at DESC) WHERE status IN (\'processing\', \'pending\')'
            },
        ]
        
        # Create each index
        for index in indexes:
            try:
                logger.info(f"Creating index: {index['name']}")
                await conn.execute(index['sql'])
                logger.info(f"✅ Created index: {index['name']}")
            except asyncpg.exceptions.DuplicateObjectError:
                logger.info(f"Index already exists: {index['name']}")
            except Exception as e:
                logger.error(f"Failed to create index {index['name']}: {e}")
        
        # Analyze tables to update statistics
        logger.info("\nAnalyzing tables to update statistics...")
        tables = ['users', 'transactions', 'categories', 'uploaded_files', 'tenants']
        
        for table in tables:
            try:
                logger.info(f"Analyzing table: {table}")
                await conn.execute(f"ANALYZE {table}")
                logger.info(f"✅ Analyzed table: {table}")
            except Exception as e:
                logger.error(f"Failed to analyze table {table}: {e}")
        
        logger.info("\n✅ Database optimization complete!")
        
        # Display performance recommendations
        logger.info("\n" + "="*60)
        logger.info("PERFORMANCE RECOMMENDATIONS")
        logger.info("="*60)
        logger.info("""
1. REDIS CACHE (High Priority):
   - Install: brew install redis && brew services start redis
   - This will dramatically reduce database load for category lookups
   
2. CONNECTION POOL SETTINGS (Already Applied):
   - min_size: 10 → 30 connections
   - max_size: 30 → 50 connections  
   - command_timeout: 10s (increased from 3s)
   
3. MONITOR PERFORMANCE:
   - Check API logs for slow query warnings
   - Monitor AI error rate (should drop below 10%)
   - Watch for timeout errors

4. NEXT STEPS:
   - Enable Redis cache
   - Consider adding materialized views for reports
   - Monitor and tune based on usage patterns
""")
        
    finally:
        await conn.close()
        logger.info("Disconnected from database")


async def main():
    """Main execution function."""
    # Get database URL from environment or settings
    db_url = os.environ.get("DATABASE_URL") or settings.DATABASE_URL
    
    if not db_url:
        logger.error("DATABASE_URL not set. Please configure your environment.")
        sys.exit(1)
    
    await create_indexes(db_url)


if __name__ == "__main__":
    asyncio.run(main())