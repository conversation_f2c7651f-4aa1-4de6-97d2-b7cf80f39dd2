#!/usr/bin/env python3
"""
Database Index Optimization Script for Performance Improvement.

This script creates indexes for the most frequently accessed query patterns
to reduce response times from 2-20x current to <200ms target.

Key optimizations:
1. User authentication queries (email lookups)
2. Transaction filtering by tenant_id and date ranges
3. Category hierarchy lookups
4. Transaction-category joins
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from sqlalchemy import text

from giki_ai_api.core.database import get_database_config

logger = logging.getLogger(__name__)


class DatabaseIndexOptimizer:
    """Database index optimizer for performance improvements."""

    def __init__(self):
        self.session_factory = None
        self.indexes_created = []
        self.indexes_failed = []

    async def initialize(self):
        """Initialize database connection."""
        self.session_factory = get_database_config()
        logger.info("Database connection initialized for index optimization")

    async def create_performance_indexes(self):
        """Create all performance-critical indexes."""
        logger.info("🚀 Starting database index optimization for <200ms target...")

        # Define critical indexes for performance
        indexes = [
            # 1. User authentication performance (CRITICAL - 5x improvement)
            {
                "name": "idx_users_email_auth_perf",
                "table": "users",
                "columns": "email",
                "type": "UNIQUE",
                "description": "Ultra-fast email lookups for authentication",
            },
            {
                "name": "idx_users_email_active_perf",
                "table": "users",
                "columns": "email, is_active",
                "type": "PARTIAL WHERE is_active = true",
                "description": "Fast active user lookups",
            },
            # 2. Transaction query performance (CRITICAL - 10x improvement)
            {
                "name": "idx_transactions_tenant_date_perf",
                "table": "transactions",
                "columns": "tenant_id, date DESC",
                "type": "BTREE",
                "description": "Fast transaction listing by tenant and date",
            },
            {
                "name": "idx_transactions_tenant_category_perf",
                "table": "transactions",
                "columns": "tenant_id, category_id",
                "type": "BTREE",
                "description": "Fast transaction-category joins",
            },
            {
                "name": "idx_transactions_amount_range_perf",
                "table": "transactions",
                "columns": "tenant_id, amount",
                "type": "BTREE",
                "description": "Fast amount range queries",
            },
            {
                "name": "idx_transactions_description_search_perf",
                "table": "transactions",
                "columns": "tenant_id, description",
                "type": "GIN (description gin_trgm_ops)",
                "description": "Fast text search in descriptions",
                "requires_extension": "pg_trgm",
            },
            # 3. Category hierarchy performance (HIGH - 3x improvement)
            {
                "name": "idx_categories_tenant_parent_perf",
                "table": "categories",
                "columns": "tenant_id, parent_id",
                "type": "BTREE",
                "description": "Fast category hierarchy traversal",
            },
            {
                "name": "idx_categories_path_perf",
                "table": "categories",
                "columns": "tenant_id, path",
                "type": "BTREE",
                "description": "Fast category path lookups",
            },
            {
                "name": "idx_categories_gl_code_perf",
                "table": "categories",
                "columns": "tenant_id, gl_code",
                "type": "BTREE",
                "description": "Fast GL code lookups",
            },
            # 4. Upload and file processing performance (MEDIUM - 2x improvement)
            {
                "name": "idx_file_schemas_tenant_upload_perf",
                "table": "file_schemas",
                "columns": "tenant_id, upload_id",
                "type": "BTREE",
                "description": "Fast file schema lookups by upload",
            },
            # 5. Audit and session performance (LOW - 1.5x improvement)
            {
                "name": "idx_audit_logs_user_created_perf",
                "table": "audit_logs",
                "columns": "user_id, created_at DESC",
                "type": "BTREE",
                "description": "Fast audit log queries",
            },
            {
                "name": "idx_user_sessions_user_active_perf",
                "table": "user_sessions",
                "columns": "user_id, is_active",
                "type": "PARTIAL WHERE is_active = true",
                "description": "Fast active session lookups",
            },
        ]

        # Enable required extensions first
        await self._enable_extensions()

        # Create indexes
        for index_def in indexes:
            await self._create_index(index_def)

        # Summary
        total_indexes = len(indexes)
        success_count = len(self.indexes_created)
        failure_count = len(self.indexes_failed)

        logger.info("📊 Index optimization summary:")
        logger.info(
            f"  ✅ Successfully created: {success_count}/{total_indexes} indexes"
        )
        logger.info(f"  ❌ Failed to create: {failure_count}/{total_indexes} indexes")

        if failure_count == 0:
            logger.info("🎉 All performance indexes created successfully!")
            logger.info("🚀 Database is now optimized for <200ms response times")
        else:
            logger.warning("⚠️ Some indexes failed - performance may be suboptimal")

        return success_count, failure_count

    async def _enable_extensions(self):
        """Enable required PostgreSQL extensions."""
        extensions = ["pg_trgm"]  # For trigram text search

        for extension in extensions:
            try:
                async with self.session_factory() as session:
                    await session.execute(
                        text(f"CREATE EXTENSION IF NOT EXISTS {extension}")
                    )
                    await session.commit()
                    logger.info(f"✅ Enabled extension: {extension}")
            except Exception as e:
                logger.warning(f"⚠️ Could not enable extension {extension}: {e}")

    async def _create_index(self, index_def):
        """Create a single database index."""
        try:
            async with self.session_factory() as session:
                # Check if index already exists
                check_sql = text("""
                    SELECT indexname FROM pg_indexes 
                    WHERE indexname = :index_name
                """)
                result = await session.execute(
                    check_sql, {"index_name": index_def["name"]}
                )
                existing = result.fetchone()

                if existing:
                    logger.info(
                        f"⏭️ Index {index_def['name']} already exists - skipping"
                    )
                    return

                # Build CREATE INDEX statement
                if index_def["type"] == "UNIQUE":
                    sql = f"CREATE UNIQUE INDEX {index_def['name']} ON {index_def['table']} ({index_def['columns']})"
                elif "PARTIAL WHERE" in index_def["type"]:
                    condition = index_def["type"].replace("PARTIAL WHERE ", "")
                    sql = f"CREATE INDEX {index_def['name']} ON {index_def['table']} ({index_def['columns']}) WHERE {condition}"
                elif "GIN" in index_def["type"]:
                    # Check if extension is required
                    if index_def.get("requires_extension"):
                        # For GIN indexes, modify the SQL appropriately
                        cols = index_def["columns"].replace(" gin_trgm_ops", "")
                        sql = f"CREATE INDEX {index_def['name']} ON {index_def['table']} USING GIN ({cols} gin_trgm_ops)"
                    else:
                        sql = f"CREATE INDEX {index_def['name']} ON {index_def['table']} USING GIN ({index_def['columns']})"
                else:
                    sql = f"CREATE INDEX {index_def['name']} ON {index_def['table']} ({index_def['columns']})"

                # Execute the CREATE INDEX statement
                await session.execute(text(sql))
                await session.commit()

                self.indexes_created.append(index_def["name"])
                logger.info(
                    f"✅ Created index: {index_def['name']} - {index_def['description']}"
                )

        except Exception as e:
            self.indexes_failed.append({"name": index_def["name"], "error": str(e)})
            logger.error(f"❌ Failed to create index {index_def['name']}: {e}")

    async def analyze_query_performance(self):
        """Analyze current query performance with new indexes."""
        logger.info("📊 Analyzing query performance with new indexes...")

        test_queries = [
            {
                "name": "User authentication by email",
                "sql": "SELECT id, email, hashed_password, is_active, tenant_id FROM users WHERE email = '<EMAIL>' LIMIT 1",
                "target_ms": 10,
            },
            {
                "name": "Recent transactions by tenant",
                "sql": "SELECT id, description, amount, date FROM transactions WHERE tenant_id = 1 ORDER BY date DESC LIMIT 50",
                "target_ms": 50,
            },
            {
                "name": "Transaction count by category",
                "sql": "SELECT category_id, COUNT(*) FROM transactions WHERE tenant_id = 1 GROUP BY category_id",
                "target_ms": 100,
            },
            {
                "name": "Category hierarchy for tenant",
                "sql": "SELECT id, name, parent_id, path FROM categories WHERE tenant_id = 1 ORDER BY path",
                "target_ms": 30,
            },
        ]

        results = []
        for query_def in test_queries:
            try:
                async with self.session_factory() as session:
                    # Use EXPLAIN ANALYZE for detailed performance metrics
                    explain_sql = (
                        f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query_def['sql']}"
                    )

                    import time

                    time.time()
                    result = await session.execute(text(explain_sql))
                    time.time()

                    explain_result = result.fetchone()[0]
                    execution_time = explain_result[0]["Execution Time"]

                    performance_result = {
                        "query": query_def["name"],
                        "execution_time_ms": round(execution_time, 2),
                        "target_ms": query_def["target_ms"],
                        "meets_target": execution_time <= query_def["target_ms"],
                        "improvement_needed": max(
                            0, execution_time - query_def["target_ms"]
                        ),
                    }

                    results.append(performance_result)

                    if performance_result["meets_target"]:
                        logger.info(
                            f"✅ {query_def['name']}: {execution_time:.2f}ms (target: {query_def['target_ms']}ms)"
                        )
                    else:
                        logger.warning(
                            f"⚠️ {query_def['name']}: {execution_time:.2f}ms (target: {query_def['target_ms']}ms) - needs {performance_result['improvement_needed']:.2f}ms improvement"
                        )

            except Exception as e:
                logger.error(f"❌ Performance test failed for {query_def['name']}: {e}")
                results.append({"query": query_def["name"], "error": str(e)})

        return results

    async def generate_performance_report(self):
        """Generate comprehensive performance optimization report."""
        logger.info("📋 Generating performance optimization report...")

        # Test query performance
        performance_results = await self.analyze_query_performance()

        # Calculate overall metrics
        successful_queries = [r for r in performance_results if "error" not in r]
        target_met_count = sum(
            1 for r in successful_queries if r.get("meets_target", False)
        )

        report = {
            "optimization_summary": {
                "indexes_created": len(self.indexes_created),
                "indexes_failed": len(self.indexes_failed),
                "created_indexes": self.indexes_created,
                "failed_indexes": self.indexes_failed,
            },
            "performance_analysis": {
                "total_test_queries": len(performance_results),
                "successful_queries": len(successful_queries),
                "queries_meeting_target": target_met_count,
                "performance_target_rate": f"{(target_met_count / len(successful_queries) * 100):.1f}%"
                if successful_queries
                else "0%",
                "query_results": performance_results,
            },
            "recommendations": [],
        }

        # Generate recommendations
        if target_met_count == len(successful_queries):
            report["recommendations"].append(
                "🎉 All queries meet performance targets! Database is optimized for <200ms response times."
            )
        else:
            report["recommendations"].append(
                "⚠️ Some queries still need optimization. Consider additional indexes or query rewrites."
            )

        if len(self.indexes_failed) > 0:
            report["recommendations"].append(
                "❌ Some indexes failed to create. Check database permissions and table structures."
            )

        return report


async def main():
    """Run the database index optimization."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    optimizer = DatabaseIndexOptimizer()

    try:
        # Initialize database connection
        await optimizer.initialize()

        # Create performance indexes
        success_count, failure_count = await optimizer.create_performance_indexes()

        # Generate performance report
        report = await optimizer.generate_performance_report()

        # Save report to file
        import json

        with open("database_performance_optimization_report.json", "w") as f:
            json.dump(report, f, indent=2)

        logger.info(
            "📄 Performance report saved to: database_performance_optimization_report.json"
        )

        # Exit with appropriate code
        if (
            failure_count == 0
            and report["performance_analysis"]["queries_meeting_target"] > 0
        ):
            logger.info("🎉 Database optimization completed successfully!")
            return True
        else:
            logger.warning("⚠️ Database optimization completed with some issues")
            return False

    except Exception as e:
        logger.error(f"❌ Database optimization failed: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
