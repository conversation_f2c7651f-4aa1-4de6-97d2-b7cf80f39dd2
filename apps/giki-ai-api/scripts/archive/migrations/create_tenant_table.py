#!/usr/bin/env python3
"""
Create tenant table for multi-tenancy support.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from sqlalchemy import text

from giki_ai_api.core.database import get_database_config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_tenant_table():
    """Create tenant table and update test user."""

    # Get database session
    SessionLocal = get_database_config()

    async with SessionLocal() as session:
        try:
            # Check if tenant table exists
            check_query = text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'tenant'
                )
            """)

            result = await session.execute(check_query)
            table_exists = result.scalar()

            if not table_exists:
                logger.info("Creating tenant table...")

                # Create tenant table
                await session.execute(
                    text("""
                    CREATE TABLE tenant (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(255) NOT NULL,
                        domain VARCHAR(255) UNIQUE,
                        settings TEXT,
                        subscription_status VARCHAR(50) NOT NULL DEFAULT 'active',
                        subscription_plan VARCHAR(50) NOT NULL DEFAULT 'free',
                        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                )

                # Create index on domain
                await session.execute(
                    text("""
                    CREATE INDEX idx_tenant_domain ON tenant(domain)
                """)
                )

                logger.info("✅ Tenant table created")
            else:
                logger.info("Tenant table already exists")

            # Insert default tenants
            logger.info("Creating default tenants...")

            # Check if tenants exist
            check_tenants = text("SELECT id, name FROM tenant WHERE id IN (1, 2, 3)")
            existing_tenants = await session.execute(check_tenants)
            existing_ids = [row[0] for row in existing_tenants]

            # Insert Giki AI tenant if not exists
            if 1 not in existing_ids:
                await session.execute(
                    text("""
                    INSERT INTO tenant (id, name, domain, subscription_plan)
                    VALUES (1, 'Giki AI', 'giki.ai', 'premium')
                """)
                )
                logger.info("✅ Created Giki AI tenant")

            # Insert Rezolve AI tenant if not exists
            if 2 not in existing_ids:
                await session.execute(
                    text("""
                    INSERT INTO tenant (id, name, domain, subscription_plan)
                    VALUES (2, 'Rezolve AI', 'rezolve.ai', 'premium')
                """)
                )
                logger.info("✅ Created Rezolve AI tenant")

            # Insert Nuvie tenant if not exists
            if 3 not in existing_ids:
                await session.execute(
                    text("""
                    INSERT INTO tenant (id, name, domain, subscription_plan)
                    VALUES (3, 'Nuvie', 'nuvie.co.in', 'premium')
                """)
                )
                logger.info("✅ Created Nuvie tenant")

            # Update users to have correct tenant IDs if needed
            logger.info("Updating user tenant associations...")

            # Update <EMAIL>
            await session.execute(
                text("""
                UPDATE users 
                SET tenant_id = 1 
                WHERE email = '<EMAIL>' AND (tenant_id IS NULL OR tenant_id != 1)
            """)
            )

            # Update <EMAIL>
            await session.execute(
                text("""
                UPDATE users 
                SET tenant_id = 2 
                WHERE email = '<EMAIL>' AND (tenant_id IS NULL OR tenant_id != 2)
            """)
            )

            # Update <EMAIL>
            await session.execute(
                text("""
                UPDATE users 
                SET tenant_id = 3 
                WHERE email = '<EMAIL>' AND (tenant_id IS NULL OR tenant_id != 3)
            """)
            )

            # Commit changes
            await session.commit()
            logger.info("✅ Database updated successfully")

            # Verify the setup
            verify_query = text("""
                SELECT t.id, t.name, t.domain, COUNT(u.id) as user_count
                FROM tenant t
                LEFT JOIN users u ON u.tenant_id = t.id
                GROUP BY t.id, t.name, t.domain
                ORDER BY t.id
            """)

            result = await session.execute(verify_query)
            logger.info("\nTenant verification:")
            for row in result:
                logger.info(
                    f"  - Tenant {row[0]}: {row[1]} ({row[2]}) - {row[3]} users"
                )

        except Exception as e:
            logger.error(f"Failed to create tenant table: {e}")
            await session.rollback()
            raise


async def main():
    """Main function."""
    logger.info("Setting up tenant table...")

    try:
        await create_tenant_table()
        logger.info("✅ Tenant setup completed successfully")

    except Exception as e:
        logger.error(f"❌ Failed to set up tenants: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
