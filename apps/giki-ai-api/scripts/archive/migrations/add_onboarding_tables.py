#!/usr/bin/env python3
"""
Add onboarding completion tracking tables to the database.
This fixes the issue where customer onboarding data doesn't persist.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables to get table prefix
from dotenv import load_dotenv

load_dotenv(project_root / ".env.development")

from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

from giki_ai_api.config import settings

# Get table prefix from environment
TABLE_PREFIX = os.environ.get("TABLE_PREFIX", "")
print(f"[INFO] Using table prefix: '{TABLE_PREFIX}'")


def get_table_name(base_name: str) -> str:
    """Get environment-aware table name."""
    if TABLE_PREFIX:
        return f"{TABLE_PREFIX}{base_name}"
    return base_name


async def add_onboarding_tables():
    """Add onboarding completion tracking tables."""

    # Create async engine with asyncpg driver
    database_url = settings.DATABASE_URL.replace(
        "postgresql://", "postgresql+asyncpg://"
    )
    # Fix SSL parameter for asyncpg compatibility
    if "sslmode=require" in database_url:
        database_url = database_url.replace("sslmode=require", "ssl=require")

    print(f"[INFO] Connecting to database: {database_url.split('@')[0]}@***")
    engine = create_async_engine(database_url, echo=True)

    try:
        async with engine.begin() as conn:
            print("[INFO] Adding onboarding completion tracking tables...")

            # Get environment-aware table names
            onboarding_completions_table = get_table_name("onboarding_completion")
            onboarding_file_history_table = get_table_name("onboarding_file_history")
            tenants_table = get_table_name("tenant")
            users_table = get_table_name("user")

            print(
                f"   [INFO] Creating tables: {onboarding_completions_table}, {onboarding_file_history_table}"
            )

            # Create onboarding_completions table
            await conn.execute(
                text(
                    f"""
                CREATE TABLE IF NOT EXISTS {onboarding_completions_table} (
                    id SERIAL PRIMARY KEY,
                    tenant_id INTEGER NOT NULL REFERENCES {tenants_table}(id) ON DELETE CASCADE,
                    user_id INTEGER NOT NULL REFERENCES {users_table}(id) ON DELETE CASCADE,

                    -- Status tracking
                    status VARCHAR(50) NOT NULL DEFAULT 'not_started',
                    is_complete BOOLEAN NOT NULL DEFAULT FALSE,

                    -- Timestamps
                    started_at TIMESTAMP WITH TIME ZONE,
                    completed_at TIMESTAMP WITH TIME ZONE,
                    last_activity_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

                    -- File processing metrics
                    total_files_processed INTEGER NOT NULL DEFAULT 0,
                    successful_files INTEGER NOT NULL DEFAULT 0,
                    failed_files INTEGER NOT NULL DEFAULT 0,
                    processed_files JSON NOT NULL DEFAULT '[]',

                    -- Data metrics
                    total_transactions INTEGER NOT NULL DEFAULT 0,
                    total_categories INTEGER NOT NULL DEFAULT 0,
                    categories_created JSON NOT NULL DEFAULT '[]',

                    -- RAG corpus tracking
                    rag_corpus_name VARCHAR(255),
                    rag_corpus_created BOOLEAN NOT NULL DEFAULT FALSE,

                    -- Processing details
                    processing_time_seconds FLOAT,
                    error_message TEXT,
                    onboarding_details JSON,

                    -- Standard timestamps
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

                    -- Indexes
                    CONSTRAINT unique_tenant_user_onboarding UNIQUE(tenant_id, user_id)
                );
            """
                )
            )
            print(f"   [SUCCESS] Created {onboarding_completions_table} table")

            # Create onboarding_file_history table
            await conn.execute(
                text(
                    f"""
                CREATE TABLE IF NOT EXISTS {onboarding_file_history_table} (
                    id SERIAL PRIMARY KEY,
                    onboarding_completion_id INTEGER NOT NULL REFERENCES {onboarding_completions_table}(id) ON DELETE CASCADE,

                    -- File details
                    filename VARCHAR(255) NOT NULL,
                    upload_id VARCHAR(36),
                    file_size INTEGER,
                    content_type VARCHAR(100),

                    -- Processing results
                    status VARCHAR(50) NOT NULL,
                    transactions_count INTEGER NOT NULL DEFAULT 0,
                    categories_found JSON NOT NULL DEFAULT '[]',
                    sheets_processed INTEGER NOT NULL DEFAULT 0,

                    -- Error tracking
                    error_details TEXT,
                    processing_message TEXT,

                    -- Timestamp
                    processed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
                );
            """
                )
            )
            print(f"   [SUCCESS] Created {onboarding_file_history_table} table")

            # Create indexes for performance
            await conn.execute(
                text(
                    f"""
                CREATE INDEX IF NOT EXISTS idx_{onboarding_completions_table}_tenant_id
                ON {onboarding_completions_table}(tenant_id);
            """
                )
            )

            await conn.execute(
                text(
                    f"""
                CREATE INDEX IF NOT EXISTS idx_{onboarding_completions_table}_user_id
                ON {onboarding_completions_table}(user_id);
            """
                )
            )

            await conn.execute(
                text(
                    f"""
                CREATE INDEX IF NOT EXISTS idx_{onboarding_completions_table}_status
                ON {onboarding_completions_table}(status);
            """
                )
            )

            await conn.execute(
                text(
                    f"""
                CREATE INDEX IF NOT EXISTS idx_{onboarding_file_history_table}_completion_id
                ON {onboarding_file_history_table}(onboarding_completion_id);
            """
                )
            )

            print("   [SUCCESS] Created indexes")

            # Add constraint for status enum
            await conn.execute(
                text(
                    f"""
                ALTER TABLE {onboarding_completions_table}
                ADD CONSTRAINT check_onboarding_status
                CHECK (status IN ('not_started', 'in_progress', 'completed', 'failed'));
            """
                )
            )
            print("   [SUCCESS] Added status constraint")

            print(
                "\n[SUCCESS] Successfully added onboarding completion tracking tables!"
            )
            print("   - onboarding_completions: Tracks overall onboarding status")
            print("   - onboarding_file_history: Tracks individual file processing")
            print("   - Proper indexes and constraints added")
            print("   - Customer onboarding data will now persist!")

    except Exception as e:
        print(f"[ERROR] Error adding onboarding tables: {e}")
        raise
    finally:
        await engine.dispose()


if __name__ == "__main__":
    asyncio.run(add_onboarding_tables())
