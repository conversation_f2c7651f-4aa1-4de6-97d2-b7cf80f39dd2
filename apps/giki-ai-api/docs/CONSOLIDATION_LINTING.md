# Consolidation Architecture Linting

## Overview

The consolidation linting system enforces the unified architecture patterns implemented in the giki.ai API codebase. It prevents regressions back to old scattered patterns and ensures consistent use of the unified router, service, and middleware systems.

## Components

### 1. Custom Consolidation Linter (`scripts/lint_consolidation.py`)

A Python-based linter that checks for:
- **Deprecated router patterns**: Direct APIRouter usage instead of UnifiedBaseRouter
- **Legacy service patterns**: Non-unified service implementations
- **Scattered configuration**: Direct environment variable access
- **Old error handling**: Non-unified error handling patterns
- **Non-unified middleware**: Direct middleware registration

### 2. Pre-commit Hooks (`.pre-commit-config.yaml`)

Automated checks that run before commits:
- Ruff linting and formatting
- Custom consolidation linting
- Security scanning with Bandit
- Import sorting
- Standard code quality checks

### 3. GitHub Actions Workflow (`.github/workflows/consolidation-lint.yml`)

CI/CD integration that:
- Runs on pull requests to master/main branches
- Enforces consolidation patterns
- Generates architecture compliance reports
- Prevents merging of non-compliant code

### 4. pnpm Scripts

Convenient commands for running linting:
```bash
pnpm lint:consolidation       # Run consolidation linting
pnpm lint:consolidation:bg    # Run in background with detailed logs
```

### 5. Makefile Commands

Comprehensive development commands:
```bash
make lint-consolidation       # Run consolidation linting
make check-architecture       # Check unified architecture compliance
make check-patterns          # Check for deprecated patterns
make consolidation-report    # Generate status report
make quality-gate           # Run all quality checks
```

## Enforcement Rules

### Required Patterns

#### Routers
- **Must inherit from UnifiedBaseRouter**: `class SomeRouter(UnifiedBaseRouter)`
- **Must use RouterFactory**: Created via `RouterFactory.create_*_router()`
- **No direct APIRouter**: Avoid `router = APIRouter()`

#### Services
- **Must inherit from BaseService**: `class SomeService(BaseService)`
- **Use dependency injection**: Through UnifiedBaseRouter patterns
- **Standardized error handling**: Via UnifiedErrorHandler

#### Configuration
- **Use unified config system**: From `libs/config` or unified settings
- **Avoid scattered config**: No direct `os.getenv()` calls
- **Environment-aware**: Proper environment variable management

#### Middleware
- **Use UnifiedMiddleware**: Standardized middleware patterns
- **Avoid direct registration**: Use unified middleware system

### Deprecated Patterns

The linter will flag these as violations:

```python
# ❌ Deprecated - Direct APIRouter usage
from fastapi import APIRouter
router = APIRouter()

# ✅ Unified - UnifiedBaseRouter
from ...shared.routers.base_router import UnifiedBaseRouter
class SomeRouter(UnifiedBaseRouter):
    pass

# ❌ Deprecated - Direct config access
import os
database_url = os.getenv("DATABASE_URL")

# ✅ Unified - Unified configuration
from ...core.unified_config import settings
database_url = settings.DATABASE_URL

# ❌ Deprecated - Direct service creation
class SomeService:
    def __init__(self, conn):
        self.conn = conn

# ✅ Unified - BaseService inheritance
class SomeService(BaseService):
    pass
```

## Running Consolidation Linting

### Local Development

```bash
# Quick check
make lint-consolidation

# Full architecture check
make check-architecture

# Complete quality gate
make quality-gate

# Generate status report
make consolidation-report
```

### Background Execution

For long-running checks without Claude Code timeout:

```bash
# Background consolidation linting
pnpm lint:consolidation:bg

# Check logs
pnpm logs:lint
```

### Pre-commit Integration

Install pre-commit hooks:

```bash
cd apps/giki-ai-api
pre-commit install
```

Now consolidation linting runs automatically on every commit.

## Understanding Violations

### Violation Types

1. **Error** - Must be fixed before merging
   - Missing required patterns in new files
   - Critical architecture violations

2. **Warning** - Should be addressed
   - Deprecated pattern usage
   - Non-optimal implementations

3. **Info** - Good to know
   - Style suggestions
   - Best practice recommendations

### Example Output

```
🔍 Consolidation Linting Report
==============================

❌ ERROR (2 issues)
----------------------
  src/domains/new/router.py:15
    Missing required pattern 'unified_router_inheritance' for routers
    💡 Ensure routers follows unified patterns

  src/domains/new/service.py:10
    Deprecated pattern 'old_service_patterns' found: class NewService():
    💡 Inherit from BaseService for standardized patterns

⚠️ WARNING (1 issues)
-------------------------
  src/domains/legacy/helper.py:25
    Deprecated pattern 'scattered_config' found: os.getenv("DEBUG")
    💡 Use unified configuration system from libs/config

📊 Summary:
  Total issues: 3
  Errors: 2
  Warnings: 1
  Info: 0
```

## Configuration

### Exempt Files

Some files are exempt from consolidation rules:

```python
exempt_files = {
    "**/legacy/**",                    # Legacy compatibility files
    "**/domains/*/router.py",         # Original routers during transition
    "**/domains/*/secure_router.py",  # Original secure routers
    "**/core/main.py",                 # Main app file has special patterns
}
```

### Custom Rules

Add custom rules in `scripts/lint_consolidation.py`:

```python
# Add new deprecated pattern
self.deprecated_patterns["new_pattern"] = [
    r"some_old_pattern",
    r"another_deprecated_usage",
]

# Add new required pattern
self.required_patterns["routers"]["new_requirement"] = [
    r"required_import_pattern",
]
```

## Architecture Compliance Metrics

### Migration Progress

The system tracks migration progress:

- **Unified Routers**: Count of `*unified*router*.py` files
- **Legacy Routers**: Count of non-unified `router.py` files
- **Progress Percentage**: `unified / (unified + legacy) * 100`

### Compliance Targets

- **80%+**: Well-migrated codebase
- **50-79%**: Migration in progress
- **<50%**: Migration starting

### Current Status

Check current status:

```bash
make consolidation-report
```

Example output:
```
📊 Generating consolidation status report...

📈 Router Migration Status:
   Unified: 11
   Legacy:  3
   Total:   14
   Progress: 79%

🔄 Migration is progressing (79%)
```

## Integration with CI/CD

### GitHub Actions

The consolidation linting runs automatically on:
- Pull requests to master/main
- Pushes to master/main
- Changes to Python files

### Failure Conditions

CI fails if:
- Architecture compliance errors found
- Required unified patterns missing
- Deprecated patterns in new code

### Success Conditions

CI passes when:
- All consolidation rules satisfied
- Architecture compliance verified
- No critical violations found

## Troubleshooting

### Common Issues

1. **"Missing required pattern"**
   - Ensure new routers inherit from UnifiedBaseRouter
   - Check import statements

2. **"Deprecated pattern found"**
   - Update to use unified patterns
   - Check suggestions in violation messages

3. **"Architecture compliance failed"**
   - Verify RouterFactory usage in main.py
   - Ensure sufficient unified routers exist

### Debug Mode

Run with verbose output:

```bash
cd apps/giki-ai-api
python scripts/lint_consolidation.py src/ --verbose
```

### Skip Checks

For temporary exemptions (not recommended):

```python
# Add to exempt files in script
"path/to/file.py"  # Reason for exemption
```

## Best Practices

### For New Code

1. **Always use unified patterns**
   - Inherit from UnifiedBaseRouter for routers
   - Inherit from BaseService for services
   - Use unified configuration system

2. **Follow naming conventions**
   - `*_router.py` for unified routers
   - `*_service.py` for unified services

3. **Include proper imports**
   - Import from shared/routers for routers
   - Import from shared/services for services

### For Legacy Code

1. **Gradual migration**
   - Update files as you work on them
   - Don't break existing functionality

2. **Use exemptions sparingly**
   - Only for files that can't be migrated yet
   - Document reasons for exemptions

3. **Track progress**
   - Monitor consolidation reports
   - Set migration milestones

## Future Enhancements

### Planned Features

1. **Auto-fix capabilities**
   - Automatic conversion of simple patterns
   - Guided migration suggestions

2. **Metrics dashboard**
   - Visual progress tracking
   - Trend analysis

3. **Custom rule configuration**
   - Project-specific rules
   - Team-specific patterns

### Contributing

To contribute to consolidation linting:

1. **Add new rules** in `scripts/lint_consolidation.py`
2. **Update tests** for new patterns
3. **Document changes** in this file
4. **Test thoroughly** before deploying

## Support

For questions about consolidation linting:

1. **Check this documentation** first
2. **Review violation messages** for specific guidance
3. **Run `make help`** for available commands
4. **Check logs** in `logs/lint-consolidation.log`