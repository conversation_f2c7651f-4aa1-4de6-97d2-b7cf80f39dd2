# Makefile for giki.ai API
# Provides convenient commands for development, testing, and consolidation checking

.PHONY: help lint lint-consolidation lint-all format test test-fast check-architecture install dev clean

# Default target
help:
	@echo "🚀 giki.ai API Development Commands"
	@echo ""
	@echo "📋 Available targets:"
	@echo "  help              Show this help message"
	@echo "  install           Install dependencies"
	@echo "  dev               Start development server"
	@echo ""
	@echo "🔍 Linting & Code Quality:"
	@echo "  lint              Run standard ruff linting"
	@echo "  lint-consolidation Run consolidation architecture linting"
	@echo "  lint-all          Run all linting checks"
	@echo "  format            Format code with ruff"
	@echo ""
	@echo "🏗️ Architecture Checks:"
	@echo "  check-architecture Check unified architecture compliance"
	@echo "  check-patterns     Check for deprecated patterns"
	@echo "  consolidation-report Generate consolidation status report"
	@echo ""
	@echo "🧪 Testing:"
	@echo "  test              Run all tests"
	@echo "  test-fast         Run fast tests only"
	@echo "  test-unit         Run unit tests"
	@echo "  test-integration  Run integration tests"
	@echo ""
	@echo "🧹 Maintenance:"
	@echo "  clean             Clean build artifacts and cache"
	@echo "  pre-commit        Run pre-commit hooks"

# Installation
install:
	@echo "📦 Installing dependencies..."
	uv sync --dev
	pre-commit install

# Development
dev:
	@echo "🚀 Starting development server..."
	uvicorn src.giki_ai_api.core.main:app --reload --host 0.0.0.0 --port 8000

# Linting
lint:
	@echo "🔍 Running standard linting..."
	uv run ruff check src/

lint-consolidation:
	@echo "🏗️ Running consolidation architecture linting..."
	python scripts/lint_consolidation.py src/

lint-all: lint lint-consolidation
	@echo "✅ All linting checks completed"

format:
	@echo "🎨 Formatting code..."
	uv run ruff format src/

# Architecture checks
check-architecture:
	@echo "🏗️ Checking unified architecture compliance..."
	@echo ""
	@echo "🔍 Checking RouterFactory usage..."
	@if grep -q "RouterFactory" src/giki_ai_api/core/main.py; then \
		echo "✅ RouterFactory is being used"; \
	else \
		echo "❌ RouterFactory not found in main.py"; \
		exit 1; \
	fi
	@echo ""
	@echo "🔍 Checking unified routers..."
	@unified_count=$$(find src/ -name "*unified*router*.py" | wc -l); \
	echo "Found $$unified_count unified routers"; \
	if [ $$unified_count -ge 8 ]; then \
		echo "✅ Sufficient unified routers ($$unified_count/8+)"; \
	else \
		echo "⚠️ Need more unified routers ($$unified_count/8)"; \
	fi
	@echo ""
	@echo "🔍 Checking for base router system..."
	@if [ -f "src/giki_ai_api/shared/routers/base_router.py" ]; then \
		echo "✅ UnifiedBaseRouter system found"; \
	else \
		echo "❌ UnifiedBaseRouter system missing"; \
		exit 1; \
	fi
	@echo ""
	@echo "✅ Architecture compliance check completed"

check-patterns:
	@echo "🚫 Checking for deprecated patterns..."
	@echo ""
	@echo "🔍 Checking for direct APIRouter usage..."
	@if grep -r "router = APIRouter(" src/ --include="*.py" > /dev/null 2>&1; then \
		echo "❌ Found direct APIRouter usage:"; \
		grep -r "router = APIRouter(" src/ --include="*.py" || true; \
		echo "   → Use UnifiedBaseRouter instead"; \
		exit 1; \
	else \
		echo "✅ No direct APIRouter usage found"; \
	fi
	@echo ""
	@echo "🔍 Checking for scattered config usage..."
	@scattered_config=$$(grep -r "os\.getenv\|os\.environ" src/ --include="*.py" | grep -v "# LEGACY" | wc -l); \
	if [ $$scattered_config -gt 0 ]; then \
		echo "⚠️ Found $$scattered_config scattered config usages"; \
		echo "   → Consider using unified configuration"; \
	else \
		echo "✅ No scattered config usage found"; \
	fi
	@echo ""
	@echo "✅ Deprecated pattern check completed"

consolidation-report:
	@echo "📊 Generating consolidation status report..."
	@echo ""
	@unified_routers=$$(find src/ -name "*unified*router*.py" | wc -l); \
	legacy_routers=$$(find src/ -name "router.py" -not -path "*/unified*" | wc -l); \
	total_routers=$$((unified_routers + legacy_routers)); \
	if [ $$total_routers -gt 0 ]; then \
		progress=$$((unified_routers * 100 / total_routers)); \
	else \
		progress=0; \
	fi; \
	echo "📈 Router Migration Status:"; \
	echo "   Unified: $$unified_routers"; \
	echo "   Legacy:  $$legacy_routers"; \
	echo "   Total:   $$total_routers"; \
	echo "   Progress: $$progress%"; \
	echo ""; \
	if [ $$progress -ge 80 ]; then \
		echo "✅ Migration is well underway ($$progress%)"; \
	elif [ $$progress -ge 50 ]; then \
		echo "🔄 Migration is progressing ($$progress%)"; \
	else \
		echo "🚀 Migration is starting ($$progress%)"; \
	fi

# Testing
test:
	@echo "🧪 Running all tests..."
	uv run pytest tests/ -v

test-fast:
	@echo "⚡ Running fast tests..."
	uv run pytest tests/ -v -m "not slow"

test-unit:
	@echo "🔬 Running unit tests..."
	uv run pytest tests/unit/ -v

test-integration:
	@echo "🔗 Running integration tests..."
	uv run pytest tests/integration/ -v

# Maintenance
clean:
	@echo "🧹 Cleaning build artifacts..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".ruff_cache" -exec rm -rf {} + 2>/dev/null || true
	rm -f *.log bandit-report.json 2>/dev/null || true
	@echo "✅ Cleanup completed"

pre-commit:
	@echo "🪝 Running pre-commit hooks..."
	pre-commit run --all-files

# Quality gates
quality-gate: lint-all check-architecture check-patterns test-fast
	@echo ""
	@echo "🎯 Quality gate checks completed!"
	@echo "✅ All checks passed - ready for deployment"

# Development workflow
workflow: install format lint-all check-architecture test-fast
	@echo ""
	@echo "🎉 Development workflow completed successfully!"
	@echo "✅ Code is formatted, linted, architecture-compliant, and tested"