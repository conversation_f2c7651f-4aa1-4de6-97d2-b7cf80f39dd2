"""
Consolidation Validation Tests - Unified Router System
======================================================

Tests to validate the unified router architecture and ensure proper consolidation
of router patterns across all domains.
"""

import pytest
import importlib
import inspect
from pathlib import Path
from typing import List, Type

try:
    from fastapi import APIRouter
    from giki_ai_api.shared.routers.base_router import UnifiedBaseRouter, RouterFactory
except ImportError as e:
    pytest.skip(f"Required imports not available: {e}")
    APIRouter = None
    UnifiedBaseRouter = None
    RouterFactory = None


class TestUnifiedRouterSystem:
    """Test the unified router system implementation."""
    
    def test_router_factory_exists(self):
        """Test that RouterFactory is properly implemented."""
        assert hasattr(RouterFactory, 'create_auth_router')
        assert hasattr(RouterFactory, 'create_transactions_router')
        assert hasattr(RouterFactory, 'create_categories_router')
        assert hasattr(RouterFactory, 'create_dashboard_router')
        assert hasattr(RouterFactory, 'create_files_router')
        assert hasattr(RouterFactory, 'create_reports_router')
        assert hasattr(RouterFactory, 'create_accuracy_router')
        assert hasattr(RouterFactory, 'create_intelligence_router')
        assert hasattr(RouterFactory, 'create_admin_router')
        assert hasattr(RouterFactory, 'create_onboarding_router')
        assert hasattr(RouterFactory, 'create_system_router')
    
    def test_router_factory_methods_return_apirouters(self):
        """Test that RouterFactory methods return APIRouter instances."""
        factory_methods = [
            'create_auth_router',
            'create_transactions_router', 
            'create_categories_router',
            'create_dashboard_router',
            'create_files_router',
            'create_reports_router',
            'create_accuracy_router',
            'create_intelligence_router',
            'create_admin_router',
            'create_onboarding_router',
            'create_system_router',
        ]
        
        for method_name in factory_methods:
            method = getattr(RouterFactory, method_name)
            router = method()
            assert isinstance(router, APIRouter), f"{method_name} should return APIRouter instance"
    
    def test_unified_base_router_abstract_methods(self):
        """Test that UnifiedBaseRouter has required abstract methods."""
        assert hasattr(UnifiedBaseRouter, '_register_routes')
        assert hasattr(UnifiedBaseRouter, 'get_router')
        
        # Test that _register_routes is abstract
        with pytest.raises(TypeError):
            UnifiedBaseRouter({})  # Should fail due to abstract method
    
    def test_unified_routers_inherit_from_base(self):
        """Test that all unified routers inherit from UnifiedBaseRouter."""
        unified_router_files = [
            'giki_ai_api.domains.auth.unified_auth_router',
            'giki_ai_api.domains.transactions.unified_transactions_router', 
            'giki_ai_api.domains.categories.unified_categories_router',
            'giki_ai_api.domains.dashboard.unified_dashboard_router',
            'giki_ai_api.domains.files.unified_router',
            'giki_ai_api.domains.reports.unified_router',
            'giki_ai_api.domains.accuracy.unified_router',
            'giki_ai_api.domains.intelligence.unified_router',
            'giki_ai_api.domains.admin.unified_router',
            'giki_ai_api.domains.onboarding.unified_router',
            'giki_ai_api.domains.system.unified_router',
        ]
        
        for module_path in unified_router_files:
            try:
                module = importlib.import_module(module_path)
                
                # Find the router class in the module
                router_classes = [
                    obj for name, obj in inspect.getmembers(module)
                    if inspect.isclass(obj) and name.endswith('Router') and 'Unified' in name
                ]
                
                assert len(router_classes) >= 1, f"No unified router class found in {module_path}"
                
                for router_class in router_classes:
                    assert issubclass(router_class, UnifiedBaseRouter), \
                        f"{router_class.__name__} should inherit from UnifiedBaseRouter"
                        
            except ImportError as e:
                pytest.fail(f"Failed to import {module_path}: {e}")
    
    def test_router_configuration_consistency(self):
        """Test that router configurations are consistent."""
        factory_methods = [
            RouterFactory.create_auth_router,
            RouterFactory.create_transactions_router,
            RouterFactory.create_categories_router,
            RouterFactory.create_dashboard_router,
            RouterFactory.create_files_router,
            RouterFactory.create_reports_router,
            RouterFactory.create_accuracy_router,
            RouterFactory.create_intelligence_router,
            RouterFactory.create_admin_router,
            RouterFactory.create_onboarding_router,
            RouterFactory.create_system_router,
        ]
        
        for factory_method in factory_methods:
            router = factory_method()
            
            # Test that router has proper prefix
            assert router.prefix.startswith('/api/v1/'), \
                f"Router {factory_method.__name__} should have /api/v1/ prefix"
            
            # Test that router has tags
            assert len(router.tags) > 0, \
                f"Router {factory_method.__name__} should have tags"
    
    def test_no_direct_apirouter_usage_in_unified_routers(self):
        """Test that unified routers don't use direct APIRouter."""
        unified_router_paths = [
            'src/giki_ai_api/domains/auth/unified_auth_router.py',
            'src/giki_ai_api/domains/transactions/unified_transactions_router.py',
            'src/giki_ai_api/domains/categories/unified_categories_router.py',
            'src/giki_ai_api/domains/dashboard/unified_dashboard_router.py',
            'src/giki_ai_api/domains/files/unified_router.py',
            'src/giki_ai_api/domains/reports/unified_router.py',
            'src/giki_ai_api/domains/accuracy/unified_router.py',
            'src/giki_ai_api/domains/intelligence/unified_router.py',
            'src/giki_ai_api/domains/admin/unified_router.py',
            'src/giki_ai_api/domains/onboarding/unified_router.py',
            'src/giki_ai_api/domains/system/unified_router.py',
        ]
        
        base_path = Path(__file__).parent.parent.parent
        
        for router_path in unified_router_paths:
            file_path = base_path / router_path
            if file_path.exists():
                content = file_path.read_text()
                
                # Should not contain direct APIRouter instantiation
                assert 'router = APIRouter(' not in content, \
                    f"{router_path} should not use direct APIRouter instantiation"
                
                # Should inherit from UnifiedBaseRouter
                assert 'UnifiedBaseRouter' in content, \
                    f"{router_path} should inherit from UnifiedBaseRouter"


class TestRouterFactoryIntegration:
    """Test RouterFactory integration with FastAPI main app."""
    
    def test_main_app_uses_router_factory(self):
        """Test that main.py uses RouterFactory for router registration."""
        main_file = Path(__file__).parent.parent.parent / 'src/giki_ai_api/core/main.py'
        
        if main_file.exists():
            content = main_file.read_text()
            
            # Should import RouterFactory
            assert 'RouterFactory' in content, \
                "main.py should import RouterFactory"
            
            # Should use RouterFactory methods
            factory_usage_patterns = [
                'RouterFactory.create_auth_router',
                'RouterFactory.create_transactions_router',
                'RouterFactory.create_categories_router',
            ]
            
            found_patterns = sum(1 for pattern in factory_usage_patterns if pattern in content)
            assert found_patterns >= 2, \
                "main.py should use RouterFactory methods for router creation"
    
    def test_all_routers_registered_in_main(self):
        """Test that all unified routers are registered in main.py."""
        main_file = Path(__file__).parent.parent.parent / 'src/giki_ai_api/core/main.py'
        
        if main_file.exists():
            content = main_file.read_text()
            
            # Check for router registrations
            expected_registrations = [
                'auth_router',
                'transactions_router', 
                'categories_router',
                'dashboard_router',
            ]
            
            found_registrations = sum(1 for reg in expected_registrations if reg in content)
            assert found_registrations >= 3, \
                "main.py should register multiple unified routers"


class TestUnifiedRouterFeatures:
    """Test unified router features and functionality."""
    
    def test_unified_router_standard_methods(self):
        """Test that UnifiedBaseRouter provides standard route methods."""
        # Test that base router has standard helper methods
        assert hasattr(UnifiedBaseRouter, 'add_list_route')
        assert hasattr(UnifiedBaseRouter, 'add_get_route') 
        assert hasattr(UnifiedBaseRouter, 'add_create_route')
        assert hasattr(UnifiedBaseRouter, 'add_update_route')
        assert hasattr(UnifiedBaseRouter, 'add_delete_route')
    
    def test_unified_router_response_formatting(self):
        """Test that UnifiedBaseRouter provides response formatting."""
        assert hasattr(UnifiedBaseRouter, '_format_success_response')
        assert hasattr(UnifiedBaseRouter, '_format_paginated_response')
    
    def test_unified_router_error_handling(self):
        """Test that UnifiedBaseRouter provides error handling."""
        assert hasattr(UnifiedBaseRouter, '_handle_request')
        assert hasattr(UnifiedBaseRouter, '_validate_tenant_access')
    
    def test_router_config_validation(self):
        """Test router configuration validation."""
        from giki_ai_api.shared.routers.base_router import BaseRouterConfig
        
        # Test valid config
        config = BaseRouterConfig(
            prefix="/api/v1/test",
            tags=["Test"]
        )
        assert config.prefix == "/api/v1/test"
        assert config.tags == ["Test"]
        assert config.include_auth == True  # Default value
        
        # Test custom config
        config_custom = BaseRouterConfig(
            prefix="/api/v1/custom",
            tags=["Custom"],
            include_auth=False,
            include_caching=True,
            cache_ttl=600
        )
        assert config_custom.include_auth == False
        assert config_custom.include_caching == True
        assert config_custom.cache_ttl == 600


class TestConsolidationMigrationProgress:
    """Test consolidation migration progress and metrics."""
    
    def test_unified_router_count(self):
        """Test that we have sufficient unified routers."""
        base_path = Path(__file__).parent.parent.parent / 'src/giki_ai_api/domains'
        
        if base_path.exists():
            unified_router_files = list(base_path.rglob('*unified*router*.py'))
            
            # Should have at least 8 unified routers
            assert len(unified_router_files) >= 8, \
                f"Expected at least 8 unified routers, found {len(unified_router_files)}"
    
    def test_legacy_router_identification(self):
        """Test identification of remaining legacy routers."""
        base_path = Path(__file__).parent.parent.parent / 'src/giki_ai_api/domains'
        
        if base_path.exists():
            # Find legacy router files (non-unified router.py files)
            all_router_files = list(base_path.rglob('router.py'))
            legacy_routers = [
                f for f in all_router_files 
                if 'unified' not in str(f) and 'base_router' not in str(f)
            ]
            
            # Track legacy routers for migration planning
            legacy_count = len(legacy_routers)
            
            # This is informational - not a failure condition
            print(f"Legacy routers remaining: {legacy_count}")
            for router in legacy_routers:
                print(f"  - {router}")
    
    def test_consolidation_completion_metrics(self):
        """Test consolidation completion metrics."""
        base_path = Path(__file__).parent.parent.parent / 'src/giki_ai_api/domains'
        
        if base_path.exists():
            # Count unified routers
            unified_routers = list(base_path.rglob('*unified*router*.py'))
            
            # Count legacy routers
            all_router_files = list(base_path.rglob('router.py'))
            legacy_routers = [
                f for f in all_router_files 
                if 'unified' not in str(f) and 'base_router' not in str(f)
            ]
            
            total_routers = len(unified_routers) + len(legacy_routers)
            
            if total_routers > 0:
                completion_percentage = (len(unified_routers) / total_routers) * 100
                
                # Should have good migration progress
                assert completion_percentage >= 50, \
                    f"Router migration should be at least 50% complete, got {completion_percentage:.1f}%"
                
                print(f"Router migration progress: {completion_percentage:.1f}%")
                print(f"Unified routers: {len(unified_routers)}")
                print(f"Legacy routers: {len(legacy_routers)}")


class TestRouterSecurity:
    """Test security aspects of unified router system."""
    
    def test_router_authentication_defaults(self):
        """Test that routers have proper authentication defaults."""
        from giki_ai_api.shared.routers.base_router import BaseRouterConfig
        
        config = BaseRouterConfig(prefix="/api/v1/test", tags=["Test"])
        
        # Should include auth by default
        assert config.include_auth == True
        assert config.include_tenant_isolation == True
    
    def test_router_security_headers(self):
        """Test that routers provide security headers."""
        # Test that base router includes standard security responses
        from giki_ai_api.shared.routers.base_router import UnifiedBaseRouter, BaseRouterConfig
        
        class TestRouter(UnifiedBaseRouter):
            def _register_routes(self):
                pass
        
        config = BaseRouterConfig(prefix="/test", tags=["Test"])
        router = TestRouter(config)
        
        # Should have security response codes defined
        responses = router._get_standard_responses()
        assert 401 in responses  # Unauthorized
        assert 403 in responses  # Forbidden
        assert 500 in responses  # Internal Server Error


@pytest.mark.integration
class TestEndToEndRouterIntegration:
    """Integration tests for the complete router system."""
    
    def test_router_system_startup(self):
        """Test that the router system can start up properly."""
        # This would test the actual FastAPI app startup
        # For now, we test that all components are importable
        
        try:
            from giki_ai_api.core.main import app
            from giki_ai_api.shared.routers.base_router import RouterFactory
            
            # Test that app exists and has routes
            assert app is not None
            assert len(app.routes) > 0
            
        except ImportError as e:
            pytest.skip(f"Cannot import main app for integration test: {e}")
    
    def test_all_unified_routers_importable(self):
        """Test that all unified routers can be imported successfully."""
        router_modules = [
            'giki_ai_api.domains.auth.unified_auth_router',
            'giki_ai_api.domains.system.unified_router', 
            'giki_ai_api.domains.admin.unified_router',
            'giki_ai_api.domains.accuracy.unified_router',
            'giki_ai_api.domains.files.unified_router',
            'giki_ai_api.domains.reports.unified_router',
            'giki_ai_api.domains.onboarding.unified_router',
            'giki_ai_api.domains.intelligence.unified_router',
        ]
        
        successful_imports = 0
        for module_name in router_modules:
            try:
                importlib.import_module(module_name)
                successful_imports += 1
            except ImportError as e:
                print(f"Warning: Could not import {module_name}: {e}")
        
        # Should be able to import most unified routers
        assert successful_imports >= len(router_modules) * 0.7, \
            f"Should be able to import at least 70% of unified routers, got {successful_imports}/{len(router_modules)}"
    
    def test_router_performance_characteristics(self):
        """Test that routers have expected performance characteristics."""
        try:
            # Test RouterFactory performance
            import time
            start_time = time.time()
            
            # Create multiple routers to test factory performance
            router1 = RouterFactory.create_auth_router()
            router2 = RouterFactory.create_transactions_router()
            router3 = RouterFactory.create_categories_router()
            
            creation_time = time.time() - start_time
            
            # Router creation should be fast (< 1 second for 3 routers)
            assert creation_time < 1.0, f"Router creation too slow: {creation_time:.3f}s"
            
            # All routers should be different instances
            assert router1 is not router2
            assert router2 is not router3
            assert router1 is not router3
            
        except ImportError:
            pytest.skip("RouterFactory not available for performance testing")


@pytest.mark.consolidation
class TestUnifiedArchitectureCompliance:
    """Test compliance with unified architecture principles."""
    
    def test_base_router_interface_compliance(self):
        """Test that UnifiedBaseRouter follows expected interface."""
        # Test that base router has required methods
        required_methods = [
            '_register_routes',
            'get_router',
            'add_list_route',
            'add_get_route',
            'add_create_route',
            'add_update_route',
            'add_delete_route',
            '_format_success_response',
            '_format_paginated_response',
            '_handle_request',
            '_validate_tenant_access',
            '_get_standard_responses'
        ]
        
        for method_name in required_methods:
            assert hasattr(UnifiedBaseRouter, method_name), \
                f"UnifiedBaseRouter missing required method: {method_name}"
    
    def test_router_config_standardization(self):
        """Test that router configurations follow standards."""
        from giki_ai_api.shared.routers.base_router import BaseRouterConfig
        
        # Test config has required fields
        config = BaseRouterConfig(prefix="/test", tags=["Test"])
        
        required_config_fields = [
            'prefix', 'tags', 'include_auth', 'include_tenant_isolation',
            'include_caching', 'cache_ttl', 'include_performance_monitoring'
        ]
        
        for field in required_config_fields:
            assert hasattr(config, field), f"BaseRouterConfig missing field: {field}"
    
    def test_unified_response_format_consistency(self):
        """Test that all routers use consistent response formats."""
        from giki_ai_api.shared.routers.base_router import StandardResponse
        
        # Test StandardResponse structure
        response = StandardResponse(
            success=True,
            data={"test": "data"},
            message="Test message"
        )
        
        assert hasattr(response, 'success')
        assert hasattr(response, 'data')
        assert hasattr(response, 'message')
        assert response.success is True
        assert response.data == {"test": "data"}
        assert response.message == "Test message"
    
    def test_error_handling_standardization(self):
        """Test that error handling follows unified patterns."""
        # Test that standard error responses are defined
        from giki_ai_api.shared.routers.base_router import UnifiedBaseRouter, BaseRouterConfig
        
        class TestRouter(UnifiedBaseRouter):
            def _register_routes(self):
                pass
        
        config = BaseRouterConfig(prefix="/test", tags=["Test"])
        router = TestRouter(config)
        
        # Should have standard error responses
        responses = router._get_standard_responses()
        
        expected_error_codes = [401, 403, 404, 422, 500]  # 400 may not be in base responses
        for code in expected_error_codes:
            assert code in responses, f"Missing standard error response for {code}"
    
    def test_middleware_integration_compliance(self):
        """Test that routers properly integrate with middleware."""
        # Test that routers support middleware configuration
        from giki_ai_api.shared.routers.base_router import BaseRouterConfig
        
        config_with_performance = BaseRouterConfig(
            prefix="/test",
            tags=["Test"],
            include_performance_monitoring=True
        )
        
        config_with_caching = BaseRouterConfig(
            prefix="/test",
            tags=["Test"],
            include_caching=True,
            cache_ttl=300
        )
        
        # Configurations should be valid
        assert config_with_performance.include_performance_monitoring is True
        assert config_with_caching.include_caching is True
        assert config_with_caching.cache_ttl == 300


@pytest.mark.consolidation
class TestConsolidationLintingIntegration:
    """Test integration with consolidation linting system."""
    
    def test_linting_script_exists(self):
        """Test that the consolidation linting script exists and is executable."""
        linting_script = Path(__file__).parent.parent.parent / 'scripts/lint_consolidation.py'
        
        assert linting_script.exists(), "Consolidation linting script should exist"
        assert linting_script.is_file(), "Linting script should be a file"
        
        # Check that script has executable permissions (on Unix systems)
        import stat
        import os
        if os.name == 'posix':  # Unix-like systems
            file_stat = linting_script.stat()
            assert file_stat.st_mode & stat.S_IEXEC, "Linting script should be executable"
    
    def test_pre_commit_config_exists(self):
        """Test that pre-commit configuration exists."""
        pre_commit_config = Path(__file__).parent.parent.parent / '.pre-commit-config.yaml'
        
        if pre_commit_config.exists():
            # Check that it contains consolidation linting
            content = pre_commit_config.read_text()
            assert 'lint_consolidation' in content, "Pre-commit should include consolidation linting"
    
    def test_consolidation_documentation_exists(self):
        """Test that consolidation documentation exists."""
        docs_file = Path(__file__).parent.parent.parent / 'docs/CONSOLIDATION_LINTING.md'
        
        assert docs_file.exists(), "Consolidation documentation should exist"
        
        # Check that documentation contains key sections
        content = docs_file.read_text()
        required_sections = [
            "## Overview",
            "## Components", 
            "## Enforcement Rules",
            "## Running Consolidation Linting"
        ]
        
        for section in required_sections:
            assert section in content, f"Documentation should contain {section}"
    
    def test_makefile_targets_exist(self):
        """Test that Makefile contains consolidation targets."""
        makefile = Path(__file__).parent.parent.parent / 'Makefile'
        
        if makefile.exists():
            content = makefile.read_text()
            expected_targets = [
                'lint-consolidation',
                'check-architecture',
                'consolidation-report'
            ]
            
            for target in expected_targets:
                assert target in content, f"Makefile should contain target: {target}"


@pytest.mark.consolidation
class TestConsolidationMetrics:
    """Test consolidation progress metrics and tracking."""
    
    def test_unified_router_coverage(self):
        """Test unified router coverage metrics."""
        base_path = Path(__file__).parent.parent.parent / 'src/giki_ai_api/domains'
        
        if not base_path.exists():
            pytest.skip("Domains directory not found")
        
        # Count unified routers
        unified_routers = list(base_path.rglob('*unified*router*.py'))
        
        # Count total router files
        all_router_files = list(base_path.rglob('router.py'))
        all_router_files.extend(list(base_path.rglob('*_router.py')))
        
        # Remove duplicates and filter out non-router files
        unique_routers = set()
        for router_file in all_router_files:
            if 'base_router' not in str(router_file):
                unique_routers.add(router_file)
        
        total_routers = len(unified_routers) + len([r for r in unique_routers if 'unified' not in str(r)])
        
        if total_routers > 0:
            coverage_percent = (len(unified_routers) / total_routers) * 100
            
            # Log metrics for visibility
            print(f"\n📊 Router Consolidation Metrics:")
            print(f"   Unified routers: {len(unified_routers)}")
            print(f"   Total routers: {total_routers}")
            print(f"   Coverage: {coverage_percent:.1f}%")
            
            # Should have reasonable coverage
            assert coverage_percent >= 30, \
                f"Router consolidation coverage too low: {coverage_percent:.1f}%"
    
    def test_architecture_quality_score(self):
        """Test overall architecture quality score."""
        # This would integrate with the linting system to get quality metrics
        base_path = Path(__file__).parent.parent.parent / 'src'
        
        if not base_path.exists():
            pytest.skip("Source directory not found")
        
        # Mock quality metrics (in real implementation, this would run the linter)
        quality_metrics = {
            "unified_router_usage": 85,  # % of routers using unified patterns
            "deprecated_pattern_count": 15,  # Number of deprecated patterns found
            "architecture_compliance": 78,  # % compliance with unified architecture
            "code_quality_score": 92,  # Overall code quality
        }
        
        # Calculate overall quality score
        overall_score = (
            quality_metrics["unified_router_usage"] * 0.3 +
            (100 - quality_metrics["deprecated_pattern_count"]) * 0.2 +
            quality_metrics["architecture_compliance"] * 0.3 +
            quality_metrics["code_quality_score"] * 0.2
        )
        
        print(f"\n📈 Architecture Quality Score: {overall_score:.1f}")
        
        # Should meet minimum quality threshold
        assert overall_score >= 70, f"Architecture quality score too low: {overall_score:.1f}"
    
    def test_consolidation_trend_tracking(self):
        """Test that consolidation progress can be tracked over time."""
        # This test validates that we can track consolidation progress
        # In a real implementation, this would store/retrieve historical data
        
        consolidation_history = [
            {"date": "2024-01-01", "unified_routers": 5, "total_routers": 15, "coverage": 33.3},
            {"date": "2024-01-15", "unified_routers": 8, "total_routers": 15, "coverage": 53.3},
            {"date": "2024-02-01", "unified_routers": 11, "total_routers": 15, "coverage": 73.3},
            {"date": "2024-02-15", "unified_routers": 13, "total_routers": 15, "coverage": 86.7},
        ]
        
        # Verify positive trend
        coverages = [entry["coverage"] for entry in consolidation_history]
        for i in range(1, len(coverages)):
            assert coverages[i] >= coverages[i-1], \
                f"Consolidation coverage should not decrease: {coverages[i-1]} -> {coverages[i]}"
        
        # Verify reasonable progress rate
        initial_coverage = coverages[0]
        final_coverage = coverages[-1]
        improvement = final_coverage - initial_coverage
        
        assert improvement >= 30, \
            f"Consolidation should show significant improvement: {improvement:.1f}%"


@pytest.mark.consolidation
@pytest.mark.integration
class TestConsolidationEndToEnd:
    """End-to-end tests for the complete consolidation system."""
    
    def test_full_router_system_integration(self):
        """Test that the complete router system works end-to-end."""
        try:
            # Test that we can create all routers
            routers = {}
            
            router_methods = [
                'create_auth_router',
                'create_transactions_router',
                'create_categories_router',
                'create_dashboard_router',
                'create_files_router',
                'create_reports_router',
                'create_accuracy_router',
                'create_intelligence_router',
                'create_admin_router',
                'create_onboarding_router',
                'create_system_router',
            ]
            
            for method_name in router_methods:
                if hasattr(RouterFactory, method_name):
                    method = getattr(RouterFactory, method_name)
                    router = method()
                    routers[method_name] = router
            
            # Should have created multiple routers
            assert len(routers) >= 5, f"Should create multiple routers, got {len(routers)}"
            
            # All routers should be unique instances
            router_instances = list(routers.values())
            for i, router1 in enumerate(router_instances):
                for j, router2 in enumerate(router_instances):
                    if i != j:
                        assert router1 is not router2, "Routers should be unique instances"
            
            # All routers should have proper configuration
            for router_name, router in routers.items():
                assert hasattr(router, 'prefix'), f"{router_name} should have prefix"
                assert hasattr(router, 'tags'), f"{router_name} should have tags"
                assert router.prefix.startswith('/api/v1/'), \
                    f"{router_name} should have proper API prefix: {router.prefix}"
            
        except ImportError as e:
            pytest.skip(f"Router system not fully available: {e}")
    
    def test_consolidation_linting_execution(self):
        """Test that consolidation linting can be executed."""
        # Test that the linting script can be imported and run
        linting_script_path = Path(__file__).parent.parent.parent / 'scripts'
        
        if not linting_script_path.exists():
            pytest.skip("Scripts directory not found")
        
        # Add scripts directory to Python path for import
        import sys
        sys.path.insert(0, str(linting_script_path))
        
        try:
            # Import the linting module
            import lint_consolidation
            
            # Test that ConsolidationLinter can be instantiated
            test_path = Path(__file__).parent.parent.parent / 'src'
            if test_path.exists():
                linter = lint_consolidation.ConsolidationLinter(test_path)
                
                # Should have expected attributes
                assert hasattr(linter, 'deprecated_patterns')
                assert hasattr(linter, 'required_patterns')
                assert hasattr(linter, 'exempt_files')
                
                # Should be able to check a file (without actually running on real files)
                assert hasattr(linter, 'check_file')
                assert hasattr(linter, 'lint_directory')
                assert hasattr(linter, 'generate_report')
            
        except ImportError:
            pytest.skip("Consolidation linting module not available")
        finally:
            # Clean up Python path
            if str(linting_script_path) in sys.path:
                sys.path.remove(str(linting_script_path))
    
    def test_main_app_integration_with_unified_routers(self):
        """Test that the main FastAPI app properly integrates unified routers."""
        try:
            from giki_ai_api.core.main import app
            
            # App should exist
            assert app is not None, "FastAPI app should exist"
            
            # Should have routes
            assert len(app.routes) > 0, "App should have routes registered"
            
            # Should have API routes
            api_routes = [route for route in app.routes if hasattr(route, 'path') and route.path.startswith('/api/v1/')]
            assert len(api_routes) > 0, "App should have API routes"
            
            # Should have routes from multiple domains
            route_prefixes = set()
            for route in api_routes:
                if hasattr(route, 'path'):
                    # Extract the domain from the path (e.g., /api/v1/auth -> auth)
                    path_parts = route.path.split('/')
                    if len(path_parts) >= 4:  # ['', 'api', 'v1', 'domain', ...]
                        domain = path_parts[3]
                        route_prefixes.add(domain)
            
            # Should have routes from multiple domains
            assert len(route_prefixes) >= 3, \
                f"App should have routes from multiple domains, got: {route_prefixes}"
            
        except ImportError as e:
            pytest.skip(f"Main app not available for integration testing: {e}")


# Pytest configuration and marks
def pytest_configure(config):
    """Configure pytest with custom marks."""
    config.addinivalue_line(
        "markers", "consolidation: mark test as a consolidation validation test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )