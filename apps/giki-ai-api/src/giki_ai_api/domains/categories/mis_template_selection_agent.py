"""
MIS Template Selection Agent
============================

AI-powered agent that intelligently selects the most appropriate
industry-specific MIS template during customer onboarding based
on business context analysis.

This agent:
- Analyzes company website and business description
- Uses Google Search for industry intelligence
- Matches business patterns to industry templates
- Provides confidence scoring for template selection
- Suggests customizations based on specific business needs
"""

import json
import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

from vertexai.generative_models import GenerativeModel

from ...shared.ai.prompt_registry import get_prompt_registry
from ...shared.ai.standard_giki_agent import StandardGikiAgent, create_enhanced_function_tool

# Removed Google Search dependency - using enhanced pattern analysis instead
from .mis_templates import MISTemplates

logger = logging.getLogger(__name__)


@dataclass
class TemplateSelectionResult:
    """Result from template selection analysis."""

    industry: str
    confidence: float
    reasoning: str
    suggested_customizations: List[str]
    business_signals: Dict[str, Any]
    alternative_industries: List[Tuple[str, float]]  # (industry, confidence)


class MISTemplateSelectionAgent(StandardGikiAgent):
    """
    Agent for intelligent MIS template selection during onboarding.

    Uses AI to analyze business context and select the most appropriate
    industry template for Income/Expense categorization structure.
    
    Inherits from StandardGikiAgent for ADK v1.3.0 compliance.
    """

    def __init__(self, tenant_id: int):
        # Store tenant context
        self.tenant_id = tenant_id
        
        # Create function tools for MIS template selection capabilities
        custom_tools = [
            create_enhanced_function_tool(
                func=self.analyze_business_context,
                name="analyze_business_context",
                description="Analyze business context and select appropriate MIS template",
                category="template_selection"
            ),
            create_enhanced_function_tool(
                func=self.validate_template_fit,
                name="validate_template_fit", 
                description="Validate how well a selected template fits the business",
                category="template_selection"
            )
        ]

        # Initialize StandardGikiAgent with template selection tools
        super().__init__(
            name="giki-ai-mis-template-selection-agent",
            description="AI-powered MIS template selection agent for intelligent industry categorization",
            custom_tools=custom_tools,
            enable_code_execution=False,  # Not needed for template selection
            enable_standard_tools=True,  # Enable google_search for business intelligence
            standard_tool_set=["google_search"],  # Only need Google Search
            model_name="gemini-2.0-flash-001"
        )
        
        # Initialize legacy model for backward compatibility
        self.model = GenerativeModel("gemini-2.0-flash-001")

    async def analyze_business_context(
        self,
        company_name: str,
        company_website: Optional[str] = None,
        business_description: Optional[str] = None,
        company_size: Optional[str] = None,
        existing_categories: Optional[List[str]] = None,
        sample_transactions: Optional[List[Dict[str, Any]]] = None,
    ) -> TemplateSelectionResult:
        """
        Analyze business context and select appropriate MIS template.

        Args:
            company_name: Name of the company
            company_website: Company website URL for analysis
            business_description: User-provided business description
            company_size: Size category (Small, Medium, Large, Enterprise)
            existing_categories: Any existing category names from uploads
            sample_transactions: Sample transaction descriptions for pattern analysis

        Returns:
            TemplateSelectionResult with industry selection and confidence
        """
        try:
            # Gather business intelligence
            business_signals = await self._gather_business_intelligence(
                company_name=company_name,
                company_website=company_website,
                business_description=business_description,
            )

            # Analyze transaction patterns if provided
            if sample_transactions:
                transaction_patterns = self._analyze_transaction_patterns(
                    sample_transactions
                )
                business_signals["transaction_patterns"] = transaction_patterns

            # Get all available industries
            available_industries = MISTemplates.get_all_industries()

            # Use AI to select best industry match
            selection_result = await self._ai_select_industry(
                business_signals=business_signals,
                company_size=company_size,
                existing_categories=existing_categories,
                available_industries=available_industries,
            )

            return selection_result

        except Exception as e:
            logger.error(f"Template selection failed: {e}")
            # Return safe default
            return TemplateSelectionResult(
                industry="General Business",
                confidence=0.3,
                reasoning="Default template selected due to analysis error",
                suggested_customizations=[],
                business_signals={},
                alternative_industries=[],
            )

    async def _gather_business_intelligence(
        self,
        company_name: str,
        company_website: Optional[str],
        business_description: Optional[str],
    ) -> Dict[str, Any]:
        """Gather intelligence about the business from various sources."""
        signals = {
            "company_name": company_name,
            "website": company_website,
            "description": business_description,
        }

        # Analyze website domain patterns for industry signals
        if company_website:
            try:
                domain_signals = self._analyze_website_domain(company_website, company_name)
                if domain_signals:
                    signals["domain_intelligence"] = domain_signals
                    
            except Exception as e:
                logger.warning(f"Domain intelligence gathering failed: {e}")

        return signals

    def _analyze_website_domain(self, website: str, company_name: str) -> Dict[str, Any]:
        """
        Analyze website domain and company name for industry signals.
        
        This replaces the Google Search stub with domain pattern analysis.
        """
        domain_signals = {}
        
        # Extract domain from URL
        domain = website.lower()
        if "://" in domain:
            domain = domain.split("://")[1]
        if "/" in domain:
            domain = domain.split("/")[0]
        
        # Industry indicators from domain patterns
        domain_indicators = {
            "technology": [
                "tech", "soft", "app", "cloud", "dev", "api", "code", "data",
                "ai", "ml", "saas", "platform", "digital", "cyber", "web"
            ],
            "healthcare": [
                "health", "medical", "care", "clinic", "hospital", "pharma",
                "med", "wellness", "therapy", "dental", "bio", "life"
            ],
            "financial_services": [
                "bank", "finance", "invest", "capital", "fund", "wealth",
                "insurance", "loan", "credit", "pay", "fin"
            ],
            "retail": [
                "shop", "store", "retail", "market", "trade", "supply",
                "commerce", "sale", "buy", "mart", "outlet"
            ],
            "professional_services": [
                "consult", "service", "solution", "legal", "law", "advise",
                "management", "strategy", "firm", "group", "partner"
            ],
            "hospitality": [
                "hotel", "restaurant", "food", "cafe", "bar", "travel",
                "resort", "hospitality", "catering", "dining"
            ],
            "manufacturing": [
                "mfg", "manufacturing", "industrial", "factory", "production",
                "supply", "materials", "equipment", "machinery"
            ]
        }
        
        # Check domain for industry keywords
        detected_industries = []
        combined_text = f"{domain} {company_name.lower()}"
        
        for industry, keywords in domain_indicators.items():
            matches = [keyword for keyword in keywords if keyword in combined_text]
            if matches:
                detected_industries.append({
                    "industry": industry,
                    "matches": matches,
                    "confidence": min(len(matches) * 0.3, 0.9)
                })
        
        if detected_industries:
            # Sort by confidence and return top matches
            detected_industries.sort(key=lambda x: x["confidence"], reverse=True)
            domain_signals["industry_matches"] = detected_industries[:3]
            domain_signals["primary_industry"] = detected_industries[0]["industry"]
        
        # Domain extension analysis
        if domain.endswith(".gov"):
            domain_signals["sector"] = "government"
        elif domain.endswith(".edu"):
            domain_signals["sector"] = "education"
        elif domain.endswith(".org"):
            domain_signals["sector"] = "nonprofit"
        else:
            domain_signals["sector"] = "commercial"
            
        return domain_signals

    def _analyze_transaction_patterns(
        self, sample_transactions: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze sample transactions to identify business patterns."""
        patterns = {
            "vendor_types": [],
            "expense_patterns": [],
            "revenue_indicators": [],
            "industry_keywords": [],
        }

        # Keywords that indicate specific industries
        industry_indicators = {
            "technology": [
                "aws",
                "google cloud",
                "azure",
                "software",
                "saas",
                "api",
                "hosting",
                "github",
                "atlassian",
                "slack",
                "zoom",
            ],
            "retail": [
                "inventory",
                "wholesale",
                "supplier",
                "merchandise",
                "pos",
                "square",
                "shopify",
                "shipping",
                "packaging",
            ],
            "healthcare": [
                "medical",
                "patient",
                "insurance",
                "pharmacy",
                "lab",
                "diagnostic",
                "clinical",
                "health",
            ],
            "hospitality": [
                "booking",
                "guest",
                "reservation",
                "food service",
                "catering",
                "restaurant",
                "hotel",
                "bar",
            ],
            "professional_services": [
                "consulting",
                "client",
                "project",
                "billable",
                "retainer",
                "contract",
                "legal",
                "accounting",
            ],
        }

        for txn in sample_transactions[:20]:  # Analyze first 20 transactions
            desc = txn.get("description", "").lower()
            amount = float(txn.get("amount", 0))

            # Check for revenue patterns
            if amount > 0:
                patterns["revenue_indicators"].append(desc)

            # Check for industry keywords
            for industry, keywords in industry_indicators.items():
                if any(keyword in desc for keyword in keywords):
                    patterns["industry_keywords"].append(industry)

        # Summarize patterns
        if patterns["industry_keywords"]:
            from collections import Counter

            keyword_counts = Counter(patterns["industry_keywords"])
            patterns["dominant_industry"] = keyword_counts.most_common(1)[0][0]

        return patterns

    async def _ai_select_industry(
        self,
        business_signals: Dict[str, Any],
        company_size: Optional[str],
        existing_categories: Optional[List[str]],
        available_industries: List[str],
    ) -> TemplateSelectionResult:
        """Use AI to select the best industry template."""

        # Prepare context for AI
        context = {
            "business_signals": business_signals,
            "company_size": company_size,
            "existing_categories": existing_categories[:20]
            if existing_categories
            else [],
            "available_industries": available_industries,
        }

        # Get prompt from registry
        prompt_registry = get_prompt_registry()
        prompt_version = prompt_registry.get("mis_template_selection")
        prompt = prompt_version.format(
            business_context=json.dumps(context, indent=2)
        )

        try:
            response = await self.model.generate_content_async(
                prompt,
                generation_config=prompt_version.model_config,
            )

            # Parse AI response
            result_text = response.text.strip()
            if result_text.startswith("```json"):
                result_text = result_text[7:-3]

            result_data = json.loads(result_text)

            # Validate industry selection
            selected_industry = result_data.get("industry", "General Business")
            if selected_industry not in available_industries:
                # Find closest match
                selected_industry = self._find_closest_industry(
                    selected_industry, available_industries
                )

            # Build result
            alternatives = [
                (alt["industry"], alt["confidence"])
                for alt in result_data.get("alternatives", [])
            ]

            return TemplateSelectionResult(
                industry=selected_industry,
                confidence=float(result_data.get("confidence", 0.7)),
                reasoning=result_data.get("reasoning", "AI-based industry analysis"),
                suggested_customizations=result_data.get("customizations", []),
                business_signals=business_signals,
                alternative_industries=alternatives,
            )

        except Exception as e:
            logger.error(f"AI industry selection failed: {e}")

            # Fallback logic based on patterns
            fallback_industry = "General Business"
            if business_signals.get("transaction_patterns", {}).get(
                "dominant_industry"
            ):
                fallback_industry = self._map_to_template_industry(
                    business_signals["transaction_patterns"]["dominant_industry"]
                )

            return TemplateSelectionResult(
                industry=fallback_industry,
                confidence=0.5,
                reasoning="Pattern-based fallback selection",
                suggested_customizations=[],
                business_signals=business_signals,
                alternative_industries=[],
            )

    def _find_closest_industry(self, suggested: str, available: List[str]) -> str:
        """Find closest matching industry from available options."""
        suggested_lower = suggested.lower()

        # Direct match
        for industry in available:
            if industry.lower() == suggested_lower:
                return industry

        # Partial match
        for industry in available:
            if (
                suggested_lower in industry.lower()
                or industry.lower() in suggested_lower
            ):
                return industry

        # Default
        return "General Business"

    def _map_to_template_industry(self, pattern_industry: str) -> str:
        """Map detected pattern to template industry."""
        mapping = {
            "technology": "Technology",
            "retail": "Retail",
            "healthcare": "Healthcare",
            "hospitality": "Hospitality",
            "professional_services": "Professional Services",
        }
        return mapping.get(pattern_industry, "General Business")

    async def validate_template_fit(
        self,
        tenant_id: int,
        selected_industry: str,
        actual_transactions: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Validate how well the selected template fits actual transaction data.

        This can be used post-selection to verify the template choice.
        """
        template = MISTemplates.get_industry_template(selected_industry)

        # Count how many transactions match template categories
        matches = 0
        mismatches = []

        # Get all category names from template
        template_categories = set()
        for section in ["income", "expenses"]:
            for cat in template[section]["subcategories"]:
                template_categories.add(cat["name"].lower())
                if "subcategories" in cat:
                    for subcat in cat["subcategories"]:
                        template_categories.add(subcat["name"].lower())

        # Check transaction fit
        for txn in actual_transactions[:100]:  # Sample first 100
            desc = txn.get("description", "").lower()
            category = txn.get("category", "").lower()

            # Simple keyword matching for validation
            found_match = any(
                cat_name in desc or cat_name in category
                for cat_name in template_categories
            )

            if found_match:
                matches += 1
            else:
                mismatches.append(desc[:50])

        fit_score = (
            matches / len(actual_transactions[:100]) if actual_transactions else 0
        )

        return {
            "industry": selected_industry,
            "fit_score": fit_score,
            "matches": matches,
            "sample_size": len(actual_transactions[:100]),
            "mismatches_sample": mismatches[:5],
            "recommendation": "Good fit"
            if fit_score > 0.6
            else "Consider customization",
        }
