"""
Authentication domain package.

Exports all authentication-related components.
"""

from .models import Tenant, User
from .schemas import (
    ActiveSessionsResponse,
    AuthenticationStats,
    AuthMetrics,
    BulkUserOperation,
    EmailVerificationConfirm,
    EmailVerificationRequest,
    LoginRequest,
    LoginResponse,
    OptimizedUserResponse,
    PasswordResetConfirm,
    PasswordResetRequest,
    PermissionCheck,
    PermissionResponse,
    SessionInfo,
    TenantUserAssignment,
    Token,
    TokenData,
    TokenRefreshRequest,
    TokenRefreshResponse,
    UserBase,
    UserCreate,
    UserListResponse,
    UserPreferencesUpdate,
    UserProfile,
    UserResponse,
    UserUpdate,
)
from .secure_auth import (
    authenticate_user_with_db,
    create_access_token,
    get_current_active_user,
    get_password_hash,
    verify_password,
)
from .unified_auth_router import router

# from .service import AuthService  # Temporarily disabled - using secure_auth instead

__all__ = [
    # Authentication functions
    "get_current_active_user",
    "get_password_hash",
    "verify_password",
    "create_access_token",
    "authenticate_user_with_db",
    # Schemas
    "Token",
    "UserBase",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "OptimizedUserResponse",
    "LoginRequest",
    "LoginResponse",
    "TokenData",
    "TokenRefreshRequest",
    "TokenRefreshResponse",
    "PasswordResetRequest",
    "PasswordResetConfirm",
    "EmailVerificationRequest",
    "EmailVerificationConfirm",
    "UserListResponse",
    "AuthMetrics",
    "UserProfile",
    "UserPreferencesUpdate",
    "TenantUserAssignment",
    "BulkUserOperation",
    "AuthenticationStats",
    "PermissionCheck",
    "PermissionResponse",
    "SessionInfo",
    "ActiveSessionsResponse",
    # Service and models
    # "AuthService",  # Temporarily disabled
    "User",
    "Tenant",
    # Router
    "router",
]
