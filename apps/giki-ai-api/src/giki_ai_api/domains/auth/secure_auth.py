"""
Secure Authentication Module for Giki AI API

This module provides secure JWT-based authentication WITHOUT dangerous optimizations.
Every request validates against the database to ensure current user state.

Security principles:
1. ALWAYS verify user exists and is active in database
2. NEVER cache authentication results or user data
3. NEVER trust JWT content without database verification
4. ALWAYS check current permissions on every request
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta, timezone
from typing import Annotated, Optional

from asyncpg import Connection
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from passlib.context import CryptContext

from ...core.unified_config import settings
from ...core.database import get_db_session
from ...core.jwt_keys import get_private_key, get_public_key
from .models import UserDB as UserModel

logger = logging.getLogger(__name__)

# OAuth2 scheme for token extraction
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

# AUTH-OPTIMIZED: Pre-compute common values for JWT generation
_ALGORITHM = "RS256"  # Force RS256 regardless of settings
_PRIVATE_KEY = None  # Lazy load RSA keys
_PUBLIC_KEY = None
_TOKEN_EXPIRE_MINUTES = settings.ACCESS_TOKEN_EXPIRE_MINUTES

def _get_private_key():
    """Lazy load private key for signing."""
    global _PRIVATE_KEY
    if _PRIVATE_KEY is None:
        _PRIVATE_KEY = get_private_key()
    return _PRIVATE_KEY

def _get_public_key():
    """Lazy load public key for verification."""
    global _PUBLIC_KEY
    if _PUBLIC_KEY is None:
        _PUBLIC_KEY = get_public_key()
    return _PUBLIC_KEY

# Password hashing context - Support both bcrypt and argon2 for compatibility
# Production-ready configuration with appropriate security levels
import os

# Use production-grade bcrypt rounds for security
# 12 rounds is the recommended minimum for production
_bcrypt_rounds = (
    12 if os.getenv("ENVIRONMENT", "development") == "production" else 12
)  # Always use secure 12 rounds

# Production-ready password hashing with strong defaults
pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__rounds=_bcrypt_rounds,  # 12 rounds for production security
)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    try:
        # SECURITY: Password verification using bcrypt
        # Use passlib for consistent password verification
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error(f"Password verification error: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        # SECURITY: Password details removed from error logging
        # If verification fails due to hash format issues, return False
        return False


async def verify_password_fast(plain_password: str, hashed_password: str) -> bool:
    """AUTH-OPTIMIZED: Async password verification for <200ms performance."""
    loop = asyncio.get_event_loop()
    try:
        # Use thread pool executor to prevent blocking the event loop
        result = await loop.run_in_executor(
            None, pwd_context.verify, plain_password, hashed_password
        )
        return result
    except Exception as e:
        logger.error(f"Password verification error: {e}")
        return False


def get_password_hash(password: str) -> str:
    """Hash a password using bcrypt (preferred)."""
    return pwd_context.hash(password)


def create_access_token(
    user: UserModel, expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create a JWT access token for a user.

    The token contains minimal information - just user ID and tenant ID.
    All other user data MUST be fetched from database on each request.
    """
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    # Minimal token payload - just IDs for database lookup
    to_encode = {
        "exp": expire,
        "sub": f"{user.id}:{user.tenant_id}",  # user_id:tenant_id format
        "type": "access",
    }

    # Debug logging
    algorithm = "RS256"  # Force RS256 regardless of settings
    logger.info(f"JWT encoding with algorithm: {algorithm} (forced)")
    private_key = _get_private_key()
    logger.info(f"Private key type: {type(private_key)}, length: {len(private_key)}")
    
    encoded_jwt = jwt.encode(
        to_encode, private_key, algorithm=algorithm
    )
    return encoded_jwt


def create_refresh_token(
    user: UserModel, expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create a JWT refresh token for a user.

    Refresh tokens have a longer lifetime and are used to obtain new access tokens.
    """
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        # Refresh tokens last 7 days by default
        expire = datetime.now(timezone.utc) + timedelta(days=7)

    # Refresh token payload
    to_encode = {
        "exp": expire,
        "sub": f"{user.id}:{user.tenant_id}",  # user_id:tenant_id format
        "type": "refresh",
    }

    encoded_jwt = jwt.encode(
        to_encode, _get_private_key(), algorithm="RS256"
    )
    return encoded_jwt


def create_access_token_fast(
    user: UserModel, expires_delta: Optional[timedelta] = None
) -> str:
    """AUTH-OPTIMIZED: Ultra-fast JWT generation with pre-computed values."""
    # Use integer timestamps for performance
    now = int(time.time())
    expire = now + (
        expires_delta.total_seconds() if expires_delta else _TOKEN_EXPIRE_MINUTES * 60
    )

    # Minimal payload
    payload = {
        "exp": int(expire),
        "sub": f"{user.id}:{user.tenant_id}",
        "type": "access",
        "iat": now,
    }

    return jwt.encode(payload, _get_private_key(), algorithm=_ALGORITHM)


def decode_access_token(token: str) -> tuple[int, Optional[int]]:
    """
    Decode JWT token and extract user_id and tenant_id.

    Returns:
        tuple[int, Optional[int]]: (user_id, tenant_id) where tenant_id can be None

    Raises:
        HTTPException: If token is invalid
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(
            token, _get_public_key(), algorithms=["RS256"]
        )

        # Validate token type
        if payload.get("type") != "access":
            raise credentials_exception

        # Extract user_id:tenant_id from subject
        subject = payload.get("sub")
        if not subject or ":" not in subject:
            raise credentials_exception

        user_id_str, tenant_id_str = subject.split(":", 1)
        user_id = int(user_id_str)

        # Handle "None" tenant_id from tokens
        if tenant_id_str == "None":
            tenant_id = None
        else:
            tenant_id = int(tenant_id_str)

        return user_id, tenant_id

    except (JWTError, ValueError):
        raise credentials_exception


def decode_refresh_token(token: str) -> tuple[int, Optional[int]]:
    """
    Decode refresh JWT token and extract user_id and tenant_id.

    Returns:
        tuple[int, Optional[int]]: (user_id, tenant_id) where tenant_id can be None

    Raises:
        HTTPException: If token is invalid
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid refresh token",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(
            token, _get_public_key(), algorithms=["RS256"]
        )

        # Validate token type
        if payload.get("type") != "refresh":
            raise credentials_exception

        # Extract user_id:tenant_id from subject
        subject = payload.get("sub")
        if not subject or ":" not in subject:
            raise credentials_exception

        user_id_str, tenant_id_str = subject.split(":", 1)
        user_id = int(user_id_str)

        # Handle "None" tenant_id from tokens
        if tenant_id_str == "None":
            tenant_id = None
        else:
            tenant_id = int(tenant_id_str)

        return user_id, tenant_id

    except (JWTError, ValueError):
        raise credentials_exception


async def get_current_user(
    token: Annotated[str, Depends(oauth2_scheme)],
    conn: Annotated[Connection, Depends(get_db_session)],
) -> UserModel:
    """
    Get the current authenticated user from the database.

    SECURITY: This function ALWAYS queries the database to ensure:
    - User still exists
    - User is still active
    - User still belongs to the tenant
    - Current permissions are applied

    NO CACHING is performed for security reasons.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # Decode token to get user_id and tenant_id
    user_id, tenant_id = decode_access_token(token)

    # ALWAYS query database for current user state
    # Handle case where tenant_id is None
    if tenant_id is None:
        query = """
            SELECT 
                id, email, email as username, hashed_password,
                is_active, false as is_verified, is_superuser, tenant_id,
                created_at, updated_at, null as last_login, 0 as login_count
            FROM users 
            WHERE id = $1 AND tenant_id IS NULL
        """
        row = await conn.fetchrow(query, user_id)
    else:
        query = """
            SELECT 
                id, email, email as username, hashed_password,
                is_active, false as is_verified, is_superuser, tenant_id,
                created_at, updated_at, null as last_login, 0 as login_count
            FROM users 
            WHERE id = $1 AND tenant_id = $2
        """
        row = await conn.fetchrow(query, user_id, tenant_id)

    user = UserModel(**dict(row)) if row else None

    if user is None:
        logger.warning(f"User {user_id} not found or tenant mismatch")
        raise credentials_exception

    # Verify user is active
    if not user.is_active:
        logger.warning(f"Inactive user {user.email} attempted access")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="User account is deactivated"
        )

    return user


async def get_current_active_user(
    current_user: Annotated[UserModel, Depends(get_current_user)],
) -> UserModel:
    """
    Convenience function that ensures user is active.

    Since get_current_user already checks is_active, this is just
    for API consistency and can be used interchangeably.
    """
    return current_user


# Removed redundant authenticate_user wrapper function - use authenticate_user_with_db directly


async def authenticate_user_with_db(
    conn: Connection, email: str, password: str
) -> Optional[UserModel]:
    """
    Authenticate a user by email and password.

    SECURITY: No caching of authentication results.
    Each login attempt queries the database and verifies the password.
    """
    logger.info(f"Authentication attempt for email: {email}")

    try:
        # AUTH-OPTIMIZED: Query all required fields for UserDB model
        query = """
            SELECT id, email, username, hashed_password, is_active, is_superuser, 
                   is_verified, tenant_id, created_at, updated_at, last_login, login_count
            FROM users 
            WHERE email = $1 AND is_active = true
            LIMIT 1
        """
        row = await conn.fetchrow(query, email)
    except Exception as e:
        logger.error(f"Database error during authentication for {email}: {e}")
        return None  # Return None instead of raising exception to avoid CORS issues

    if not row:
        logger.info(f"Login attempt for non-existent user: {email}")
        return None

    try:
        # AUTH-OPTIMIZED: Create user with minimal data and sensible defaults
        user_data = {
            "id": row["id"],
            "email": row["email"],
            "username": row.get(
                "username", row["email"]
            ),  # Use email as username if not provided
            "hashed_password": row["hashed_password"],
            "is_active": row["is_active"],
            "is_verified": row.get(
                "is_verified", False
            ),  # Default for auth compatibility
            "is_superuser": row["is_superuser"],
            "tenant_id": row["tenant_id"],
            "created_at": row.get("created_at"),  # Use from DB if available
            "updated_at": row.get("updated_at"),  # Use from DB if available
            "last_login": row.get("last_login"),  # Use from DB if available
            "login_count": row.get("login_count", 0),  # Default for auth compatibility
        }
        user = UserModel(**user_data)
    except Exception as e:
        logger.error(f"Failed to create UserModel: {e}")
        logger.error(f"Row data: {dict(row)}")
        return None

    # AUTH-OPTIMIZED: Use async password verification for better performance
    password_valid = await verify_password_fast(password, user.hashed_password)
    if not password_valid:
        logger.info(f"Invalid password attempt for user: {email}")
        return None

    # Check if user is active
    if not user.is_active:
        logger.info(f"Login attempt by inactive user: {email}")
        return None

    logger.warning(f"Successful login for user: {email}")
    logger.warning(f"Returning user object: {type(user)}")
    return user


# WebSocket authentication helper
async def get_current_active_user_ws(
    token: str, conn: Connection
) -> Optional[UserModel]:
    """
    Validate WebSocket connection token and return active user.

    This is similar to get_current_active_user but designed for WebSocket
    connections where we can't use standard FastAPI dependencies.
    """
    try:
        # Decode the token
        user_id, tenant_id = decode_access_token(token)

        # Get user from database
        if tenant_id is None:
            query = """
                SELECT 
                    id, email, email as username, hashed_password,
                    is_active, false as is_verified, is_superuser, tenant_id,
                    created_at, updated_at, null as last_login, 0 as login_count
                FROM users 
                WHERE id = $1 AND tenant_id IS NULL
            """
            row = await conn.fetchrow(query, user_id)
        else:
            query = """
                SELECT 
                    id, email, email as username, hashed_password,
                    is_active, false as is_verified, is_superuser, tenant_id,
                    created_at, updated_at, null as last_login, 0 as login_count
                FROM users 
                WHERE id = $1 AND tenant_id = $2
            """
            row = await conn.fetchrow(query, user_id, tenant_id)

        if not row:
            return None

        user = UserModel(**dict(row))
        if not user.is_active:
            return None

        return user

    except Exception as e:
        logger.error(f"WebSocket authentication error: {e}")
        return None


async def require_admin_user(
    current_user: Annotated[UserModel, Depends(get_current_active_user)],
) -> UserModel:
    """
    Dependency that requires the current user to be an admin/superuser.

    Args:
        current_user: Current active user from authentication

    Returns:
        UserModel: The admin user

    Raises:
        HTTPException: If user is not a superuser
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Admin privileges required"
        )
    return current_user


# Export only the secure functions
__all__ = [
    "oauth2_scheme",
    "verify_password",
    "get_password_hash",
    "create_access_token",
    "create_refresh_token",
    "decode_access_token",
    "decode_refresh_token",
    "get_current_user",
    "get_current_active_user",
    "get_current_active_user_ws",
    "authenticate_user_with_db",
    "require_admin_user",
]
