"""
Dashboard service layer.

This module provides a consolidated service layer for dashboard operations,
abstracting the underlying database queries and business logic for dashboard metrics.

MIGRATION STATUS: ✅ MIGRATED to BaseService pattern
- Now inherits from BaseService for standardized database operations
- Unified error handling and logging
- Consistent service patterns across all domains
"""

import logging
import time
from datetime import date, datetime, timedelta
from typing import Any, Dict, Optional

from asyncpg import Connection

from ...shared.cache.performance_cache import cache_response
from ...shared.services.base_service import BaseService, ServiceError

logger = logging.getLogger(__name__)


class DashboardError(ServiceError):
    """Dashboard service error."""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            service_name="DashboardService",
            operation="dashboard",
            **kwargs,
        )


class DashboardService(BaseService):
    """Service for managing dashboard metrics and analytics."""

    def __init__(self, conn: Connection):
        super().__init__(conn)

    @cache_response(
        cache_type="dashboard_metrics", key_params=["start_date", "end_date"]
    )
    async def get_dashboard_metrics(
        self,
        tenant_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Dict[str, Any]:
        """
        Get comprehensive dashboard metrics for a tenant.

        Args:
            tenant_id: Tenant ID
            start_date: Optional start date for metrics
            end_date: Optional end date for metrics

        Returns:
            Dictionary containing dashboard metrics

        Raises:
            DashboardError: If metrics retrieval fails
        """
        try:
            query_start = time.time()

            # Default date range if not provided
            if not end_date:
                end_date = datetime.now().date()
            if not start_date:
                start_date = end_date - timedelta(days=30)

            # Optimized query using new performance indexes
            query = """
                SELECT 
                    COUNT(*) as total_count,
                    COALESCE(SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END), 0) as total_income,
                    COALESCE(SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END), 0) as total_expenses,
                    COUNT(CASE WHEN original_category IS NOT NULL OR ai_category IS NOT NULL THEN 1 END) as categorized_count,
                    COUNT(CASE WHEN upload_id IS NOT NULL THEN 1 END) as uploaded_count,
                    COUNT(CASE WHEN entity_id IS NOT NULL THEN 1 END) as entity_count
                FROM transactions
                WHERE tenant_id = $1
                AND date >= $2
                AND date <= $3
            """

            row = await self.conn.fetchrow(query, tenant_id, start_date, end_date)

            # Performance monitoring
            query_time = (time.time() - query_start) * 1000
            if query_time > 100:
                logger.warning(
                    f"🐌 SLOW dashboard query: {query_time:.2f}ms for tenant {tenant_id}"
                )
            else:
                logger.debug(
                    f"⚡ Dashboard query: {query_time:.2f}ms for tenant {tenant_id}"
                )

            total_count = row["total_count"] or 0
            total_income = float(row["total_income"] or 0)
            total_expenses = float(row["total_expenses"] or 0)
            categorized_count = row["categorized_count"] or 0
            uploaded_count = row["uploaded_count"] or 0
            entity_count = row["entity_count"] or 0

            return {
                "total_transactions": total_count,
                "total_income": total_income,
                "total_expenses": total_expenses,
                "net_income": total_income - total_expenses,
                "categorized_transactions": categorized_count,
                "uncategorized_transactions": total_count - categorized_count,
                "categorization_rate": (categorized_count / total_count * 100)
                if total_count > 0
                else 0,
                "uploaded_transactions": uploaded_count,
                "transactions_with_entities": entity_count,
                "date_range": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat(),
                    "days": (end_date - start_date).days + 1,
                },
                "daily_average": {
                    "transactions": total_count
                    / max((end_date - start_date).days + 1, 1),
                    "income": total_income / max((end_date - start_date).days + 1, 1),
                    "expenses": total_expenses
                    / max((end_date - start_date).days + 1, 1),
                },
            }

        except Exception as e:
            logger.error(f"Failed to get dashboard metrics for tenant {tenant_id}: {e}")
            raise DashboardError(f"Dashboard metrics retrieval failed: {str(e)}")

    async def get_recent_transactions(
        self,
        tenant_id: int,
        limit: int = 10,
        include_uncategorized_only: bool = False,
    ) -> Dict[str, Any]:
        """
        Get recent transactions for dashboard display.

        Args:
            tenant_id: Tenant ID
            limit: Maximum number of transactions to return
            include_uncategorized_only: Whether to return only uncategorized transactions

        Returns:
            Dictionary containing recent transactions

        Raises:
            DashboardError: If transaction retrieval fails
        """
        try:
            # Build query with optional filtering
            base_query = """
                SELECT id, date, description, amount, original_category, ai_category, 
                       entity_id, upload_id, created_at
                FROM transactions
                WHERE tenant_id = $1
            """

            if include_uncategorized_only:
                base_query += " AND (original_category IS NULL AND ai_category IS NULL)"

            base_query += " ORDER BY date DESC, created_at DESC LIMIT $2"

            rows = await self.conn.fetch(base_query, tenant_id, limit)

            return {
                "items": [
                    {
                        "id": row["id"],
                        "date": row["date"].isoformat() if row["date"] else None,
                        "description": row["description"],
                        "amount": float(row["amount"]),
                        "category": row["original_category"] or row["ai_category"],
                        "ai_suggested_category": row["ai_category"],
                        "is_categorized": bool(
                            row["original_category"] or row["ai_category"]
                        ),
                        "has_entity": bool(row["entity_id"]),
                        "is_uploaded": bool(row["upload_id"]),
                        "created_at": row["created_at"].isoformat()
                        if row["created_at"]
                        else None,
                    }
                    for row in rows
                ],
                "total": len(rows),
                "limit": limit,
                "filtered_uncategorized_only": include_uncategorized_only,
            }

        except Exception as e:
            logger.error(
                f"Failed to get recent transactions for tenant {tenant_id}: {e}"
            )
            raise DashboardError(f"Recent transactions retrieval failed: {str(e)}")

    async def get_category_breakdown(
        self,
        tenant_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        breakdown_type: str = "expenses",
        limit: int = 10,
    ) -> Dict[str, Any]:
        """
        Get spending/income breakdown by category.

        Args:
            tenant_id: Tenant ID
            start_date: Optional start date
            end_date: Optional end date
            breakdown_type: Type of breakdown ("expenses", "income", "both")
            limit: Maximum number of categories to return

        Returns:
            Dictionary containing category breakdown

        Raises:
            DashboardError: If breakdown retrieval fails
        """
        try:
            # Default date range if not provided
            if not end_date:
                end_date = datetime.now().date()
            if not start_date:
                start_date = end_date - timedelta(days=30)

            # Build amount filter based on breakdown type
            if breakdown_type == "expenses":
                amount_filter = "AND amount < 0"
                amount_select = "SUM(ABS(amount))"
            elif breakdown_type == "income":
                amount_filter = "AND amount > 0"
                amount_select = "SUM(amount)"
            else:  # both
                amount_filter = ""
                amount_select = "SUM(ABS(amount))"

            query = f"""
                SELECT 
                    COALESCE(original_category, ai_category) as category,
                    {amount_select} as total_amount,
                    COUNT(*) as transaction_count,
                    AVG(ABS(amount)) as average_amount
                FROM transactions
                WHERE tenant_id = $1
                AND (original_category IS NOT NULL OR ai_category IS NOT NULL)
                {amount_filter}
                AND date >= $2
                AND date <= $3
                GROUP BY COALESCE(original_category, ai_category)
                ORDER BY {amount_select} DESC
                LIMIT $4
            """

            rows = await self.conn.fetch(
                query, tenant_id, start_date, end_date, limit
            )

            total_amount = sum(float(row["total_amount"]) for row in rows)

            return {
                "items": [
                    {
                        "category": row["category"],
                        "amount": float(row["total_amount"]),
                        "transaction_count": row["transaction_count"],
                        "average_amount": float(row["average_amount"]),
                        "percentage": (float(row["total_amount"]) / total_amount * 100)
                        if total_amount > 0
                        else 0,
                    }
                    for row in rows
                ],
                "total_categories": len(rows),
                "total_amount": total_amount,
                "breakdown_type": breakdown_type,
                "date_range": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat(),
                },
            }

        except Exception as e:
            logger.error(
                f"Failed to get category breakdown for tenant {tenant_id}: {e}"
            )
            raise DashboardError(f"Category breakdown retrieval failed: {str(e)}")

    async def get_monthly_trends(
        self,
        tenant_id: int,
        months: int = 6,
    ) -> Dict[str, Any]:
        """
        Get monthly income/expense trends.

        Args:
            tenant_id: Tenant ID
            months: Number of months to analyze

        Returns:
            Dictionary containing monthly trends

        Raises:
            DashboardError: If trends retrieval fails
        """
        try:
            # Calculate start date for trend analysis
            end_date = datetime.now().date()
            start_date = end_date.replace(day=1) - timedelta(days=months * 31)

            query = """
                SELECT 
                    DATE_TRUNC('month', date) as month,
                    SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as income,
                    SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as expenses,
                    COUNT(*) as transaction_count
                FROM transactions
                WHERE tenant_id = $1
                AND date >= $2
                AND date <= $3
                GROUP BY DATE_TRUNC('month', date)
                ORDER BY month DESC
            """

            rows = await self.conn.fetch(query, tenant_id, start_date, end_date)

            return {
                "trends": [
                    {
                        "month": row["month"].strftime("%Y-%m")
                        if row["month"]
                        else None,
                        "income": float(row["income"]),
                        "expenses": float(row["expenses"]),
                        "net_income": float(row["income"]) - float(row["expenses"]),
                        "transaction_count": row["transaction_count"],
                    }
                    for row in rows
                ],
                "months_analyzed": len(rows),
                "date_range": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat(),
                },
            }

        except Exception as e:
            logger.error(f"Failed to get monthly trends for tenant {tenant_id}: {e}")
            raise DashboardError(f"Monthly trends retrieval failed: {str(e)}")

    async def get_entity_insights(
        self,
        tenant_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 10,
    ) -> Dict[str, Any]:
        """
        Get insights about entities (merchants/vendors) from transactions.

        Args:
            tenant_id: Tenant ID
            start_date: Optional start date
            end_date: Optional end date
            limit: Maximum number of entities to return

        Returns:
            Dictionary containing entity insights

        Raises:
            DashboardError: If entity insights retrieval fails
        """
        try:
            # Default date range if not provided
            if not end_date:
                end_date = datetime.now().date()
            if not start_date:
                start_date = end_date - timedelta(days=30)

            query = """
                SELECT 
                    e.name as entity_name,
                    e.entity_type,
                    COUNT(t.id) as transaction_count,
                    SUM(ABS(t.amount)) as total_amount,
                    AVG(ABS(t.amount)) as average_amount,
                    MIN(t.date) as first_transaction,
                    MAX(t.date) as last_transaction
                FROM entities e
                JOIN transactions t ON e.id = t.entity_id
                WHERE e.tenant_id = $1
                AND t.date >= $2
                AND t.date <= $3
                GROUP BY e.id, e.name, e.entity_type
                ORDER BY SUM(ABS(t.amount)) DESC
                LIMIT $4
            """

            rows = await self.conn.fetch(
                query, tenant_id, start_date, end_date, limit
            )

            return {
                "entities": [
                    {
                        "entity_name": row["entity_name"],
                        "entity_type": row["entity_type"],
                        "transaction_count": row["transaction_count"],
                        "total_amount": float(row["total_amount"]),
                        "average_amount": float(row["average_amount"]),
                        "first_transaction": row["first_transaction"].isoformat()
                        if row["first_transaction"]
                        else None,
                        "last_transaction": row["last_transaction"].isoformat()
                        if row["last_transaction"]
                        else None,
                        "frequency_days": (
                            row["last_transaction"] - row["first_transaction"]
                        ).days
                        + 1
                        if row["last_transaction"] and row["first_transaction"]
                        else 1,
                    }
                    for row in rows
                ],
                "total_entities": len(rows),
                "date_range": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat(),
                },
            }

        except Exception as e:
            logger.error(f"Failed to get entity insights for tenant {tenant_id}: {e}")
            raise DashboardError(f"Entity insights retrieval failed: {str(e)}")

    async def get_upload_summary(
        self,
        tenant_id: int,
        limit: int = 5,
    ) -> Dict[str, Any]:
        """
        Get summary of recent file uploads and their processing status.

        Args:
            tenant_id: Tenant ID
            limit: Maximum number of uploads to return

        Returns:
            Dictionary containing upload summary

        Raises:
            DashboardError: If upload summary retrieval fails
        """
        try:
            # Get recent uploads with transaction counts
            query = """
                SELECT 
                    u.id,
                    u.filename,
                    u.status,
                    u.uploaded_at,
                    COUNT(t.id) as transaction_count
                FROM uploads u
                LEFT JOIN transactions t ON u.id = t.upload_id
                WHERE u.tenant_id = $1
                GROUP BY u.id, u.filename, u.status, u.uploaded_at
                ORDER BY u.uploaded_at DESC
                LIMIT $2
            """

            rows = await self.conn.fetch(query, tenant_id, limit)

            return {
                "uploads": [
                    {
                        "upload_id": row["id"],
                        "filename": row["filename"],
                        "status": row["status"],
                        "uploaded_at": row["uploaded_at"].isoformat()
                        if row["uploaded_at"]
                        else None,
                        "transaction_count": row["transaction_count"],
                    }
                    for row in rows
                ],
                "total_uploads": len(rows),
            }

        except Exception as e:
            logger.error(f"Failed to get upload summary for tenant {tenant_id}: {e}")
            raise DashboardError(f"Upload summary retrieval failed: {str(e)}")


# Service singleton management (maintains backward compatibility)
_dashboard_service_cache: Dict[str, DashboardService] = {}


def get_dashboard_service(conn: Connection) -> DashboardService:
    """Get or create dashboard service instance for connection."""
    # Use connection ID as cache key
    conn_id = str(id(conn))

    if conn_id not in _dashboard_service_cache:
        _dashboard_service_cache[conn_id] = DashboardService(conn)

    return _dashboard_service_cache[conn_id]
