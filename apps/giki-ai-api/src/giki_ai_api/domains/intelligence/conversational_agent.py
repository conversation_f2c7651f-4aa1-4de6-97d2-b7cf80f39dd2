"""
Conversational Agent - Frontend Agent Panel Integration
======================================================

This module implements the conversational agent that powers the frontend
agent panel, providing natural language interaction with the system.

Features:
- Command parsing and routing
- Context-aware responses
- Integration with other agents
- Session memory management
"""

import json
import logging
from datetime import datetime
from typing import Any
from uuid import uuid4

from ...shared.ai.prompt_registry import get_prompt_registry
from ...shared.ai.standard_giki_agent import StandardGikiAgent

logger = logging.getLogger(__name__)


class ConversationalAgent(StandardGikiAgent):
    """
    Handles natural language interactions from the frontend agent panel.
    Routes commands to appropriate specialized agents and maintains context.
    """

    def __init__(self, conn=None, *args, **kwargs):
        # Set required StandardGikiAgent parameters
        if 'name' not in kwargs:
            kwargs['name'] = 'giki_conversational_agent'
        if 'description' not in kwargs:
            kwargs['description'] = 'AI assistant for natural language interaction with financial data and categorization'
        
        # Enable memory tools for session management
        if 'enable_standard_tools' not in kwargs:
            kwargs['enable_standard_tools'] = True
        if 'standard_tool_set' not in kwargs:
            kwargs['standard_tool_set'] = ['load_memory', 'preload_memory']
            
        super().__init__(*args, **kwargs)
        
        # Store attributes after super().__init__ to avoid Pydantic conflicts
        object.__setattr__(self, 'conn', conn)
        object.__setattr__(self, 'agent_id', "conversational_agent")
        object.__setattr__(self, 'display_name', "Giki Assistant")

        # Command patterns for routing
        object.__setattr__(self, 'command_patterns', {
            "/upload": "handle_upload_command",
            "/categorize": "handle_categorize_command",
            "/report": "handle_report_command",
            "/filter": "handle_filter_command",
            "/help": "handle_help_command",
            "/status": "handle_status_command",
        })

        # Session memory for context - will be replaced with ADK memory tools
        object.__setattr__(self, 'session_memory', {})

    async def process_command(
        self,
        command: str,
        parameters: dict[str, Any] | None = None,
        context: dict[str, Any] | None = None,
    ) -> dict[str, Any]:
        """
        Process a command from the frontend agent panel.

        Args:
            command: The command to process (e.g., '/upload', 'categorize these transactions')
            parameters: Optional parameters for the command
            context: Session context including user info, current state

        Returns:
            Response dictionary with message and metadata
        """
        try:
            session_id = context.get("session_id", str(uuid4()))
            user_id = context.get("user_id")
            tenant_id = context.get("tenant_id")

            # Update session memory
            await self._update_session_memory(session_id, context)

            # Parse command
            command_type, parsed_params = self._parse_command(command, parameters)

            # Route to appropriate handler
            handler_name = self.command_patterns.get(
                command_type, "handle_conversation"
            )
            handler = getattr(self, handler_name, self.handle_conversation)

            # Execute handler
            result = await handler(
                command=command,
                parameters=parsed_params,
                context={
                    "session_id": session_id,
                    "user_id": user_id,
                    "tenant_id": tenant_id,
                    "session_memory": self.session_memory.get(session_id, {}),
                },
            )

            # Format response
            return self._format_response(result, command_type)

        except Exception as e:
            logger.error(f"Command processing error: {e!s}")
            return self._format_error_response(str(e))

    async def handle_conversation(
        self, command: str, parameters: dict[str, Any], context: dict[str, Any]
    ) -> dict[str, Any]:
        """
        Handle general conversational queries using ADK.
        """
        try:
            # Prepare prompt with context
            prompt = self._build_conversational_prompt(command, context)

            # Use ADK to generate response
            response = await self.agent.run(prompt)

            # Extract relevant information
            return {
                "message": response.response.text
                if response.response
                else "I understand your request. How can I help you further?",
                "suggestions": self._extract_suggestions(response),
                "requires_action": False,
            }

        except Exception as e:
            logger.error(f"Conversation handling error: {e!s}")
            return {
                "message": "I encountered an issue processing your request. Could you please rephrase it?",
                "error": str(e),
            }

    async def handle_upload_command(
        self, command: str, parameters: dict[str, Any], context: dict[str, Any]
    ) -> dict[str, Any]:
        """Handle file upload commands."""
        return {
            "message": "I'll help you upload your transaction file. Please use the upload button to select your Excel or CSV file.",
            "action": "show_upload_interface",
            "metadata": {
                "accepted_formats": [".xlsx", ".xls", ".csv"],
                "max_file_size": "10MB",
            },
        }

    async def handle_categorize_command(
        self, command: str, parameters: dict[str, Any], context: dict[str, Any]
    ) -> dict[str, Any]:
        """Handle categorization commands."""
        # Check if there are pending transactions
        pending_count = parameters.get("pending_transactions", 0)

        if pending_count > 0:
            return {
                "message": f"I'll start categorizing your {pending_count} pending transactions. This should take about {pending_count * 0.1:.1f} seconds.",
                "action": "start_categorization",
                "metadata": {
                    "transaction_count": pending_count,
                    "estimated_time": pending_count * 0.1,
                },
            }
        else:
            return {
                "message": "You don't have any pending transactions to categorize. Would you like to upload a new file?",
                "suggestions": [
                    "Upload new file",
                    "View categorized transactions",
                    "Generate report",
                ],
            }

    async def handle_report_command(
        self, command: str, parameters: dict[str, Any], context: dict[str, Any]
    ) -> dict[str, Any]:
        """Handle report generation commands."""
        report_type = parameters.get("type", "expense_summary")
        date_range = parameters.get("date_range", "last_month")

        return {
            "message": f"I'll generate your {report_type.replace('_', ' ')} report for {date_range.replace('_', ' ')}.",
            "action": "generate_report",
            "metadata": {
                "report_type": report_type,
                "date_range": date_range,
                "format": "excel",
            },
        }

    async def handle_filter_command(
        self, command: str, parameters: dict[str, Any], context: dict[str, Any]
    ) -> dict[str, Any]:
        """Handle transaction filtering commands."""
        filters = parameters.get("filters", {})

        filter_description = self._describe_filters(filters)

        return {
            "message": f"I'll filter your transactions {filter_description}.",
            "action": "apply_filters",
            "metadata": {"filters": filters},
        }

    async def handle_help_command(
        self, command: str, parameters: dict[str, Any], context: dict[str, Any]
    ) -> dict[str, Any]:
        """Handle help commands."""
        return {
            "message": "Here's what I can help you with:",
            "suggestions": [
                "Upload transaction files",
                "Categorize transactions automatically",
                "Generate financial reports",
                "Filter and search transactions",
                "Review and approve categorizations",
                "Export data to Excel",
            ],
            "metadata": {
                "available_commands": list(self.command_patterns.keys()),
                "examples": [
                    "/upload - Start file upload process",
                    "/categorize - Categorize pending transactions",
                    "/report expense_summary - Generate expense report",
                    "/filter category:Travel - Filter by category",
                ],
            },
        }

    async def handle_status_command(
        self, command: str, parameters: dict[str, Any], context: dict[str, Any]
    ) -> dict[str, Any]:
        """Handle status check commands."""
        tenant_id = context.get("tenant_id")

        # Get real-time statistics
        stats = await self._get_tenant_statistics(tenant_id)

        return {
            "message": "Here's your current status:",
            "metadata": {
                "statistics": stats,
                "summary": f"You have {stats.get('pending_transactions', 0)} pending transactions and {stats.get('total_categories', 0)} categories set up.",
            },
        }

    def _parse_command(
        self, command: str, parameters: dict[str, Any] | None = None
    ) -> tuple[str, dict[str, Any]]:
        """Parse command to extract type and parameters."""
        command = command.strip()

        # Check for explicit commands
        for cmd_pattern in self.command_patterns:
            if command.startswith(cmd_pattern):
                return cmd_pattern, parameters or {}

        # Natural language parsing
        command_lower = command.lower()

        if any(word in command_lower for word in ["upload", "import", "file"]):
            return "/upload", parameters or {}
        elif any(
            word in command_lower for word in ["categorize", "classify", "process"]
        ):
            return "/categorize", parameters or {}
        elif any(word in command_lower for word in ["report", "export", "summary"]):
            return "/report", parameters or {}
        elif any(word in command_lower for word in ["filter", "search", "find"]):
            return "/filter", self._extract_filter_params(command)
        elif any(word in command_lower for word in ["help", "what can you do"]):
            return "/help", parameters or {}
        elif any(
            word in command_lower for word in ["status", "statistics", "how many"]
        ):
            return "/status", parameters or {}

        # Default to conversation
        return "conversation", parameters or {}

    def _extract_filter_params(self, command: str) -> dict[str, Any]:
        """Extract filter parameters from natural language."""
        filters = {}

        # Simple pattern matching for common filters
        if "category:" in command:
            category = command.split("category:")[1].split()[0]
            filters["category"] = category

        if "amount>" in command:
            amount = command.split("amount>")[1].split()[0]
            filters["amount_gt"] = float(amount)

        if "date:" in command:
            date = command.split("date:")[1].split()[0]
            filters["date"] = date

        return {"filters": filters}

    async def _update_session_memory(self, session_id: str, context: dict[str, Any]) -> None:
        """Update session memory with ADK memory tools."""
        try:
            # Try to use ADK memory tools if available
            from google.adk.tools import load_memory, preload_memory
            
            # Load existing memory for this session
            memory_key = f"conversational_session_{session_id}"
            existing_memory = await load_memory(memory_key)
            
            if not existing_memory:
                existing_memory = {
                    "created_at": datetime.utcnow().isoformat(),
                    "interactions": [],
                }
            
            # Update memory
            existing_memory["last_interaction"] = datetime.utcnow().isoformat()
            existing_memory["interactions"].append(
                {"timestamp": datetime.utcnow().isoformat(), "context": context}
            )
            
            # Keep only last 10 interactions
            if len(existing_memory["interactions"]) > 10:
                existing_memory["interactions"] = existing_memory["interactions"][-10:]
            
            # Save updated memory
            await preload_memory(memory_key, existing_memory)
            
            # Also update local cache for performance
            self.session_memory[session_id] = existing_memory
            
        except (ImportError, Exception) as e:
            logger.debug(f"ADK memory tools not available, using local memory: {e}")
            # Fallback to local memory management
            if session_id not in self.session_memory:
                self.session_memory[session_id] = {
                    "created_at": datetime.utcnow().isoformat(),
                    "interactions": [],
                }

            self.session_memory[session_id]["last_interaction"] = (
                datetime.utcnow().isoformat()
            )
            self.session_memory[session_id]["interactions"].append(
                {"timestamp": datetime.utcnow().isoformat(), "context": context}
            )

            # Keep only last 10 interactions
            if len(self.session_memory[session_id]["interactions"]) > 10:
                self.session_memory[session_id]["interactions"] = self.session_memory[
                    session_id
                ]["interactions"][-10:]

    def _build_conversational_prompt(
        self, command: str, context: dict[str, Any]
    ) -> str:
        """Build prompt for ADK conversation using centralized registry."""
        session_memory = context.get("session_memory", {})
        recent_interactions = session_memory.get("interactions", [])[-3:]
        
        # Use centralized prompt registry
        prompt_registry = get_prompt_registry()
        prompt_version = prompt_registry.get("conversational_assistant")
        
        # Format recent context
        recent_context_str = json.dumps(recent_interactions, indent=2) if recent_interactions else "No recent interactions"
        
        prompt = prompt_version.format(
            command=command,
            recent_context=recent_context_str
        )

        return prompt

    def _extract_suggestions(self, adk_response: Any) -> list[str]:
        """Extract action suggestions from ADK response."""
        suggestions = []

        # Basic suggestion extraction
        if hasattr(adk_response, "response") and adk_response.response:
            text = adk_response.response.text.lower()

            if "upload" in text:
                suggestions.append("Upload transaction file")
            if "categorize" in text:
                suggestions.append("Start categorization")
            if "report" in text:
                suggestions.append("Generate report")

        return suggestions[:3]  # Limit to 3 suggestions

    def _describe_filters(self, filters: dict[str, Any]) -> str:
        """Create human-readable filter description."""
        descriptions = []

        if "category" in filters:
            descriptions.append(f"by category '{filters['category']}'")
        if "amount_gt" in filters:
            descriptions.append(f"with amount greater than ${filters['amount_gt']}")
        if "date" in filters:
            descriptions.append(f"on date {filters['date']}")

        return " and ".join(descriptions) if descriptions else "with your criteria"

    def _format_response(
        self, result: dict[str, Any], command_type: str
    ) -> dict[str, Any]:
        """Format response for frontend consumption."""
        return {
            "success": True,
            "command_type": command_type,
            "message": result.get("message", ""),
            "action": result.get("action"),
            "metadata": result.get("metadata", {}),
            "suggestions": result.get("suggestions", []),
            "timestamp": datetime.utcnow().isoformat(),
        }

    def _format_error_response(self, error: str) -> dict[str, Any]:
        """Format error response."""
        return {
            "success": False,
            "message": "I encountered an issue processing your request.",
            "error": error,
            "suggestions": [
                "Try rephrasing your request",
                "Use /help to see available commands",
            ],
            "timestamp": datetime.utcnow().isoformat(),
        }

    async def _get_tenant_statistics(self, tenant_id: int | None) -> dict[str, Any]:
        """Get real statistics for the tenant from database."""
        if not tenant_id:
            return {
                "total_transactions": 0,
                "pending_transactions": 0,
                "categorized_transactions": 0,
                "total_categories": 0,
                "accuracy_rate": 0.0,
                "last_upload": None,
            }
        
        try:
            from ...core.database import get_db_session
            
            async with get_db_session() as conn:
                # Get transaction counts and statistics
                transaction_stats_query = """
                    SELECT 
                        COUNT(*) as total_transactions,
                        COUNT(CASE WHEN ai_category IS NULL OR ai_category = '' THEN 1 END) as pending_transactions,
                        COUNT(CASE WHEN ai_category IS NOT NULL AND ai_category != '' THEN 1 END) as categorized_transactions,
                        COUNT(CASE WHEN confidence_score >= 0.85 THEN 1 END) as high_confidence_transactions,
                        AVG(CASE WHEN confidence_score IS NOT NULL THEN confidence_score END) as avg_confidence,
                        MAX(upload_date) as last_upload
                    FROM transactions 
                    WHERE tenant_id = $1
                """
                
                transaction_stats = await conn.fetchrow(transaction_stats_query, tenant_id)
                
                # Get category count
                category_count_query = """
                    SELECT COUNT(DISTINCT category_name) as total_categories
                    FROM categories 
                    WHERE tenant_id = $1
                """
                
                category_result = await conn.fetchrow(category_count_query, tenant_id)
                
                # Calculate accuracy rate based on high confidence transactions
                total_transactions = transaction_stats["total_transactions"] or 0
                high_confidence_count = transaction_stats["high_confidence_transactions"] or 0
                accuracy_rate = (high_confidence_count / total_transactions) if total_transactions > 0 else 0.0
                
                return {
                    "total_transactions": total_transactions,
                    "pending_transactions": transaction_stats["pending_transactions"] or 0,
                    "categorized_transactions": transaction_stats["categorized_transactions"] or 0,
                    "total_categories": category_result["total_categories"] or 0,
                    "accuracy_rate": round(accuracy_rate, 3),
                    "last_upload": transaction_stats["last_upload"].isoformat() if transaction_stats["last_upload"] else None,
                }
                
        except Exception as e:
            logger.error(f"Failed to get tenant statistics: {e}")
            # Return empty stats on error instead of mock data
            return {
                "total_transactions": 0,
                "pending_transactions": 0,
                "categorized_transactions": 0,
                "total_categories": 0,
                "accuracy_rate": 0.0,
                "last_upload": None,
                "error": "Failed to fetch statistics"
            }
