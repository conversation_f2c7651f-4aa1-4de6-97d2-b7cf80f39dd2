"""
Agents API router.

Provides conversational agent endpoints for frontend chat integration.
"""

import logging
from typing import Any, Dict, List, Optional

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from ...core.dependencies import get_current_tenant_id, get_db_session
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user
# ADK-compliant agents only - removed non-ADK AgentFactory and UnifiedBaseAgent
from ..intelligence.conversational_agent import ConversationalAgent
from ..intelligence.customer_agent import CustomerFacingAgent
from ..intelligence.coordinator_agent import CoordinatorAgent

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/agents", tags=["Agents"])


class ChatMessage(BaseModel):
    """Chat message model for frontend communication."""

    message: str
    command: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    context: Optional[Dict[str, Any]] = None


class ChatResponse(BaseModel):
    """Chat response model for frontend."""

    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    suggestions: Optional[List[str]] = None
    actions: Optional[List[Dict[str, Any]]] = None
    processing_time_ms: Optional[float] = None
    error: Optional[str] = None


@router.post("/chat", response_model=ChatResponse)
async def chat_with_agent(
    chat_message: ChatMessage,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
):
    """
    Process chat message with conversational agent.

    This endpoint handles both natural language messages and specific commands
    from the frontend UnifiedAgentPanel.
    """
    try:
        # Get vertex client for AI capabilities
        from ...core.dependencies import get_vertex_ai_client

        vertex_client = await get_vertex_ai_client()

        # Create ADK-compliant conversational agent
        agent = ConversationalAgent(
            name="conversational_agent",
            description="Conversational agent for chat interactions",
            tenant_id=tenant_id
        )

        # Process message with ADK agent (using only ADK-compliant methods)
        if chat_message.command:
            # Use process_command method from ConversationalAgent
            result = await agent.process_command(
                command=chat_message.command,
                parameters=chat_message.parameters or {},
                tenant_id=tenant_id,
                user_id=current_user.id
            )
        else:
            # Handle natural language with the agent directly
            # Using ADK agent's built-in capabilities for conversation
            result = await agent.handle_conversation(
                message=chat_message.message,
                tenant_id=tenant_id,
                user_id=current_user.id
            )

        return ChatResponse(
            success=True,
            message=result.get("message", "Command processed successfully"),
            data=result.get("data"),
            suggestions=result.get("suggestions", []),
            actions=result.get("actions", []),
            processing_time_ms=result.get("processing_time_ms"),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chat processing failed: {e}")
        return ChatResponse(
            success=False,
            message="I encountered an error processing your request. Please try again.",
            error=str(e),
        )


@router.get("/commands")
async def get_available_commands(
    tenant_id: int = Depends(get_current_tenant_id),
    current_user: User = Depends(get_current_active_user),
):
    """Get list of available commands for the agent."""
    return {
        "commands": [
            {
                "command": "/upload",
                "description": "Upload and process transaction files",
                "parameters": ["file_path", "column_mapping"],
                "example": "Upload my bank statement for processing",
            },
            {
                "command": "/filter",
                "description": "Filter transactions by criteria",
                "parameters": ["date_range", "category", "amount_range"],
                "example": "Show me transactions from last month over $1000",
            },
            {
                "command": "/export",
                "description": "Export data to various formats",
                "parameters": ["format", "date_range", "filters"],
                "example": "Export all expenses from Q1 to Excel",
            },
            {
                "command": "/categorize",
                "description": "Categorize transactions automatically",
                "parameters": ["transaction_ids", "force_recategorize"],
                "example": "Categorize all uncategorized transactions",
            },
            {
                "command": "/delete",
                "description": "Delete transactions or entities",
                "parameters": ["transaction_ids", "entity_ids"],
                "example": "Delete duplicate transactions from yesterday",
            },
            {
                "command": "/report",
                "description": "Generate financial reports",
                "parameters": ["report_type", "date_range", "format"],
                "example": "Generate profit & loss report for this quarter",
            },
            {
                "command": "/analyze",
                "description": "Analyze spending patterns and trends",
                "parameters": ["analysis_type", "time_period"],
                "example": "Analyze my spending trends over the last 6 months",
            },
            {
                "command": "/settings",
                "description": "Manage account and system settings",
                "parameters": ["setting_type", "new_values"],
                "example": "Update my default expense categories",
            },
            {
                "command": "/search",
                "description": "Search transactions and data",
                "parameters": ["query", "filters", "sort_order"],
                "example": "Search for all Amazon purchases this year",
            },
            {
                "command": "/create",
                "description": "Create new entities or categories",
                "parameters": ["entity_type", "properties"],
                "example": "Create a new expense category for office supplies",
            },
            {
                "command": "/refresh",
                "description": "Refresh data and recalculate metrics",
                "parameters": ["scope"],
                "example": "Refresh dashboard metrics and category totals",
            },
        ],
        "natural_language_support": True,
        "context_aware": True,
    }


@router.get("/status")
async def get_agent_status(
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
):
    """Get agent status and capabilities."""
    try:
        # Check agent health by testing connection and basic functionality
        from ...core.dependencies import get_vertex_client

        _vertex_client = await get_vertex_client()

        # Test database connectivity
        test_result = await conn.fetchval("SELECT 1")

        return {
            "status": "healthy",
            "agent_version": "1.0.0",
            "commands_available": 11,
            "database_connected": test_result == 1,
            "tenant_id": tenant_id,
            "user_id": current_user.id,
            "capabilities": [
                "Natural language processing",
                "Command routing",
                "Context management",
                "Multi-domain integration",
                "Real-time responses",
            ],
        }

    except Exception as e:
        logger.error(f"Agent status check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "database_connected": False,
        }


@router.post("/suggestions")
async def get_command_suggestions(
    partial_message: str,
    tenant_id: int = Depends(get_current_tenant_id),
    current_user: User = Depends(get_current_active_user),
):
    """Get command suggestions based on partial message input."""
    try:
        # Simple suggestion logic based on keywords
        suggestions = []
        partial_lower = partial_message.lower()

        suggestion_map = {
            "upload": ["Upload file", "Process transactions", "Import data"],
            "filter": [
                "Filter transactions",
                "Show specific data",
                "Find transactions",
            ],
            "export": ["Export to Excel", "Download report", "Save data"],
            "categorize": [
                "Categorize transactions",
                "Auto-categorize",
                "Fix categories",
            ],
            "report": ["Generate report", "Create analysis", "View summary"],
            "analyze": ["Analyze spending", "Show trends", "Pattern analysis"],
            "search": ["Search transactions", "Find specific data", "Lookup records"],
            "create": ["Create category", "Add new entity", "Set up new item"],
            "delete": ["Delete transactions", "Remove duplicates", "Clean up data"],
            "settings": ["Update settings", "Change preferences", "Configure system"],
            "refresh": ["Refresh data", "Update metrics", "Reload information"],
        }

        for keyword, keyword_suggestions in suggestion_map.items():
            if keyword in partial_lower:
                suggestions.extend(keyword_suggestions)

        # If no specific keyword matches, provide general suggestions
        if not suggestions:
            suggestions = [
                "Show me my recent transactions",
                "Upload a new file",
                "Generate a spending report",
                "Analyze my expenses",
                "Help me categorize transactions",
            ]

        return {
            "suggestions": suggestions[:5],  # Limit to 5 suggestions
            "partial_message": partial_message,
        }

    except Exception as e:
        logger.error(f"Suggestion generation failed: {e}")
        return {
            "suggestions": [],
            "error": str(e),
        }
