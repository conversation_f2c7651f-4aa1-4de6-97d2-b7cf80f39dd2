"""
Business Appropriateness Evaluation Agent for Zero-Onboarding

This agent evaluates whether AI-generated categories are "business-appropriate"
for zero-onboarding scenarios, using a weighted scoring system:

- 40% Context Appropriateness: Does the category make sense for the transaction context?
- 30% Industry Standards: Does the category align with standard business practices?
- 20% Merchant/Vendor Alignment: Does the category match what this merchant typically provides?
- 10% Amount Reasonableness: Does the amount make sense for this category?

Target: >85% business appropriateness for zero-onboarding validation
"""

import asyncio
import json
import logging
import os
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import vertexai
from vertexai.generative_models import GenerativeModel

from ...shared.ai.prompt_registry import get_prompt_registry
from ...shared.ai.standard_giki_agent import StandardGikiAgent, create_enhanced_function_tool

logger = logging.getLogger(__name__)


@dataclass
class BusinessAppropriatenessConfig:
    """Configuration for business appropriateness evaluation."""

    model_name: str = "gemini-2.0-flash-001"
    project: str = "rezolve-poc"
    location: str = "us-central1"

    # Evaluation weights (must sum to 100%)
    context_weight: float = 0.40  # 40%
    industry_weight: float = 0.30  # 30%
    merchant_weight: float = 0.20  # 20%
    amount_weight: float = 0.10  # 10%


@dataclass
class BusinessAppropriatenessResult:
    """Result of business appropriateness evaluation."""

    overall_score: float  # 0.0 to 1.0
    is_appropriate: bool  # True if >= 0.85

    # Individual component scores
    context_score: float
    industry_score: float
    merchant_score: float
    amount_score: float

    # Detailed reasoning
    context_reasoning: str
    industry_reasoning: str
    merchant_reasoning: str
    amount_reasoning: str

    # Overall assessment
    overall_reasoning: str
    improvement_suggestions: List[str]


class BusinessAppropriatenessAgent(StandardGikiAgent):
    """
    Agent for evaluating business appropriateness of AI categorizations.

    Used for zero-onboarding validation where we need to
    assess whether categories are "business-appropriate" without comparing
    to historical categories.
    """

    def __init__(self, config: Optional[BusinessAppropriatenessConfig] = None):
        """Initialize the business appropriateness evaluation agent."""

        # Store config for backward compatibility
        _config = config or BusinessAppropriatenessConfig()

        # Store evaluation weights as instance variables
        self._context_weight = _config.context_weight
        self._industry_weight = _config.industry_weight
        self._merchant_weight = _config.merchant_weight
        self._amount_weight = _config.amount_weight

        # Create function tools for business appropriateness evaluation
        custom_tools = [
            create_enhanced_function_tool(
                func=self.evaluate_appropriateness,
                name="evaluate_appropriateness",
                description="Evaluate business appropriateness of AI categorizations for zero-onboarding",
                category="accuracy_evaluation"
            ),
            create_enhanced_function_tool(
                func=self.evaluate_batch,
                name="evaluate_batch",
                description="Batch evaluate business appropriateness of multiple categorizations",
                category="accuracy_evaluation"
            )
        ]

        # Initialize StandardGikiAgent with appropriateness evaluation tools
        super().__init__(
            name="giki-ai-business-appropriateness-agent",
            description="AI agent for evaluating business appropriateness of categorizations in zero-onboarding scenarios",
            custom_tools=custom_tools,
            enable_code_execution=False,  # Not needed for evaluation
            enable_standard_tools=False,  # Use only evaluation-specific tools
            model_name=_config.model_name,
            project_id=_config.project,
            location=_config.location
        )

        # Initialize legacy model for backward compatibility
        self._model = GenerativeModel(model_name=_config.model_name)

        # Get prompt registry for evaluation prompts
        self.prompt_registry = get_prompt_registry()

        logger.info("✅ Business Appropriateness Agent initialized for evaluation")

    async def evaluate_appropriateness(
        self,
        transaction_description: str,
        transaction_amount: float,
        ai_category: str,
        merchant_name: Optional[str] = None,
        transaction_date: Optional[str] = None,
        additional_context: Optional[Dict[str, Any]] = None,
    ) -> BusinessAppropriatenessResult:
        """
        Evaluate the business appropriateness of an AI categorization.

        Args:
            transaction_description: The transaction description
            transaction_amount: The transaction amount
            ai_category: The AI-suggested category
            merchant_name: Optional merchant/vendor name
            transaction_date: Optional transaction date
            additional_context: Optional additional context

        Returns:
            BusinessAppropriatenessResult with detailed evaluation
        """

        try:
            # Get the business appropriateness evaluation prompt
            prompt = self.prompt_registry.get("business_appropriateness_evaluation")

            # Prepare evaluation context
            evaluation_context = {
                "transaction_description": transaction_description,
                "transaction_amount": transaction_amount,
                "ai_category": ai_category,
                "merchant_name": merchant_name or "Unknown",
                "transaction_date": transaction_date or "Not specified",
                "context_weight": self._context_weight * 100,
                "industry_weight": self._industry_weight * 100,
                "merchant_weight": self._merchant_weight * 100,
                "amount_weight": self._amount_weight * 100,
                "additional_context": json.dumps(additional_context or {}, indent=2),
            }

            # Format the prompt
            formatted_prompt = prompt.format(**evaluation_context)

            # Track performance
            import time

            start_time = time.time()

            # Call the AI model (using async method)
            response = await self._model.generate_content_async(
                formatted_prompt, generation_config=prompt.model_config
            )

            # Track performance
            latency_ms = (time.time() - start_time) * 1000

            # Parse the response
            evaluation_result = self._parse_evaluation_response(response.text)

            # Track success
            self.prompt_registry.track_performance(
                prompt_id=prompt.id,
                version=prompt.version,
                success=True,
                latency_ms=latency_ms,
                metadata={
                    "transaction_amount": transaction_amount,
                    "ai_category": ai_category,
                    "overall_score": evaluation_result.overall_score,
                },
            )

            logger.info(
                f"Business appropriateness evaluation completed: "
                f"{evaluation_result.overall_score:.1%} appropriate"
            )

            return evaluation_result

        except Exception as e:
            # Track failure
            self.prompt_registry.track_performance(
                prompt_id="business_appropriateness_evaluation",
                version="1.0.0",
                success=False,
                latency_ms=0,
                metadata={"error": str(e)},
            )

            logger.error(f"Business appropriateness evaluation failed: {e}")
            raise

    def _parse_evaluation_response(
        self, response_text: str
    ) -> BusinessAppropriatenessResult:
        """Parse the AI response into a structured result."""

        try:
            # Extract JSON from response, handling markdown code blocks
            response_text = response_text.strip()
            if response_text.startswith("```json"):
                response_text = response_text[7:]  # Remove ```json
            if response_text.startswith("```"):
                response_text = response_text[3:]  # Remove ```
            if response_text.endswith("```"):
                response_text = response_text[:-3]  # Remove trailing ```

            response_data = json.loads(response_text.strip())

            # Calculate overall score using weighted components
            overall_score = (
                response_data["context_score"] * self._context_weight
                + response_data["industry_score"] * self._industry_weight
                + response_data["merchant_score"] * self._merchant_weight
                + response_data["amount_score"] * self._amount_weight
            )

            # Determine if appropriate (85% threshold for M1)
            is_appropriate = overall_score >= 0.85

            return BusinessAppropriatenessResult(
                overall_score=overall_score,
                is_appropriate=is_appropriate,
                context_score=response_data["context_score"],
                industry_score=response_data["industry_score"],
                merchant_score=response_data["merchant_score"],
                amount_score=response_data["amount_score"],
                context_reasoning=response_data["context_reasoning"],
                industry_reasoning=response_data["industry_reasoning"],
                merchant_reasoning=response_data["merchant_reasoning"],
                amount_reasoning=response_data["amount_reasoning"],
                overall_reasoning=response_data["overall_reasoning"],
                improvement_suggestions=response_data.get(
                    "improvement_suggestions", []
                ),
            )

        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Failed to parse evaluation response: {e}")
            logger.error(f"Response text: {response_text}")

            # Return a fallback result
            return BusinessAppropriatenessResult(
                overall_score=0.0,
                is_appropriate=False,
                context_score=0.0,
                industry_score=0.0,
                merchant_score=0.0,
                amount_score=0.0,
                context_reasoning="Failed to parse evaluation",
                industry_reasoning="Failed to parse evaluation",
                merchant_reasoning="Failed to parse evaluation",
                amount_reasoning="Failed to parse evaluation",
                overall_reasoning=f"Evaluation parsing failed: {e}",
                improvement_suggestions=["Fix evaluation response parsing"],
            )

    async def evaluate_batch(
        self, transactions: List[Dict[str, Any]], batch_size: int = 150
    ) -> Dict[str, Any]:
        """
        Evaluate business appropriateness for a batch of transactions.

        Args:
            transactions: List of transaction dictionaries
            batch_size: Size of processing batches (default 150 for M1)

        Returns:
            Batch evaluation results with statistics
        """

        logger.info(f"Starting batch evaluation of {len(transactions)} transactions")

        results = []
        total_score = 0.0
        appropriate_count = 0

        # Process in batches to avoid overwhelming the system
        for i in range(0, len(transactions), batch_size):
            batch = transactions[i : i + batch_size]
            batch_results = []

            # Process batch with controlled concurrency
            tasks = []
            for transaction in batch:
                task = self.evaluate_appropriateness(
                    transaction_description=transaction.get("description", ""),
                    transaction_amount=float(transaction.get("amount", 0)),
                    ai_category=transaction.get("ai_category", ""),
                    merchant_name=transaction.get("merchant_name"),
                    transaction_date=transaction.get("date"),
                    additional_context=transaction.get("context", {}),
                )
                tasks.append(task)

            # Execute batch with limited concurrency
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process batch results
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    logger.error(f"Evaluation failed for transaction {i + j}: {result}")
                    continue

                results.append(result)
                total_score += result.overall_score
                if result.is_appropriate:
                    appropriate_count += 1

            logger.info(
                f"Completed batch {i // batch_size + 1}, {len(results)} evaluations total"
            )

        # Calculate overall statistics
        if results:
            average_score = total_score / len(results)
            appropriateness_rate = appropriate_count / len(results)

            # Determine if M1 target is met
            m1_target_met = appropriateness_rate >= 0.85

            batch_summary = {
                "total_transactions": len(transactions),
                "successful_evaluations": len(results),
                "failed_evaluations": len(transactions) - len(results),
                "average_score": average_score,
                "appropriateness_rate": appropriateness_rate,
                "appropriate_count": appropriate_count,
                "m1_target_met": m1_target_met,
                "target_threshold": 0.85,
                "detailed_results": results,
            }

            logger.info(
                f"Batch evaluation completed: {appropriateness_rate:.1%} appropriate "
                f"(target: 85%, {'✅ PASSED' if m1_target_met else '❌ FAILED'})"
            )

            return batch_summary

        else:
            logger.error("No successful evaluations in batch")
            return {
                "total_transactions": len(transactions),
                "successful_evaluations": 0,
                "failed_evaluations": len(transactions),
                "average_score": 0.0,
                "appropriateness_rate": 0.0,
                "appropriate_count": 0,
                "m1_target_met": False,
                "target_threshold": 0.85,
                "detailed_results": [],
            }


# Singleton for easy access
_business_appropriateness_agent = None


def get_business_appropriateness_agent() -> BusinessAppropriatenessAgent:
    """Get the singleton business appropriateness agent."""
    global _business_appropriateness_agent
    if _business_appropriateness_agent is None:
        _business_appropriateness_agent = BusinessAppropriatenessAgent()
    return _business_appropriateness_agent
