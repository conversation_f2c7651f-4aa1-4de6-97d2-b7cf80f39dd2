"""
Shared schemas package for the giki.ai API.

This package provides base schemas and validation patterns used across
the entire application. Currently simplified due to consolidation work.

Example usage:

    # Domain-specific schemas
    from giki_ai_api.domains.transactions.unified_schemas import TransactionCreate
    from giki_ai_api.domains.auth.schemas import UserCreate
    
    # Use domain schemas directly for now
    transaction_data = TransactionCreate(...)
    user_data = UserCreate(...)

Future expansion:
    # Once base_schemas module is implemented:
    from .base_schemas import BaseRequest, BaseResponse
    
    # Create domain-specific schemas
    CategoryListResponse = SchemaFactory.create_list_response_class(Category)
    CategoryCreateRequest = SchemaFactory.create_create_request_class(CategoryBase)
"""

# Temporarily disabled due to missing base_schemas module
# from .base_schemas import (
#     # Base classes and utilities will be imported here
# )

# Temporarily disabled due to missing base_schemas module
__all__ = [
    # Will be populated when base_schemas module is implemented
]