"""
Base Router Classes - Unified router architecture
=================================================

This module provides base classes and utilities for creating consistent routers
across the application. It consolidates common router patterns and provides
a unified architecture for API endpoint management.

Key features:
- Unified base router class with common functionality
- Standardized request/response patterns
- Built-in pagination and filtering
- Performance tracking and error handling
- Configurable router factory
- Consistent API structure
"""

import logging
import time
import uuid
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, TypeVar, Generic
from dataclasses import dataclass, field

from fastapi import APIRouter, Query, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from asyncpg import Connection

logger = logging.getLogger(__name__)

# Type variables for generic responses
T = TypeVar('T')


@dataclass
class BaseRouterConfig:
    """Configuration for unified base routers."""
    prefix: str
    tags: List[str]
    dependencies: List[Depends] = field(default_factory=list)
    enable_pagination: bool = True
    enable_filtering: bool = True
    enable_caching: bool = False
    cache_ttl: int = 300
    rate_limit: Optional[int] = None


class PaginationParams(BaseModel):
    """Standard pagination parameters."""
    page: int = Field(default=1, ge=1, description="Page number (1-based)")
    page_size: int = Field(default=20, ge=1, le=100, description="Items per page")
    
    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.page_size
    
    @property
    def limit(self) -> int:
        """Get limit for database queries."""
        return self.page_size


class DateRangeParams(BaseModel):
    """Standard date range filtering parameters."""
    start_date: Optional[str] = Field(None, description="Start date (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="End date (YYYY-MM-DD)")


class FilterParams(BaseModel):
    """Base filtering parameters."""
    search: Optional[str] = Field(None, description="Search term")
    sort_by: Optional[str] = Field(None, description="Sort field")
    sort_order: Optional[str] = Field("asc", pattern="^(asc|desc)$", description="Sort order")


class StandardResponse(BaseModel, Generic[T]):
    """Standard API response format."""
    success: bool = True
    message: str = "Operation completed successfully"
    data: Optional[T] = None
    timestamp: str = Field(default_factory=lambda: str(int(time.time())))
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))


class PaginatedResponse(BaseModel, Generic[T]):
    """Paginated response format."""
    success: bool = True
    message: str = "Data retrieved successfully"
    data: List[T] = Field(default_factory=list)
    pagination: Dict[str, Any] = Field(default_factory=dict)
    total_count: int = 0
    timestamp: str = Field(default_factory=lambda: str(int(time.time())))
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    
    @classmethod
    def create(
        cls,
        data: List[T],
        total_count: int,
        pagination_params: PaginationParams,
        message: str = "Data retrieved successfully"
    ) -> "PaginatedResponse[T]":
        """Create a paginated response with calculated pagination info."""
        total_pages = (total_count + pagination_params.page_size - 1) // pagination_params.page_size
        
        pagination_info = {
            "current_page": pagination_params.page,
            "page_size": pagination_params.page_size,
            "total_pages": total_pages,
            "total_count": total_count,
            "has_next": pagination_params.page < total_pages,
            "has_prev": pagination_params.page > 1
        }
        
        return cls(
            data=data,
            total_count=total_count,
            pagination=pagination_info,
            message=message
        )


class UnifiedBaseRouter(ABC):
    """
    Abstract base class for unified routers.
    
    Provides common functionality for all domain routers including:
    - Standardized configuration
    - Built-in pagination and filtering
    - Error handling patterns
    - Performance tracking
    - Consistent response formats
    """
    
    def __init__(self, config: BaseRouterConfig):
        """Initialize the unified base router."""
        self.config = config
        self.router = APIRouter(
            prefix=config.prefix,
            tags=config.tags,
            dependencies=config.dependencies
        )
        self._setup_routes()
    
    @abstractmethod
    def _setup_routes(self) -> None:
        """Setup routes for this router. Must be implemented by subclasses."""
        pass
    
    def get_router(self) -> APIRouter:
        """Get the configured FastAPI router."""
        return self.router
    
    def _create_standard_response(
        self,
        data: Any = None,
        message: str = "Operation completed successfully",
        success: bool = True
    ) -> StandardResponse:
        """Create a standardized response."""
        return StandardResponse(
            success=success,
            message=message,
            data=data
        )
    
    def _create_paginated_response(
        self,
        data: List[Any],
        total_count: int,
        pagination_params: PaginationParams,
        message: str = "Data retrieved successfully"
    ) -> PaginatedResponse:
        """Create a standardized paginated response."""
        return PaginatedResponse.create(
            data=data,
            total_count=total_count,
            pagination_params=pagination_params,
            message=message
        )
    
    def _handle_error(
        self,
        error: Exception,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        message: str = "An error occurred"
    ) -> HTTPException:
        """Handle errors with standardized format."""
        logger.error(f"Router error: {str(error)}", exc_info=True)
        
        return HTTPException(
            status_code=status_code,
            detail={
                "success": False,
                "message": message,
                "error": str(error),
                "timestamp": str(int(time.time())),
                "request_id": str(uuid.uuid4())
            }
        )


class RouterFactory:
    """Factory for creating domain-specific routers."""
    
    @staticmethod
    def create_domain_router(
        domain: str,
        router_class: type,
        **config_kwargs
    ) -> UnifiedBaseRouter:
        """Create a domain-specific router with standard configuration."""
        config = BaseRouterConfig(
            prefix=f"/api/v1/{domain}",
            tags=[domain.title()],
            **config_kwargs
        )
        
        return router_class(config)
    
    @staticmethod
    def create_admin_router(
        router_class: type,
        **config_kwargs
    ) -> UnifiedBaseRouter:
        """Create an admin router with admin-specific configuration."""
        config = BaseRouterConfig(
            prefix="/api/v1/admin",
            tags=["Admin"],
            enable_caching=False,  # Disable caching for admin endpoints
            **config_kwargs
        )
        
        return router_class(config)


# Common dependency functions
def get_pagination_params(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page")
) -> PaginationParams:
    """Get pagination parameters from query params."""
    return PaginationParams(page=page, page_size=page_size)


def get_date_range_params(
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)")
) -> DateRangeParams:
    """Get date range parameters from query params."""
    return DateRangeParams(start_date=start_date, end_date=end_date)


def get_filter_params(
    search: Optional[str] = Query(None, description="Search term"),
    sort_by: Optional[str] = Query(None, description="Sort field"),
    sort_order: str = Query("asc", pattern="^(asc|desc)$", description="Sort order")
) -> FilterParams:
    """Get filter parameters from query params."""
    return FilterParams(search=search, sort_by=sort_by, sort_order=sort_order)