"""
Unified Validation Package
==========================

This package provides consolidated validation functionality across the API.
"""

from .unified_validator import (
    ValidationResult,
    ValidationRule,
    RequiredRule,
    EmailRule,
    PasswordRule,
    NumericRule,
    DateRule,
    FileRule,
    TenantRule,
    UnifiedValidator,
    strip_whitespace,
    lowercase,
    normalize_email,
    create_login_validator,
    create_registration_validator,
    create_transaction_validator,
    create_file_upload_validator,
    login_validator,
    registration_validator,
    transaction_validator,
    file_upload_validator,
)

__all__ = [
    "ValidationResult",
    "ValidationRule",
    "RequiredRule",
    "EmailRule", 
    "PasswordRule",
    "NumericRule",
    "DateRule",
    "FileRule",
    "TenantRule",
    "UnifiedValidator",
    "strip_whitespace",
    "lowercase",
    "normalize_email",
    "create_login_validator",
    "create_registration_validator",
    "create_transaction_validator",
    "create_file_upload_validator",
    "login_validator",
    "registration_validator",
    "transaction_validator",
    "file_upload_validator",
]
