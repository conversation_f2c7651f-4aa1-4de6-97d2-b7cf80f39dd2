"""
Unified Authentication Dependencies
==================================

FastAPI dependencies that use the unified authentication service.
This replaces the scattered auth dependencies across the codebase.

Consolidates:
- core/dependencies.py (auth-related functions)
- core/secure_dependencies.py
- domains/auth/dependencies.py
"""

from typing import Annotated, Tuple

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer

from ...core.config import settings
from ...core.database import get_db_session
from ..services.auth_service import UnifiedAuthService, AuthenticationError, AuthorizationError


# OAuth2 scheme for token extraction
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")


async def get_auth_service(db=Depends(get_db_session)) -> UnifiedAuthService:
    """Get the unified authentication service."""
    return UnifiedAuthService(db, settings)


async def get_current_user(
    token: Annotated[str, Depends(oauth2_scheme)],
    auth_service: UnifiedAuthService = Depends(get_auth_service)
) -> dict:
    """
    Get the current authenticated user.
    
    This dependency validates the JWT token and returns the current user
    from the database (no caching for security).
    """
    try:
        return await auth_service.get_current_user(token)
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: dict = Depends(get_current_user)
) -> dict:
    """
    Get the current active user.
    
    This is an alias for get_current_user since the user service
    already checks for active status.
    """
    return current_user


async def get_current_user_with_tenant(
    current_user: dict = Depends(get_current_user),
    auth_service: UnifiedAuthService = Depends(get_auth_service)
) -> Tuple[dict, dict]:
    """
    Get current user with their tenant information.
    
    Returns a tuple of (user, tenant) with fresh data from the database.
    """
    try:
        return await auth_service.get_current_user_with_tenant(current_user["id"])
    except (AuthenticationError, AuthorizationError) as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e),
        )


async def get_current_tenant_id(
    current_user: dict = Depends(get_current_user)
) -> int:
    """
    Get the current user's tenant ID.
    
    This is a convenience dependency for endpoints that only need the tenant ID.
    """
    return current_user["tenant_id"]


async def get_current_tenant(
    current_user: dict = Depends(get_current_user),
    auth_service: UnifiedAuthService = Depends(get_auth_service)
) -> dict:
    """
    Get the current user's tenant information.
    
    Returns fresh tenant data from the database.
    """
    try:
        tenant = await auth_service.get_tenant_by_id(current_user["tenant_id"])
        if not tenant:
            raise AuthorizationError("Tenant not found")
        return tenant
    except AuthorizationError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e),
        )


def require_tenant_access(resource_tenant_id: int):
    """
    Dependency factory for tenant-specific resource access.
    
    Usage:
        @app.get("/resource/{resource_id}")
        async def get_resource(
            resource_id: int,
            _: None = Depends(require_tenant_access(resource_tenant_id))
        ):
            # Resource access is validated
            pass
    """
    async def _check_tenant_access(
        current_user: dict = Depends(get_current_user),
        auth_service: UnifiedAuthService = Depends(get_auth_service)
    ):
        try:
            auth_service.validate_tenant_access(
                current_user["tenant_id"], 
                resource_tenant_id
            )
        except AuthorizationError as e:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )
    
    return _check_tenant_access


# Convenience dependencies for common auth patterns
CurrentUser = Annotated[dict, Depends(get_current_user)]
CurrentActiveUser = Annotated[dict, Depends(get_current_active_user)]
CurrentUserWithTenant = Annotated[Tuple[dict, dict], Depends(get_current_user_with_tenant)]
CurrentTenantId = Annotated[int, Depends(get_current_tenant_id)]
CurrentTenant = Annotated[dict, Depends(get_current_tenant)]
AuthService = Annotated[UnifiedAuthService, Depends(get_auth_service)]


# Legacy compatibility - these can be used to gradually migrate existing code
async def get_current_active_user_legacy(
    token: Annotated[str, Depends(oauth2_scheme)],
    auth_service: UnifiedAuthService = Depends(get_auth_service)
) -> dict:
    """Legacy compatibility for get_current_active_user."""
    return await get_current_user(token, auth_service)


async def get_current_user_with_tenant_legacy(
    current_user: dict = Depends(get_current_user),
    auth_service: UnifiedAuthService = Depends(get_auth_service)
) -> Tuple[dict, dict]:
    """Legacy compatibility for get_current_user_with_tenant."""
    return await get_current_user_with_tenant(current_user, auth_service)


async def get_current_tenant_id_legacy(
    current_user: dict = Depends(get_current_user)
) -> int:
    """Legacy compatibility for get_current_tenant_id."""
    return get_current_tenant_id(current_user)
