"""
Shared dependencies package.

This package contains FastAPI dependencies that can be used across all domains.
"""

from .auth_dependencies import (
    get_current_user,
    get_current_active_user,
    get_current_user_with_tenant,
    get_current_tenant_id,
    get_current_tenant,
    require_tenant_access,
    get_auth_service,
    # Type annotations for convenience
    CurrentUser,
    CurrentActiveUser,
    CurrentUserWithTenant,
    CurrentTenantId,
    CurrentTenant,
    AuthService,
)

__all__ = [
    # Functions
    "get_current_user",
    "get_current_active_user", 
    "get_current_user_with_tenant",
    "get_current_tenant_id",
    "get_current_tenant",
    "require_tenant_access",
    "get_auth_service",
    # Type annotations
    "CurrentUser",
    "CurrentActiveUser",
    "CurrentUserWithTenant", 
    "CurrentTenantId",
    "CurrentTenant",
    "AuthService",
]
