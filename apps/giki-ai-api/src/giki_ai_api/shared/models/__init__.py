"""
Shared Models Package
====================

Consolidated model definitions that eliminate duplicate patterns across domains.

This package provides:
- Base model classes with common fields and validation
- Standard CRUD operation schemas  
- Validation mixins for common patterns
- Specialized models for recurring use cases

Usage:
    from giki_ai_api.shared.models import CRUDModel, StatusTrackingModel
    
    class MyDomainModel(CRUDModel, StatusTrackingModel):
        # Domain-specific fields
        pass
"""

from .base_models import (
    AmountValidationMixin,
    BaseModel,
    ConfidenceValidationMixin,
    ConfigurableModel,
    CreateSchema,
    CRUDModel,
    FileProcessingModel,
    ListResponseSchema,
    MetricsModel,
    ResponseSchema,
    StatusTrackingModel,
    TenantIsolatedModel,
    TestingModel,
    TimestampedModel,
    UpdateSchema,
    UserOwnedModel,
)

__all__ = [
    # Base models
    "BaseModel",
    "TenantIsolatedModel",
    "TimestampedModel",
    "UserOwnedModel",
    "CRUDModel",

    # Specialized models
    "StatusTrackingModel",
    "MetricsModel",
    "ConfigurableModel",
    "FileProcessingModel",
    "TestingModel",

    # CRUD schemas
    "CreateSchema",
    "UpdateSchema",
    "ResponseSchema",
    "ListResponseSchema",

    # Validation mixins
    "AmountValidationMixin",
    "ConfidenceValidationMixin",
]
