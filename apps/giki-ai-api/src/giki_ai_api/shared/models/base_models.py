"""
Unified Base Models
==================

Consolidated base model classes that eliminate duplicate patterns across domains.
This consolidates common patterns found in:
- accuracy, categories, transactions, auth, reports, intelligence, onboarding, files, admin models
- Common fields: id, tenant_id, created_at, updated_at, user_id
- Common validation patterns and serialization methods
- Standard CRUD operation schemas

Base Model Hierarchy:
- BaseModel: Core Pydantic model with common config
- TenantIsolatedModel: Models that belong to a tenant
- TimestampedModel: Models with created_at/updated_at
- UserOwnedModel: Models owned by a specific user
- CRUDModel: Complete CRUD model with all common fields
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel as PydanticBaseModel, ConfigDict, Field, field_serializer


class BaseModel(PydanticBaseModel):
    """
    Base model with common configuration for all domain models.
    
    Provides:
    - Consistent model configuration
    - Common serialization patterns
    - Standard validation behavior
    """
    
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        use_enum_values=True,
        populate_by_name=True,
        str_strip_whitespace=True,
    )


class TenantIsolatedModel(BaseModel):
    """
    Base model for tenant-isolated resources.
    
    All models that belong to a specific tenant should inherit from this.
    Provides automatic tenant isolation and validation.
    """
    
    tenant_id: int = Field(..., description="Tenant ID for multi-tenancy isolation")
    
    def validate_tenant_access(self, user_tenant_id: int) -> bool:
        """Validate that the user has access to this tenant's data."""
        return self.tenant_id == user_tenant_id


class TimestampedModel(BaseModel):
    """
    Base model with automatic timestamp management.
    
    Provides created_at and updated_at fields with proper defaults.
    """
    
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Creation timestamp"
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Last update timestamp"
    )
    
    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, dt: datetime) -> str:
        """Serialize datetime to ISO format with timezone."""
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        return dt.isoformat()


class UserOwnedModel(BaseModel):
    """
    Base model for resources owned by a specific user.
    
    Provides user ownership tracking and access validation.
    """
    
    user_id: int = Field(..., description="ID of the user who owns this resource")
    
    def validate_user_access(self, requesting_user_id: int) -> bool:
        """Validate that the requesting user owns this resource."""
        return self.user_id == requesting_user_id


class CRUDModel(TenantIsolatedModel, TimestampedModel, UserOwnedModel):
    """
    Complete CRUD model with all common fields.
    
    Combines tenant isolation, timestamps, and user ownership.
    Most domain models should inherit from this.
    """
    
    id: Union[int, str, uuid.UUID] = Field(..., description="Unique identifier")
    
    class Meta:
        """Metadata for CRUD operations."""
        abstract = True


# Specialized base models for common patterns

class StatusTrackingModel(BaseModel):
    """
    Base model for resources with status tracking.
    
    Common pattern across accuracy tests, onboarding sessions, file processing, etc.
    """
    
    status: str = Field(..., description="Current status of the resource")
    started_at: Optional[datetime] = Field(None, description="When processing started")
    completed_at: Optional[datetime] = Field(None, description="When processing completed")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    
    @property
    def is_completed(self) -> bool:
        """Check if the resource processing is completed."""
        return self.completed_at is not None
    
    @property
    def is_failed(self) -> bool:
        """Check if the resource processing failed."""
        return self.error_message is not None
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Calculate processing duration in seconds."""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None


class MetricsModel(BaseModel):
    """
    Base model for resources with metrics/analytics.
    
    Common pattern across accuracy measurements, reports, analytics, etc.
    """
    
    total_count: int = Field(default=0, ge=0, description="Total count of items")
    success_count: int = Field(default=0, ge=0, description="Successful items count")
    failure_count: int = Field(default=0, ge=0, description="Failed items count")
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_count == 0:
            return 0.0
        return (self.success_count / self.total_count) * 100.0
    
    @property
    def failure_rate(self) -> float:
        """Calculate failure rate as percentage."""
        return 100.0 - self.success_rate


class ConfigurableModel(BaseModel):
    """
    Base model for resources with configuration.
    
    Common pattern across reports, tests, agents, etc.
    """
    
    name: str = Field(..., max_length=255, description="Resource name")
    description: Optional[str] = Field(None, description="Resource description")
    configuration: Dict[str, Any] = Field(
        default_factory=dict,
        description="Configuration parameters as JSON"
    )
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get a configuration value with optional default."""
        return self.configuration.get(key, default)
    
    def set_config_value(self, key: str, value: Any) -> None:
        """Set a configuration value."""
        self.configuration[key] = value


class FileProcessingModel(BaseModel):
    """
    Base model for file processing resources.
    
    Common pattern across uploads, interpretations, processing results, etc.
    """
    
    filename: str = Field(..., description="Original filename")
    file_size: Optional[int] = Field(None, ge=0, description="File size in bytes")
    file_type: Optional[str] = Field(None, description="MIME type or file extension")
    upload_id: Optional[str] = Field(None, description="Associated upload ID")
    
    @property
    def file_size_mb(self) -> Optional[float]:
        """Get file size in megabytes."""
        if self.file_size:
            return self.file_size / (1024 * 1024)
        return None


# Standard CRUD operation schemas

class CreateSchema(BaseModel):
    """Base schema for create operations."""
    
    class Meta:
        """Metadata for create operations."""
        abstract = True


class UpdateSchema(BaseModel):
    """Base schema for update operations."""
    
    class Meta:
        """Metadata for update operations."""
        abstract = True


class ResponseSchema(BaseModel):
    """Base schema for API responses."""
    
    class Meta:
        """Metadata for response schemas."""
        abstract = True


class ListResponseSchema(BaseModel):
    """Base schema for paginated list responses."""
    
    total: int = Field(..., ge=0, description="Total number of items")
    limit: int = Field(..., ge=1, description="Items per page limit")
    offset: int = Field(..., ge=0, description="Pagination offset")
    has_more: bool = Field(..., description="Whether more items are available")
    
    @property
    def current_page(self) -> int:
        """Calculate current page number (1-based)."""
        return (self.offset // self.limit) + 1
    
    @property
    def total_pages(self) -> int:
        """Calculate total number of pages."""
        return (self.total + self.limit - 1) // self.limit


# Validation mixins for common patterns

class AmountValidationMixin(BaseModel):
    """Mixin for models with financial amounts."""
    
    amount: float = Field(..., description="Financial amount")
    
    @property
    def amount_abs(self) -> float:
        """Get absolute value of amount."""
        return abs(self.amount)
    
    @property
    def is_debit(self) -> bool:
        """Check if amount represents a debit (negative)."""
        return self.amount < 0
    
    @property
    def is_credit(self) -> bool:
        """Check if amount represents a credit (positive)."""
        return self.amount > 0


class ConfidenceValidationMixin(BaseModel):
    """Mixin for models with confidence scores."""
    
    confidence: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Confidence score (0.0 to 1.0)"
    )
    
    @property
    def confidence_percentage(self) -> Optional[float]:
        """Get confidence as percentage."""
        if self.confidence is not None:
            return self.confidence * 100.0
        return None
    
    @property
    def confidence_level(self) -> str:
        """Get confidence level as string."""
        if self.confidence is None:
            return "unknown"
        elif self.confidence >= 0.8:
            return "high"
        elif self.confidence >= 0.6:
            return "medium"
        elif self.confidence >= 0.4:
            return "low"
        else:
            return "very_low"


# Additional specialized base models

class TestingModel(StatusTrackingModel, MetricsModel):
    """
    Base model for testing and validation resources.

    Common pattern across accuracy tests, onboarding validation, etc.
    Combines status tracking with metrics collection.
    """

    test_name: str = Field(..., max_length=255, description="Test name")
    test_description: Optional[str] = Field(None, description="Test description")
    sample_size: int = Field(..., ge=1, description="Number of samples tested")

    # Test execution metadata
    test_scenario: Optional[str] = Field(None, description="Test scenario identifier")
    test_data_source: Optional[str] = Field(None, description="Source of test data")

    # Results summary (extends MetricsModel)
    precision: Optional[float] = Field(None, ge=0.0, le=1.0, description="Precision score")
    recall: Optional[float] = Field(None, ge=0.0, le=1.0, description="Recall score")
    f1_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="F1 score")
    accuracy_percentage: Optional[float] = Field(None, ge=0.0, le=100.0, description="Accuracy percentage")

    @property
    def has_results(self) -> bool:
        """Check if test has completed with results."""
        return self.accuracy_percentage is not None

    @property
    def performance_grade(self) -> str:
        """Get performance grade based on accuracy."""
        if not self.accuracy_percentage:
            return "incomplete"
        elif self.accuracy_percentage >= 90:
            return "excellent"
        elif self.accuracy_percentage >= 80:
            return "good"
        elif self.accuracy_percentage >= 70:
            return "fair"
        else:
            return "poor"


# Export all base models for easy importing
__all__ = [
    "BaseModel",
    "TenantIsolatedModel",
    "TimestampedModel",
    "UserOwnedModel",
    "CRUDModel",
    "StatusTrackingModel",
    "MetricsModel",
    "ConfigurableModel",
    "FileProcessingModel",
    "TestingModel",
    "CreateSchema",
    "UpdateSchema",
    "ResponseSchema",
    "ListResponseSchema",
    "AmountValidationMixin",
    "ConfidenceValidationMixin",
]
