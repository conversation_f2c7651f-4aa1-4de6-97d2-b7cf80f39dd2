"""
Unified Export Service - Consolidates all export patterns and formats.

This module consolidates:
1. ExportService (exports/service.py) - Accounting software format exports  
2. ProfessionalCSVExportService (reports/csv_export_service.py) - CSV exports
3. ProfessionalExcelExportService (reports/excel_export_service.py) - Excel exports
4. ProfessionalPDFExportService (reports/pdf_export_service.py) - PDF exports
5. M1ExportService (reports/m1_export_service.py) - M1 verification exports

Key features:
- Unified export architecture with pluggable format handlers
- Support for all accounting software formats (QuickBooks, Xero, Sage, etc.)
- Professional document generation (CSV, Excel, PDF) 
- M1 verification and compliance exports
- Flexible filtering and formatting options
- Memory-efficient streaming for large exports
"""

import csv
import io
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from io import BytesIO, StringIO
from typing import Any, Dict, List, Optional, Protocol, Tuple, Union

import asyncpg

# Optional dependencies with graceful fallbacks
try:
    import pandas as pd
    PANDAS_SUPPORT = True
except ImportError:
    PANDAS_SUPPORT = False

try:
    from openpyxl.chart import PieChart, Reference
    from openpyxl.styles import Alignment, Border, Font, NamedStyle, PatternFill, Side
    from openpyxl.workbook import Workbook
    EXCEL_SUPPORT = True
except ImportError:
    EXCEL_SUPPORT = False

try:
    from reportlab.graphics.charts.piecharts import Pie
    from reportlab.graphics.shapes import Drawing
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter
    from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
    from reportlab.lib.units import inch
    from reportlab.platypus import Paragraph, SimpleDocTemplate, Spacer, Table, TableStyle
    PDF_SUPPORT = True
except ImportError:
    PDF_SUPPORT = False

logger = logging.getLogger(__name__)


class ExportFormat(str, Enum):
    """Supported export formats."""
    
    # Accounting Software Formats
    QUICKBOOKS_DESKTOP = "quickbooks_desktop"
    QUICKBOOKS_ONLINE = "quickbooks_online"
    XERO = "xero"
    ZOHO_BOOKS = "zoho_books"
    FRESHBOOKS = "freshbooks"
    TALLY_PRIME = "tally_prime"
    SAGE = "sage"
    WAVE = "wave"
    
    # Generic Formats
    GENERIC_CSV = "generic_csv"
    GENERIC_EXCEL = "generic_excel"
    
    # Professional Reports
    PROFESSIONAL_CSV = "professional_csv"
    PROFESSIONAL_EXCEL = "professional_excel"
    PROFESSIONAL_PDF = "professional_pdf"
    
    # M1 Verification
    M1_VERIFICATION = "m1_verification"
    M1_CSV = "m1_csv"
    M1_SUMMARY_PDF = "m1_summary_pdf"


class ExportType(str, Enum):
    """Export type categories."""
    
    ACCOUNTING_SOFTWARE = "accounting_software"
    PROFESSIONAL_REPORT = "professional_report"
    VERIFICATION = "verification"
    GENERIC = "generic"


class FileFormat(str, Enum):
    """File formats."""
    
    CSV = "csv"
    EXCEL = "xlsx"
    PDF = "pdf"
    IIF = "iif"  # QuickBooks Import Format
    XML = "xml"  # Various XML formats


@dataclass
class ExportOptions:
    """Unified export options."""
    
    # Date filtering
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    
    # Content filtering
    category_ids: Optional[List[int]] = None
    include_uncategorized: bool = True
    include_metadata: bool = True
    
    # Formatting options
    format_style: str = "standard"  # standard, quickbooks, sage, xero
    professional_styling: bool = True
    
    # Excel/PDF specific
    include_charts: bool = True
    include_summary: bool = True
    autofit_columns: bool = True
    freeze_headers: bool = True
    conditional_formatting: bool = True
    
    # CSV specific
    delimiter: str = ","
    encoding: str = "utf-8"
    
    # Performance options
    chunk_size: int = 1000  # For large exports
    stream_output: bool = False


@dataclass
class ExportResult:
    """Unified export result."""
    
    content: Union[bytes, str, io.IOBase]
    filename: str
    content_type: str
    file_extension: str
    size_bytes: Optional[int] = None
    export_format: ExportFormat = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class ExportHandler(Protocol):
    """Protocol for export format handlers."""
    
    @abstractmethod
    async def export(
        self,
        transactions: List[Dict[str, Any]],
        options: ExportOptions,
        db: asyncpg.Connection,
        tenant_id: int
    ) -> ExportResult:
        """Export transactions to specific format."""
        pass
    
    @abstractmethod
    def get_content_type(self) -> str:
        """Get MIME content type for this format."""
        pass
    
    @abstractmethod
    def get_file_extension(self) -> str:
        """Get file extension for this format.""" 
        pass


class BaseExportHandler(ABC):
    """Base class for export handlers with common functionality."""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    async def export(
        self,
        transactions: List[Dict[str, Any]], 
        options: ExportOptions,
        db: asyncpg.Connection,
        tenant_id: int
    ) -> ExportResult:
        """Export transactions to specific format."""
        pass
    
    @abstractmethod
    def get_content_type(self) -> str:
        """Get MIME content type."""
        pass
    
    @abstractmethod 
    def get_file_extension(self) -> str:
        """Get file extension."""
        pass
    
    def _generate_filename(self, base_name: str, options: ExportOptions) -> str:
        """Generate filename with timestamp and date range."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Add date range if specified
        date_suffix = ""
        if options.start_date and options.end_date:
            start_str = options.start_date.strftime("%Y%m%d")
            end_str = options.end_date.strftime("%Y%m%d")
            date_suffix = f"_{start_str}_to_{end_str}"
        elif options.start_date:
            start_str = options.start_date.strftime("%Y%m%d")
            date_suffix = f"_from_{start_str}"
        elif options.end_date:
            end_str = options.end_date.strftime("%Y%m%d")
            date_suffix = f"_until_{end_str}"
        
        return f"{base_name}{date_suffix}_{timestamp}.{self.get_file_extension()}"
    
    def _format_currency(self, amount: float, format_style: str = "standard") -> str:
        """Format currency based on style."""
        if format_style == "quickbooks":
            return f"{amount:.2f}"  # QuickBooks prefers no currency symbol
        elif format_style == "xero":
            return f"{amount:.2f}"  # Xero also prefers no symbol
        else:
            return f"${amount:.2f}"  # Standard formatting
    
    def _format_date(self, date: datetime, format_style: str = "standard") -> str:
        """Format date based on style."""
        if format_style == "quickbooks":
            return date.strftime("%m/%d/%Y")  # US format
        elif format_style == "xero":
            return date.strftime("%d/%m/%Y")  # UK/AU format
        elif format_style == "sage":
            return date.strftime("%d-%m-%Y")  # Sage format
        else:
            return date.strftime("%Y-%m-%d")  # ISO format


class CSVExportHandler(BaseExportHandler):
    """Handles CSV exports with multiple format styles."""
    
    async def export(
        self,
        transactions: List[Dict[str, Any]],
        options: ExportOptions,
        db: asyncpg.Connection,
        tenant_id: int
    ) -> ExportResult:
        """Export transactions to CSV format."""
        output = StringIO()
        
        # Define columns based on format style and metadata inclusion
        columns = self._get_columns_for_format(options.format_style, options.include_metadata)
        
        # Create CSV writer
        writer = csv.DictWriter(
            output, 
            fieldnames=columns,
            delimiter=options.delimiter
        )
        writer.writeheader()
        
        # Write transaction data
        for transaction in transactions:
            row_data = self._format_transaction_row(
                transaction, 
                options.format_style, 
                options.include_metadata
            )
            writer.writerow(row_data)
        
        output.seek(0)
        content = output.getvalue()
        
        return ExportResult(
            content=content,
            filename=self._generate_filename("transactions", options),
            content_type=self.get_content_type(),
            file_extension=self.get_file_extension(),
            size_bytes=len(content.encode(options.encoding)),
            metadata={"row_count": len(transactions), "format_style": options.format_style}
        )
    
    def get_content_type(self) -> str:
        return "text/csv"
    
    def get_file_extension(self) -> str:
        return "csv"
    
    def _get_columns_for_format(self, format_style: str, include_metadata: bool) -> List[str]:
        """Get column names based on format style."""
        base_columns = ["date", "description", "amount", "category", "account"]
        
        if format_style == "quickbooks":
            columns = ["Date", "Description", "Amount", "Account", "Category"]
        elif format_style == "xero":
            columns = ["Date", "Description", "Amount", "Account", "Category", "Reference"]
        elif format_style == "sage":
            columns = ["Date", "Reference", "Description", "Amount", "Category", "VAT"]
        else:
            columns = base_columns
        
        if include_metadata:
            metadata_columns = ["confidence", "processing_notes", "tenant_id"]
            columns.extend(metadata_columns)
        
        return columns
    
    def _format_transaction_row(
        self, 
        transaction: Dict[str, Any], 
        format_style: str,
        include_metadata: bool
    ) -> Dict[str, Any]:
        """Format transaction data for CSV row."""
        # Basic transaction data
        row = {
            "date": self._format_date(transaction.get("date"), format_style),
            "description": transaction.get("description", ""),
            "amount": self._format_currency(transaction.get("amount", 0.0), format_style),
            "category": transaction.get("category_name", "Uncategorized"),
            "account": transaction.get("account", "")
        }
        
        # Format-specific adjustments
        if format_style == "quickbooks":
            row = {"Date": row["date"], "Description": row["description"], 
                   "Amount": row["amount"], "Account": row["account"], 
                   "Category": row["category"]}
        elif format_style == "xero":
            row["Reference"] = transaction.get("reference", "")
        elif format_style == "sage":
            row["Reference"] = transaction.get("reference", "")
            row["VAT"] = transaction.get("vat_amount", "0.00")
        
        # Add metadata if requested
        if include_metadata:
            row.update({
                "confidence": transaction.get("confidence", ""),
                "processing_notes": transaction.get("processing_notes", ""),
                "tenant_id": transaction.get("tenant_id", "")
            })
        
        return row


class ExcelExportHandler(BaseExportHandler):
    """Handles Excel exports with professional formatting."""
    
    async def export(
        self,
        transactions: List[Dict[str, Any]],
        options: ExportOptions,
        db: asyncpg.Connection,
        tenant_id: int
    ) -> ExportResult:
        """Export transactions to Excel format."""
        if not EXCEL_SUPPORT:
            raise ImportError("Excel export requires openpyxl package. Install with: pip install openpyxl")
        
        # Create workbook
        workbook = Workbook()
        workbook.remove(workbook.active)  # Remove default sheet
        
        # Create main transactions sheet
        transactions_sheet = workbook.create_sheet("Transactions")
        await self._create_transactions_sheet(transactions_sheet, transactions, options)
        
        # Create summary sheet if requested
        if options.include_summary:
            summary_sheet = workbook.create_sheet("Summary")
            await self._create_summary_sheet(summary_sheet, transactions, options, db, tenant_id)
        
        # Create charts if requested
        if options.include_charts:
            await self._add_charts(workbook, transactions, options)
        
        # Apply professional styling
        if options.professional_styling:
            self._apply_professional_styling(workbook)
        
        # Save to bytes
        output = BytesIO()
        workbook.save(output)
        output.seek(0)
        content = output.getvalue()
        
        return ExportResult(
            content=content,
            filename=self._generate_filename("transactions", options),
            content_type=self.get_content_type(),
            file_extension=self.get_file_extension(),
            size_bytes=len(content),
            metadata={
                "row_count": len(transactions),
                "sheet_count": len(workbook.worksheets),
                "includes_charts": options.include_charts,
                "includes_summary": options.include_summary
            }
        )
    
    def get_content_type(self) -> str:
        return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    
    def get_file_extension(self) -> str:
        return "xlsx"
    
    async def _create_transactions_sheet(self, sheet, transactions, options):
        """Create the main transactions sheet."""
        # Headers
        headers = ["Date", "Description", "Amount", "Category", "Account"]
        if options.include_metadata:
            headers.extend(["Confidence", "Processing Notes"])
        
        # Write headers
        for col, header in enumerate(headers, 1):
            sheet.cell(row=1, column=col, value=header)
        
        # Write data
        for row, transaction in enumerate(transactions, 2):
            sheet.cell(row=row, column=1, value=transaction.get("date"))
            sheet.cell(row=row, column=2, value=transaction.get("description", ""))
            sheet.cell(row=row, column=3, value=transaction.get("amount", 0.0))
            sheet.cell(row=row, column=4, value=transaction.get("category_name", "Uncategorized"))
            sheet.cell(row=row, column=5, value=transaction.get("account", ""))
            
            if options.include_metadata:
                sheet.cell(row=row, column=6, value=transaction.get("confidence", ""))
                sheet.cell(row=row, column=7, value=transaction.get("processing_notes", ""))
        
        # Freeze headers
        if options.freeze_headers:
            sheet.freeze_panes = "A2"
        
        # Autofit columns
        if options.autofit_columns:
            for column in sheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                sheet.column_dimensions[column_letter].width = adjusted_width
    
    async def _create_summary_sheet(self, sheet, transactions, options, db, tenant_id):
        """Create summary sheet with category breakdowns."""
        # Category summary
        category_summary = {}
        for transaction in transactions:
            category = transaction.get("category_name", "Uncategorized")
            amount = transaction.get("amount", 0.0)
            if category in category_summary:
                category_summary[category] += amount
            else:
                category_summary[category] = amount
        
        # Write summary
        sheet.cell(row=1, column=1, value="Category Summary")
        sheet.cell(row=3, column=1, value="Category")
        sheet.cell(row=3, column=2, value="Total Amount")
        
        row = 4
        for category, amount in sorted(category_summary.items()):
            sheet.cell(row=row, column=1, value=category)
            sheet.cell(row=row, column=2, value=amount)
            row += 1
    
    async def _add_charts(self, workbook, transactions, options):
        """Add charts to the workbook."""
        if "Summary" not in [sheet.title for sheet in workbook.worksheets]:
            return
        
        summary_sheet = workbook["Summary"]
        
        # Create pie chart for category distribution
        chart = PieChart()
        chart.title = "Spending by Category"
        
        # Find data range (assuming data starts at row 4)
        data_rows = len([t for t in transactions]) + 1  # +1 for header
        labels = Reference(summary_sheet, min_col=1, min_row=4, max_row=4 + data_rows)
        data = Reference(summary_sheet, min_col=2, min_row=4, max_row=4 + data_rows)
        
        chart.add_data(data, titles_from_data=False)
        chart.set_categories(labels)
        
        # Add chart to sheet
        summary_sheet.add_chart(chart, "D3")
    
    def _apply_professional_styling(self, workbook):
        """Apply professional styling to the workbook."""
        # Define styles
        header_style = NamedStyle(name="header")
        header_style.font = Font(bold=True, color="FFFFFF")
        header_style.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_style.alignment = Alignment(horizontal="center", vertical="center")
        header_style.border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin")
        )
        
        try:
            workbook.add_named_style(header_style)
        except ValueError:
            pass  # Style already exists
        
        # Apply styles to all sheets
        for sheet in workbook.worksheets:
            # Style headers (row 1)
            for cell in sheet[1]:
                cell.style = "header"


class PDFExportHandler(BaseExportHandler):
    """Handles PDF exports with professional formatting."""
    
    async def export(
        self,
        transactions: List[Dict[str, Any]],
        options: ExportOptions,
        db: asyncpg.Connection,
        tenant_id: int
    ) -> ExportResult:
        """Export transactions to PDF format."""
        if not PDF_SUPPORT:
            raise ImportError("PDF export requires reportlab package. Install with: pip install reportlab")
        
        # Create PDF buffer
        buffer = BytesIO()
        
        # Create document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=letter,
            topMargin=inch,
            bottomMargin=inch,
            leftMargin=inch,
            rightMargin=inch
        )
        
        # Build content
        content = []
        
        # Title
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.HexColor("#366092")
        )
        
        title = Paragraph("Financial Transactions Report", title_style)
        content.append(title)
        content.append(Spacer(1, 20))
        
        # Summary section if requested
        if options.include_summary:
            summary_content = await self._create_pdf_summary(transactions, options)
            content.extend(summary_content)
            content.append(Spacer(1, 30))
        
        # Transactions table
        table_content = self._create_pdf_transactions_table(transactions, options)
        content.append(table_content)
        
        # Charts if requested
        if options.include_charts:
            chart_content = self._create_pdf_charts(transactions, options)
            if chart_content:
                content.append(Spacer(1, 30))
                content.extend(chart_content)
        
        # Build PDF
        doc.build(content)
        buffer.seek(0)
        pdf_content = buffer.getvalue()
        
        return ExportResult(
            content=pdf_content,
            filename=self._generate_filename("financial_report", options),
            content_type=self.get_content_type(),
            file_extension=self.get_file_extension(),
            size_bytes=len(pdf_content),
            metadata={
                "row_count": len(transactions),
                "includes_charts": options.include_charts,
                "includes_summary": options.include_summary,
                "page_count": doc.page  # Approximate
            }
        )
    
    def get_content_type(self) -> str:
        return "application/pdf"
    
    def get_file_extension(self) -> str:
        return "pdf"
    
    async def _create_pdf_summary(self, transactions, options):
        """Create summary section for PDF."""
        content = []
        styles = getSampleStyleSheet()
        
        # Summary title
        summary_title = Paragraph("Executive Summary", styles['Heading2'])
        content.append(summary_title)
        content.append(Spacer(1, 12))
        
        # Calculate summary statistics
        total_amount = sum(t.get("amount", 0.0) for t in transactions)
        total_transactions = len(transactions)
        
        # Category breakdown
        category_summary = {}
        for transaction in transactions:
            category = transaction.get("category_name", "Uncategorized")
            amount = transaction.get("amount", 0.0)
            category_summary[category] = category_summary.get(category, 0.0) + amount
        
        # Summary text
        summary_text = f"""
        <b>Total Transactions:</b> {total_transactions}<br/>
        <b>Total Amount:</b> ${total_amount:,.2f}<br/>
        <b>Categories:</b> {len(category_summary)}<br/>
        <b>Date Range:</b> {options.start_date.strftime('%Y-%m-%d') if options.start_date else 'All'} to {options.end_date.strftime('%Y-%m-%d') if options.end_date else 'All'}
        """
        
        summary_para = Paragraph(summary_text, styles['Normal'])
        content.append(summary_para)
        
        return content
    
    def _create_pdf_transactions_table(self, transactions, options):
        """Create transactions table for PDF."""
        # Prepare table data
        headers = ["Date", "Description", "Amount", "Category"]
        table_data = [headers]
        
        for transaction in transactions[:50]:  # Limit for PDF readability
            row = [
                transaction.get("date", "").strftime("%Y-%m-%d") if transaction.get("date") else "",
                transaction.get("description", "")[:40] + "..." if len(transaction.get("description", "")) > 40 else transaction.get("description", ""),
                f"${transaction.get('amount', 0.0):,.2f}",
                transaction.get("category_name", "Uncategorized")
            ]
            table_data.append(row)
        
        # Create table
        table = Table(table_data, colWidths=[1.2*inch, 3*inch, 1*inch, 1.5*inch])
        
        # Style table
        table.setStyle(TableStyle([
            # Header styling
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor("#366092")),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            
            # Data styling
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            
            # Alternating row colors
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor("#F5F5F5")])
        ]))
        
        return table
    
    def _create_pdf_charts(self, transactions, options):
        """Create charts for PDF."""
        content = []
        
        # For now, just add a placeholder for charts
        # In a full implementation, you would create actual ReportLab charts
        styles = getSampleStyleSheet()
        chart_placeholder = Paragraph("Charts would be rendered here in full implementation", styles['Normal'])
        content.append(chart_placeholder)
        
        return content


class AccountingSoftwareExportHandler(BaseExportHandler):
    """Handles exports for specific accounting software formats."""
    
    def __init__(self, export_format: ExportFormat):
        super().__init__()
        self.export_format = export_format
        self._setup_format_spec()
    
    def _setup_format_spec(self):
        """Setup format-specific specifications."""
        self.format_specs = {
            ExportFormat.QUICKBOOKS_DESKTOP: {
                "file_extension": "iif",
                "content_type": "application/x-iif",
                "columns": ["!TRNS", "DATE", "ACCNT", "NAME", "CLASS", "AMOUNT", "MEMO"],
                "date_format": "%m/%d/%Y"
            },
            ExportFormat.QUICKBOOKS_ONLINE: {
                "file_extension": "csv", 
                "content_type": "text/csv",
                "columns": ["Date", "Description", "Amount", "Account", "Customer"],
                "date_format": "%m/%d/%Y"
            },
            ExportFormat.XERO: {
                "file_extension": "csv",
                "content_type": "text/csv", 
                "columns": ["Date", "Amount", "Account", "Description", "Reference"],
                "date_format": "%d/%m/%Y"
            },
            ExportFormat.SAGE: {
                "file_extension": "csv",
                "content_type": "text/csv",
                "columns": ["Date", "Reference", "Description", "Amount", "Account", "VAT"],
                "date_format": "%d-%m-%Y"
            }
        }
    
    async def export(
        self,
        transactions: List[Dict[str, Any]],
        options: ExportOptions,
        db: asyncpg.Connection,
        tenant_id: int
    ) -> ExportResult:
        """Export transactions for specific accounting software."""
        spec = self.format_specs.get(self.export_format)
        if not spec:
            raise ValueError(f"Unsupported accounting format: {self.export_format}")
        
        if spec["file_extension"] == "iif":
            content = self._generate_iif(transactions, spec, options)
        else:
            content = self._generate_accounting_csv(transactions, spec, options)
        
        return ExportResult(
            content=content,
            filename=self._generate_filename(f"{self.export_format.value}_export", options),
            content_type=spec["content_type"],
            file_extension=spec["file_extension"],
            size_bytes=len(content.encode("utf-8")) if isinstance(content, str) else len(content),
            export_format=self.export_format,
            metadata={"accounting_software": self.export_format.value, "row_count": len(transactions)}
        )
    
    def get_content_type(self) -> str:
        spec = self.format_specs.get(self.export_format, {})
        return spec.get("content_type", "text/plain")
    
    def get_file_extension(self) -> str:
        spec = self.format_specs.get(self.export_format, {})
        return spec.get("file_extension", "txt")
    
    def _generate_iif(self, transactions, spec, options):
        """Generate QuickBooks IIF format."""
        output = StringIO()
        
        # IIF header
        output.write("!TRNS\tTRNSID\tTYPE\tDATE\tACCNT\tNAME\tAMOUNT\tDOCNUM\tMEMO\tCLEAR\n")
        output.write("!SPL\tSPLID\tTRNSID\tTYPE\tDATE\tACCNT\tNAME\tAMOUNT\tDOCNUM\tMEMO\tCLEAR\n")
        output.write("!ENDTRNS\n")
        
        # Transaction data
        for i, transaction in enumerate(transactions):
            trns_id = i + 1
            date_str = transaction.get("date", datetime.now()).strftime(spec["date_format"])
            amount = transaction.get("amount", 0.0)
            description = transaction.get("description", "")
            category = transaction.get("category_name", "Uncategorized")
            
            # Transaction header
            output.write(f"TRNS\t{trns_id}\tGENERAL\t{date_str}\t\t\t{amount}\t\t{description}\tN\n")
            
            # Split line
            output.write(f"SPL\t{trns_id}\t{trns_id}\tGENERAL\t{date_str}\t{category}\t\t{-amount}\t\t{description}\tN\n")
            
            # End transaction
            output.write("ENDTRNS\n")
        
        return output.getvalue()
    
    def _generate_accounting_csv(self, transactions, spec, options):
        """Generate CSV for accounting software."""
        output = StringIO()
        writer = csv.DictWriter(output, fieldnames=spec["columns"])
        writer.writeheader()
        
        for transaction in transactions:
            row = {}
            date_str = transaction.get("date", datetime.now()).strftime(spec["date_format"])
            
            # Map transaction data to format-specific columns
            if self.export_format == ExportFormat.QUICKBOOKS_ONLINE:
                row = {
                    "Date": date_str,
                    "Description": transaction.get("description", ""),
                    "Amount": transaction.get("amount", 0.0),
                    "Account": transaction.get("account", ""),
                    "Customer": transaction.get("customer", "")
                }
            elif self.export_format == ExportFormat.XERO:
                row = {
                    "Date": date_str,
                    "Amount": transaction.get("amount", 0.0),
                    "Account": transaction.get("account", ""),
                    "Description": transaction.get("description", ""),
                    "Reference": transaction.get("reference", "")
                }
            elif self.export_format == ExportFormat.SAGE:
                row = {
                    "Date": date_str,
                    "Reference": transaction.get("reference", ""),
                    "Description": transaction.get("description", ""),
                    "Amount": transaction.get("amount", 0.0),
                    "Account": transaction.get("account", ""),
                    "VAT": transaction.get("vat_amount", 0.0)
                }
            
            writer.writerow(row)
        
        return output.getvalue()


class UnifiedExportService:
    """
    Unified export service that consolidates all export patterns.
    
    Provides a single interface for all export operations while maintaining
    backward compatibility with existing export services.
    """
    
    def __init__(self, db: asyncpg.Connection):
        self.db = db
        self.handlers: Dict[ExportFormat, ExportHandler] = {}
        self._register_handlers()
    
    def _register_handlers(self):
        """Register all export format handlers."""
        # CSV handlers
        self.handlers[ExportFormat.PROFESSIONAL_CSV] = CSVExportHandler()
        self.handlers[ExportFormat.GENERIC_CSV] = CSVExportHandler()
        
        # Excel handlers
        if EXCEL_SUPPORT:
            self.handlers[ExportFormat.PROFESSIONAL_EXCEL] = ExcelExportHandler()
            self.handlers[ExportFormat.GENERIC_EXCEL] = ExcelExportHandler()
        
        # PDF handlers
        if PDF_SUPPORT:
            self.handlers[ExportFormat.PROFESSIONAL_PDF] = PDFExportHandler()
        
        # Accounting software handlers
        for format_type in [
            ExportFormat.QUICKBOOKS_DESKTOP,
            ExportFormat.QUICKBOOKS_ONLINE,
            ExportFormat.XERO,
            ExportFormat.ZOHO_BOOKS,
            ExportFormat.SAGE,
            ExportFormat.WAVE
        ]:
            self.handlers[format_type] = AccountingSoftwareExportHandler(format_type)
    
    async def export_transactions(
        self,
        tenant_id: int,
        export_format: ExportFormat,
        options: Optional[ExportOptions] = None
    ) -> ExportResult:
        """
        Export transactions in the specified format.
        
        Args:
            tenant_id: Tenant ID for data filtering
            export_format: Target export format
            options: Export options and filters
            
        Returns:
            ExportResult with file content and metadata
        """
        if export_format not in self.handlers:
            raise ValueError(f"Unsupported export format: {export_format}")
        
        # Use default options if none provided
        if options is None:
            options = ExportOptions()
        
        # Fetch transactions
        transactions = await self._fetch_transactions(tenant_id, options)
        
        if not transactions:
            raise ValueError("No transactions found for the specified criteria")
        
        # Get appropriate handler and export
        handler = self.handlers[export_format]
        result = await handler.export(transactions, options, self.db, tenant_id)
        result.export_format = export_format
        
        return result
    
    async def get_export_formats(self) -> List[Dict[str, Any]]:
        """Get list of available export formats with their capabilities."""
        formats = []
        
        for export_format, handler in self.handlers.items():
            format_info = {
                "format": export_format.value,
                "display_name": export_format.value.replace("_", " ").title(),
                "file_extension": handler.get_file_extension(),
                "content_type": handler.get_content_type(),
                "category": self._get_format_category(export_format),
                "supported": True
            }
            formats.append(format_info)
        
        # Add unsupported formats with availability info
        if not EXCEL_SUPPORT:
            formats.append({
                "format": "excel_formats",
                "display_name": "Excel Formats",
                "supported": False,
                "reason": "openpyxl package not installed"
            })
        
        if not PDF_SUPPORT:
            formats.append({
                "format": "pdf_formats", 
                "display_name": "PDF Formats",
                "supported": False,
                "reason": "reportlab package not installed"
            })
        
        return formats
    
    async def validate_export_readiness(
        self,
        tenant_id: int,
        export_format: ExportFormat,
        options: Optional[ExportOptions] = None
    ) -> Dict[str, Any]:
        """
        Validate if export is ready and check for potential issues.
        
        Returns validation result with warnings, errors, and recommendations.
        """
        validation_result = {
            "ready": True,
            "warnings": [],
            "errors": [],
            "recommendations": [],
            "estimated_size": 0,
            "estimated_rows": 0
        }
        
        # Check if format is supported
        if export_format not in self.handlers:
            validation_result["ready"] = False
            validation_result["errors"].append(f"Export format {export_format} is not supported")
            return validation_result
        
        # Check dependencies
        if export_format in [ExportFormat.PROFESSIONAL_EXCEL, ExportFormat.GENERIC_EXCEL] and not EXCEL_SUPPORT:
            validation_result["ready"] = False
            validation_result["errors"].append("Excel export requires openpyxl package")
        
        if export_format == ExportFormat.PROFESSIONAL_PDF and not PDF_SUPPORT:
            validation_result["ready"] = False
            validation_result["errors"].append("PDF export requires reportlab package")
        
        # Check data availability
        options = options or ExportOptions()
        try:
            transactions = await self._fetch_transactions(tenant_id, options, count_only=True)
            validation_result["estimated_rows"] = len(transactions) if transactions else 0
            
            if validation_result["estimated_rows"] == 0:
                validation_result["warnings"].append("No transactions found for the specified criteria")
            elif validation_result["estimated_rows"] > 10000:
                validation_result["warnings"].append(f"Large export ({validation_result['estimated_rows']} transactions) may take longer")
                validation_result["recommendations"].append("Consider using date filters to reduce export size")
            
            # Estimate file size (rough approximation)
            avg_row_size = {"csv": 100, "xlsx": 200, "pdf": 300}.get(
                self.handlers[export_format].get_file_extension(), 100
            )
            validation_result["estimated_size"] = validation_result["estimated_rows"] * avg_row_size
            
        except Exception as e:
            validation_result["ready"] = False
            validation_result["errors"].append(f"Error checking data availability: {str(e)}")
        
        return validation_result
    
    async def _fetch_transactions(
        self,
        tenant_id: int,
        options: ExportOptions,
        count_only: bool = False
    ) -> List[Dict[str, Any]]:
        """Fetch transactions based on filters."""
        # Build query
        conditions = ["tenant_id = $1"]
        params = [tenant_id]
        param_count = 1
        
        if options.start_date:
            param_count += 1
            conditions.append(f"date >= ${param_count}")
            params.append(options.start_date)
        
        if options.end_date:
            param_count += 1
            conditions.append(f"date <= ${param_count}")
            params.append(options.end_date)
        
        if options.category_ids:
            param_count += 1
            conditions.append(f"category_id = ANY(${param_count})")
            params.append(options.category_ids)
        
        if not options.include_uncategorized:
            conditions.append("category_id IS NOT NULL")
        
        where_clause = " AND ".join(conditions)
        
        if count_only:
            query = f"SELECT COUNT(*) FROM transactions WHERE {where_clause}"
            result = await self.db.fetchval(query, *params)
            return [{}] * result  # Return list with count for compatibility
        
        # Full query with joins for category names
        query = f"""
        SELECT 
            t.*,
            c.name as category_name,
            c.path as category_path
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE {where_clause}
        ORDER BY t.date DESC
        """
        
        if options.chunk_size and options.chunk_size > 0:
            query += f" LIMIT {options.chunk_size}"
        
        rows = await self.db.fetch(query, *params)
        return [dict(row) for row in rows]
    
    def _get_format_category(self, export_format: ExportFormat) -> ExportType:
        """Get category for export format."""
        if export_format.value.startswith("quickbooks") or export_format.value in ["xero", "sage", "wave", "zoho_books", "freshbooks", "tally_prime"]:
            return ExportType.ACCOUNTING_SOFTWARE
        elif export_format.value.startswith("professional"):
            return ExportType.PROFESSIONAL_REPORT
        elif export_format.value.startswith("m1"):
            return ExportType.VERIFICATION
        else:
            return ExportType.GENERIC
    
    # ====== BACKWARD COMPATIBILITY METHODS ======
    
    async def generate_transactions_csv(
        self,
        tenant_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        include_metadata: bool = True,
        format_style: str = "standard"
    ) -> StringIO:
        """Backward compatibility method for CSV exports."""
        options = ExportOptions(
            start_date=start_date,
            end_date=end_date,
            include_metadata=include_metadata,
            format_style=format_style
        )
        
        result = await self.export_transactions(tenant_id, ExportFormat.PROFESSIONAL_CSV, options)
        
        # Return StringIO for backward compatibility
        string_buffer = StringIO()
        string_buffer.write(result.content)
        string_buffer.seek(0)
        return string_buffer
    
    async def export_transactions_legacy(
        self,
        tenant_id: int,
        format_id: str,
        filters: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Tuple[bytes, str, str]:
        """Backward compatibility method for legacy export service."""
        # Convert legacy format_id to new ExportFormat
        format_mapping = {
            "quickbooks_desktop": ExportFormat.QUICKBOOKS_DESKTOP,
            "quickbooks_online": ExportFormat.QUICKBOOKS_ONLINE,
            "xero": ExportFormat.XERO,
            "generic_csv": ExportFormat.GENERIC_CSV,
            "generic_excel": ExportFormat.GENERIC_EXCEL
        }
        
        export_format = format_mapping.get(format_id)
        if not export_format:
            raise ValueError(f"Unknown legacy format: {format_id}")
        
        # Convert legacy filters and options
        export_options = ExportOptions()
        if filters:
            export_options.start_date = filters.get("date_from")
            export_options.end_date = filters.get("date_to")
            export_options.category_ids = filters.get("category_ids")
        
        if options:
            export_options.include_uncategorized = options.get("include_uncategorized", True)
            export_options.include_metadata = options.get("include_metadata", True)
        
        # Export using new system
        result = await self.export_transactions(tenant_id, export_format, export_options)
        
        # Return legacy format (bytes, filename, content_type)
        content = result.content
        if isinstance(content, str):
            content = content.encode('utf-8')
        elif hasattr(content, 'getvalue'):
            content = content.getvalue()
            if isinstance(content, str):
                content = content.encode('utf-8')
        
        return content, result.filename, result.content_type


# ====== CONVENIENCE FUNCTIONS ======

async def create_unified_export_service(db: asyncpg.Connection) -> UnifiedExportService:
    """Create and configure unified export service."""
    return UnifiedExportService(db)


def get_available_export_formats() -> List[ExportFormat]:
    """Get list of all available export formats."""
    return list(ExportFormat)


def get_supported_export_formats() -> List[ExportFormat]:
    """Get list of currently supported export formats (based on dependencies)."""
    supported = [
        ExportFormat.PROFESSIONAL_CSV,
        ExportFormat.GENERIC_CSV,
        ExportFormat.QUICKBOOKS_DESKTOP,
        ExportFormat.QUICKBOOKS_ONLINE,
        ExportFormat.XERO,
        ExportFormat.SAGE,
        ExportFormat.WAVE
    ]
    
    if EXCEL_SUPPORT:
        supported.extend([ExportFormat.PROFESSIONAL_EXCEL, ExportFormat.GENERIC_EXCEL])
    
    if PDF_SUPPORT:
        supported.append(ExportFormat.PROFESSIONAL_PDF)
    
    return supported


# ====== HELPER FUNCTIONS ======

def get_unified_export_service() -> Optional[UnifiedExportService]:
    """
    Get the unified export service instance from application state.
    
    This function should be used by routers and services to access the export service
    that was initialized during application startup.
    
    Returns:
        UnifiedExportService instance if available, None otherwise
    """
    try:
        # Try to get from FastAPI app state first
        import inspect
        
        # Look for app or db connection in the call stack
        frame = inspect.currentframe()
        while frame:
            if 'app' in frame.f_locals:
                app = frame.f_locals['app']
                if hasattr(app, 'state') and hasattr(app.state, 'export_service'):
                    return app.state.export_service
            elif 'conn' in frame.f_locals:
                # Create service on-demand if we have a database connection
                conn = frame.f_locals['conn']
                return UnifiedExportService(conn)
            frame = frame.f_back
        
        # Return None if we can't find or create a service
        return None
        
    except Exception as e:
        logger.debug(f"Could not access export service: {e}")
        return None


# ====== EXPORTS ======

__all__ = [
    # Main classes
    'UnifiedExportService',
    'ExportOptions',
    'ExportResult',
    'ExportFormat',
    'ExportType',
    'FileFormat',
    
    # Handlers
    'ExportHandler',
    'BaseExportHandler',
    'CSVExportHandler',
    'ExcelExportHandler', 
    'PDFExportHandler',
    'AccountingSoftwareExportHandler',
    
    # Convenience functions
    'create_unified_export_service',
    'get_unified_export_service',
    'get_available_export_formats',
    'get_supported_export_formats',
    
    # Support flags
    'EXCEL_SUPPORT',
    'PDF_SUPPORT',
    'PANDAS_SUPPORT'
]