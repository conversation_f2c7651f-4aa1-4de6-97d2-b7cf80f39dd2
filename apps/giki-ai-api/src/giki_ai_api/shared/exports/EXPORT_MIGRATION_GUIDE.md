# Export Services Migration Guide

## Overview

This guide explains how to migrate from the multiple specialized export services to the unified `UnifiedExportService`. The consolidation eliminates ~2,000+ lines of duplicate code while providing enhanced functionality and backward compatibility.

## What Was Consolidated

### 1. Accounting Software Export Service (`domains/exports/service.py`)
- **400+ lines** of accounting format exports (QuickBooks, Xero, Sage, etc.)
- Format-specific transformations and validations
- CSV, Excel, IIF, and XML generation

### 2. Professional CSV Export Service (`domains/reports/csv_export_service.py`)
- **515+ lines** of CSV generation with multiple format styles
- Transaction and category summary exports
- Professional formatting options

### 3. Professional Excel Export Service (`domains/reports/excel_export_service.py`)
- **600+ lines** of Excel generation with advanced formatting
- Charts, formulas, and professional styling
- Multi-sheet workbooks with summary data

### 4. Professional PDF Export Service (`domains/reports/pdf_export_service.py`)
- **500+ lines** of PDF generation with enterprise-grade formatting
- Charts, tables, and publication-ready reports
- Professional styling and layout

### 5. M1 Export Service (`domains/reports/m1_export_service.py`)
- **300+ lines** of M1 verification and compliance exports
- Specialized validation and summary generation
- Multiple M1 format variants

## Migration Steps

### Step 1: Update Imports

#### Replace Individual Service Imports
```python
# OLD - Replace these imports
from giki_ai_api.domains.exports.service import ExportService
from giki_ai_api.domains.reports.csv_export_service import ProfessionalCSVExportService
from giki_ai_api.domains.reports.excel_export_service import ProfessionalExcelExportService
from giki_ai_api.domains.reports.pdf_export_service import ProfessionalPDFExportService
from giki_ai_api.domains.reports.m1_export_service import M1ExportService

# NEW - Use unified import
from giki_ai_api.shared.exports.unified_export_service import (
    UnifiedExportService,
    ExportFormat,
    ExportOptions,
    ExportResult,
    create_unified_export_service
)
```

### Step 2: Update Service Initialization

#### Replace Multiple Service Instances
```python
# OLD - Multiple service instances
export_service = ExportService()
csv_service = ProfessionalCSVExportService(db)
excel_service = ProfessionalExcelExportService(db)
pdf_service = ProfessionalPDFExportService(db)
m1_service = M1ExportService(db)

# NEW - Single unified service
unified_export_service = UnifiedExportService(db)
# OR using factory function
unified_export_service = await create_unified_export_service(db)
```

### Step 3: Update Export Method Calls

#### Accounting Software Exports
```python
# OLD - ExportService usage
content, filename, content_type = await export_service.export_transactions(
    conn=db_conn,
    tenant_id=tenant_id,
    format_id=ExportFormat.QUICKBOOKS_ONLINE,
    filters={"date_from": start_date, "date_to": end_date},
    options={"include_uncategorized": True}
)

# NEW - Unified service usage
options = ExportOptions(
    start_date=start_date,
    end_date=end_date,
    include_uncategorized=True
)
result = await unified_export_service.export_transactions(
    tenant_id=tenant_id,
    export_format=ExportFormat.QUICKBOOKS_ONLINE,
    options=options
)
# Access content, filename, content_type through result object
content = result.content
filename = result.filename
content_type = result.content_type
```

#### CSV Exports
```python
# OLD - ProfessionalCSVExportService usage
csv_buffer = await csv_service.generate_transactions_csv(
    tenant_id=tenant_id,
    start_date=start_date,
    end_date=end_date,
    include_metadata=True,
    format_style="quickbooks"
)

# NEW - Unified service usage
options = ExportOptions(
    start_date=start_date,
    end_date=end_date,
    include_metadata=True,
    format_style="quickbooks"
)
result = await unified_export_service.export_transactions(
    tenant_id=tenant_id,
    export_format=ExportFormat.PROFESSIONAL_CSV,
    options=options
)
# Convert to StringIO if needed for backward compatibility
csv_buffer = StringIO(result.content)
```

#### Excel Exports
```python
# OLD - ProfessionalExcelExportService usage
excel_bytes = await excel_service.export_transactions(
    tenant_id=tenant_id,
    start_date=start_date,
    end_date=end_date,
    format_options={
        "include_charts": True,
        "include_summary": True,
        "professional_styling": True
    }
)

# NEW - Unified service usage
options = ExportOptions(
    start_date=start_date,
    end_date=end_date,
    include_charts=True,
    include_summary=True,
    professional_styling=True
)
result = await unified_export_service.export_transactions(
    tenant_id=tenant_id,
    export_format=ExportFormat.PROFESSIONAL_EXCEL,
    options=options
)
excel_bytes = result.content
```

#### PDF Exports
```python
# OLD - ProfessionalPDFExportService usage
pdf_buffer = await pdf_service.generate_financial_report_pdf(
    tenant_id=tenant_id,
    start_date=start_date,
    end_date=end_date,
    include_charts=True,
    include_summary=True
)

# NEW - Unified service usage
options = ExportOptions(
    start_date=start_date,
    end_date=end_date,
    include_charts=True,
    include_summary=True
)
result = await unified_export_service.export_transactions(
    tenant_id=tenant_id,
    export_format=ExportFormat.PROFESSIONAL_PDF,
    options=options
)
pdf_bytes = result.content
```

### Step 4: Update Router/API Endpoints

#### Export Endpoints
```python
# OLD - Multiple endpoints for different services
@router.post("/export/accounting/{format_id}")
async def export_accounting_format(format_id: str, filters: dict):
    export_service = ExportService()
    return await export_service.export_transactions(...)

@router.post("/export/csv")
async def export_csv(options: dict):
    csv_service = ProfessionalCSVExportService(db)
    return await csv_service.generate_transactions_csv(...)

@router.post("/export/excel")
async def export_excel(options: dict):
    excel_service = ProfessionalExcelExportService(db)
    return await excel_service.export_transactions(...)

# NEW - Single unified endpoint
@router.post("/export/{export_format}")
async def unified_export(
    export_format: ExportFormat,
    tenant_id: int = Depends(get_current_tenant_id),
    options: ExportOptions = Body(default_factory=ExportOptions)
):
    unified_service = UnifiedExportService(db)
    result = await unified_service.export_transactions(
        tenant_id=tenant_id,
        export_format=export_format,
        options=options
    )
    
    return Response(
        content=result.content,
        media_type=result.content_type,
        headers={"Content-Disposition": f"attachment; filename={result.filename}"}
    )
```

#### Format Discovery Endpoint
```python
# NEW - Get available formats
@router.get("/export/formats")
async def get_export_formats():
    unified_service = UnifiedExportService(db)
    return await unified_service.get_export_formats()

# NEW - Validate export readiness
@router.post("/export/validate")
async def validate_export(
    export_format: ExportFormat,
    tenant_id: int = Depends(get_current_tenant_id),
    options: ExportOptions = Body(default_factory=ExportOptions)
):
    unified_service = UnifiedExportService(db)
    return await unified_service.validate_export_readiness(
        tenant_id=tenant_id,
        export_format=export_format,
        options=options
    )
```

### Step 5: Update Frontend Integration

#### API Service Updates
```typescript
// OLD - Multiple service methods
export class ReportsService {
  async exportCSV(options: CSVExportOptions) { /* ... */ }
  async exportExcel(options: ExcelExportOptions) { /* ... */ }
  async exportPDF(options: PDFExportOptions) { /* ... */ }
}

export class ExportService {
  async exportToQuickBooks(filters: ExportFilters) { /* ... */ }
  async exportToXero(filters: ExportFilters) { /* ... */ }
}

// NEW - Unified export service
export class UnifiedExportService {
  async exportTransactions(
    exportFormat: ExportFormat,
    options: ExportOptions
  ): Promise<ExportResult> {
    const response = await this.apiClient.post(`/export/${exportFormat}`, options);
    return response.data;
  }
  
  async getAvailableFormats(): Promise<ExportFormat[]> {
    const response = await this.apiClient.get('/export/formats');
    return response.data;
  }
  
  async validateExportReadiness(
    exportFormat: ExportFormat,
    options: ExportOptions
  ): Promise<ValidationResult> {
    const response = await this.apiClient.post('/export/validate', {
      export_format: exportFormat,
      ...options
    });
    return response.data;
  }
}
```

## Enhanced Features in Unified System

### 1. Comprehensive Format Support
```python
# All accounting software formats in one place
supported_formats = [
    ExportFormat.QUICKBOOKS_DESKTOP,    # IIF format
    ExportFormat.QUICKBOOKS_ONLINE,     # CSV format
    ExportFormat.XERO,                  # CSV format
    ExportFormat.SAGE,                  # CSV format  
    ExportFormat.ZOHO_BOOKS,            # CSV format
    ExportFormat.FRESHBOOKS,            # CSV format
    ExportFormat.TALLY_PRIME,           # CSV format
    ExportFormat.WAVE,                  # CSV format
    ExportFormat.PROFESSIONAL_CSV,      # Professional CSV
    ExportFormat.PROFESSIONAL_EXCEL,    # Professional Excel
    ExportFormat.PROFESSIONAL_PDF,      # Professional PDF
    ExportFormat.M1_VERIFICATION,       # M1 verification
]
```

### 2. Unified Options Structure
```python
# Single options class for all export types
options = ExportOptions(
    # Date filtering
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 12, 31),
    
    # Content filtering  
    category_ids=[1, 2, 3],
    include_uncategorized=True,
    include_metadata=True,
    
    # Format styling
    format_style="quickbooks",  # quickbooks, xero, sage, standard
    professional_styling=True,
    
    # Document features
    include_charts=True,
    include_summary=True,
    autofit_columns=True,
    freeze_headers=True,
    
    # Performance options
    chunk_size=1000,
    stream_output=False
)
```

### 3. Enhanced Export Results
```python
# Comprehensive export result information
result = await unified_service.export_transactions(tenant_id, export_format, options)

print(f"File: {result.filename}")
print(f"Size: {result.size_bytes} bytes")
print(f"Type: {result.content_type}")
print(f"Format: {result.export_format}")
print(f"Metadata: {result.metadata}")

# Access content (bytes, string, or stream)
content = result.content
```

### 4. Format-Specific Intelligence
```python
# Automatic format detection and optimization
handler = unified_service.handlers[ExportFormat.QUICKBOOKS_DESKTOP]
content_type = handler.get_content_type()  # "application/x-iif"
file_ext = handler.get_file_extension()    # "iif"

# Format-specific date and currency formatting
formatted_date = handler._format_date(datetime.now(), "quickbooks")  # "12/31/2024"
formatted_amount = handler._format_currency(1234.56, "xero")          # "1234.56"
```

### 5. Export Validation and Readiness
```python
# Pre-export validation
validation = await unified_service.validate_export_readiness(
    tenant_id=tenant_id,
    export_format=ExportFormat.PROFESSIONAL_EXCEL,
    options=options
)

if validation["ready"]:
    print(f"Ready to export {validation['estimated_rows']} transactions")
    print(f"Estimated file size: {validation['estimated_size']} bytes")
else:
    print("Export not ready:")
    for error in validation["errors"]:
        print(f"  Error: {error}")
    for warning in validation["warnings"]:
        print(f"  Warning: {warning}")
```

## Backward Compatibility

### Legacy Method Support
The unified service provides backward compatibility methods:

```python
# Legacy CSV export (still works)
csv_buffer = await unified_service.generate_transactions_csv(
    tenant_id=tenant_id,
    start_date=start_date,
    end_date=end_date,
    include_metadata=True,
    format_style="standard"
)

# Legacy accounting export (still works)
content, filename, content_type = await unified_service.export_transactions_legacy(
    tenant_id=tenant_id,
    format_id="quickbooks_online",
    filters={"date_from": start_date, "date_to": end_date},
    options={"include_uncategorized": True}
)
```

### Gradual Migration Strategy
1. **Phase 1**: Install unified service alongside existing services
2. **Phase 2**: Update new features to use unified service
3. **Phase 3**: Migrate existing endpoints one by one
4. **Phase 4**: Remove old services after verification

## Performance Benefits

### Memory Efficiency
- **Streaming support** for large exports to reduce memory usage
- **Chunk processing** prevents memory overflow with large datasets
- **Lazy loading** of optional dependencies (Excel, PDF libraries)

### Processing Efficiency  
- **Shared query optimization** across all export formats
- **Format-specific optimizations** built into each handler
- **Parallel processing** support for multiple export formats

### Development Efficiency
- **Single service interface** reduces complexity
- **Unified options structure** eliminates format-specific parameter mapping
- **Comprehensive validation** prevents runtime export errors

## Dependency Management

### Optional Dependencies
```bash
# Install all export capabilities
pip install openpyxl reportlab pandas

# Or install selectively
pip install openpyxl          # For Excel exports
pip install reportlab         # For PDF exports  
pip install pandas            # For advanced data processing
```

### Graceful Degradation
```python
# Check support at runtime
from giki_ai_api.shared.exports.unified_export_service import (
    EXCEL_SUPPORT, PDF_SUPPORT, PANDAS_SUPPORT
)

if EXCEL_SUPPORT:
    print("Excel exports available")
else:
    print("Install openpyxl for Excel support")

if PDF_SUPPORT:
    print("PDF exports available")
else:
    print("Install reportlab for PDF support")
```

## Testing Migration

### Unit Tests
```python
import pytest
from giki_ai_api.shared.exports.unified_export_service import (
    UnifiedExportService, ExportFormat, ExportOptions
)

class TestUnifiedExportService:
    async def test_csv_export(self, db_connection, sample_transactions):
        service = UnifiedExportService(db_connection)
        options = ExportOptions(include_metadata=True)
        
        result = await service.export_transactions(
            tenant_id=1,
            export_format=ExportFormat.PROFESSIONAL_CSV,
            options=options
        )
        
        assert result.content_type == "text/csv"
        assert result.file_extension == "csv"
        assert "transactions" in result.filename
        assert len(result.content) > 0
    
    async def test_excel_export_with_charts(self, db_connection):
        if not EXCEL_SUPPORT:
            pytest.skip("Excel support not available")
        
        service = UnifiedExportService(db_connection)
        options = ExportOptions(include_charts=True, include_summary=True)
        
        result = await service.export_transactions(
            tenant_id=1,
            export_format=ExportFormat.PROFESSIONAL_EXCEL,
            options=options
        )
        
        assert result.content_type.startswith("application/vnd.openxml")
        assert result.metadata["includes_charts"] is True
    
    async def test_export_validation(self, db_connection):
        service = UnifiedExportService(db_connection)
        
        validation = await service.validate_export_readiness(
            tenant_id=1,
            export_format=ExportFormat.PROFESSIONAL_CSV,
            options=ExportOptions()
        )
        
        assert "ready" in validation
        assert "estimated_rows" in validation
        assert "estimated_size" in validation
```

### Integration Tests
```python
async def test_end_to_end_export_flow():
    # Test complete export workflow
    service = UnifiedExportService(db)
    
    # 1. Get available formats
    formats = await service.get_export_formats()
    assert len(formats) > 0
    
    # 2. Validate export readiness
    validation = await service.validate_export_readiness(
        tenant_id=1,
        export_format=ExportFormat.QUICKBOOKS_ONLINE,
        options=ExportOptions()
    )
    assert validation["ready"] is True
    
    # 3. Perform export
    result = await service.export_transactions(
        tenant_id=1,
        export_format=ExportFormat.QUICKBOOKS_ONLINE,
        options=ExportOptions()
    )
    assert result.content is not None
    assert result.filename.endswith(".csv")
```

## Troubleshooting

### Common Migration Issues

#### Import Errors
```python
# If you get import errors
from giki_ai_api.shared.exports.unified_export_service import UnifiedExportService

# Make sure the old import paths are removed
# OLD: from giki_ai_api.domains.exports.service import ExportService
```

#### Missing Dependencies
```python
# Check which features are available
from giki_ai_api.shared.exports.unified_export_service import (
    EXCEL_SUPPORT, PDF_SUPPORT
)

if not EXCEL_SUPPORT:
    # Install: pip install openpyxl
    pass

if not PDF_SUPPORT:
    # Install: pip install reportlab
    pass
```

#### Format Not Supported
```python
# Check supported formats
supported = get_supported_export_formats()
if ExportFormat.PROFESSIONAL_EXCEL not in supported:
    print("Excel exports require openpyxl package")
```

#### Performance Issues
```python
# For large exports, use chunking
options = ExportOptions(
    chunk_size=1000,           # Process in smaller chunks
    stream_output=True,        # Enable streaming
    include_charts=False,      # Disable charts for performance
    professional_styling=False # Disable heavy styling
)
```

## Rollback Plan

If issues arise during migration:

1. **Keep old services temporarily** - Don't remove until migration is complete
2. **Feature flags** - Use feature flags to switch between old/new systems
3. **Endpoint versioning** - Create new `/v2/export` endpoints alongside old ones
4. **Gradual migration** - Migrate one export format at a time

```python
# Example rollback-friendly approach
USE_UNIFIED_EXPORT = os.getenv("USE_UNIFIED_EXPORT", "false").lower() == "true"

if USE_UNIFIED_EXPORT:
    service = UnifiedExportService(db)
    result = await service.export_transactions(...)
else:
    # Fallback to old service
    service = ProfessionalCSVExportService(db)
    result = await service.generate_transactions_csv(...)
```

This migration provides significant benefits while maintaining full backward compatibility and allowing for gradual adoption across the application.