"""
Base Service Class for Domain Services
=====================================

This module provides a base service class that consolidates common patterns
across all domain services, eliminating code duplication and providing
consistent error handling, database operations, and validation.

Key Features:
- Standardized database operations with retry logic
- Consistent error handling and validation
- Common utility methods for all services
- Tenant-aware operations
- Logging and monitoring integration
"""

import logging
import uuid
from abc import ABC
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from asyncpg import Connection

from ..exceptions import GikiAIError


class ServiceError(GikiAIError):
    """Base exception for service-level errors."""
    pass


class ValidationError(ServiceError):
    """Exception raised for validation errors."""
    pass


@dataclass
class ServiceResult:
    """Standard result wrapper for service operations."""
    success: bool
    data: Any = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class BaseService(ABC):
    """
    Base service class providing common functionality for all domain services.
    
    This class consolidates:
    - Database operations with retry logic
    - Error handling and validation
    - Logging and monitoring
    - Common utility methods
    - Tenant-aware operations
    """
    
    def __init__(self, conn: Connection, ai_service=None):
        self.conn = conn
        self.ai_service = ai_service
        self.logger = logging.getLogger(self.__class__.__name__)
        
    # Database Operations
    async def fetch_one(
        self, 
        query: str, 
        *args, 
        timeout: float = 10.0
    ) -> Optional[Dict[str, Any]]:
        """Fetch a single row and return as dict."""
        try:
            row = await self.conn.fetchrow(query, *args, timeout=timeout)
            return dict(row) if row else None
        except Exception as e:
            self.logger.error(f"Database fetch_one error: {e}")
            raise ServiceError(f"Database operation failed: {str(e)}")
    
    async def fetch_all(
        self, 
        query: str, 
        *args, 
        timeout: float = 15.0
    ) -> List[Dict[str, Any]]:
        """Fetch all rows and return as list of dicts."""
        try:
            rows = await self.conn.fetch(query, *args, timeout=timeout)
            return [dict(row) for row in rows]
        except Exception as e:
            self.logger.error(f"Database fetch_all error: {e}")
            raise ServiceError(f"Database operation failed: {str(e)}")
    
    async def fetch_val(
        self, 
        query: str, 
        *args, 
        column: int = 0, 
        timeout: float = 10.0
    ) -> Any:
        """Fetch a single value."""
        try:
            return await self.conn.fetchval(query, *args, column=column, timeout=timeout)
        except Exception as e:
            self.logger.error(f"Database fetch_val error: {e}")
            raise ServiceError(f"Database operation failed: {str(e)}")
    
    async def execute(
        self, 
        query: str, 
        *args, 
        timeout: float = 10.0
    ) -> str:
        """Execute a query and return status."""
        try:
            return await self.conn.execute(query, *args, timeout=timeout)
        except Exception as e:
            self.logger.error(f"Database execute error: {e}")
            raise ServiceError(f"Database operation failed: {str(e)}")
    
    async def insert_returning(
        self, 
        table: str, 
        data: Dict[str, Any], 
        returning: str = "*"
    ) -> Dict[str, Any]:
        """Insert a row and return specified columns."""
        try:
            columns = ", ".join(data.keys())
            placeholders = ", ".join(f"${i + 1}" for i in range(len(data)))
            query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders}) RETURNING {returning}"
            
            row = await self.conn.fetchrow(query, *data.values())
            return dict(row) if row else {}
        except Exception as e:
            self.logger.error(f"Database insert error: {e}")
            raise ServiceError(f"Insert operation failed: {str(e)}")
    
    async def update_returning(
        self, 
        table: str, 
        data: Dict[str, Any], 
        where: Dict[str, Any], 
        returning: str = "*"
    ) -> List[Dict[str, Any]]:
        """Update rows and return specified columns."""
        try:
            set_clause = ", ".join(f"{k} = ${i + 1}" for i, k in enumerate(data.keys()))
            where_clause = " AND ".join(f"{k} = ${i + len(data) + 1}" for i, k in enumerate(where.keys()))
            query = f"UPDATE {table} SET {set_clause} WHERE {where_clause} RETURNING {returning}"
            
            rows = await self.conn.fetch(query, *data.values(), *where.values())
            return [dict(row) for row in rows]
        except Exception as e:
            self.logger.error(f"Database update error: {e}")
            raise ServiceError(f"Update operation failed: {str(e)}")
    
    # Validation Methods
    def validate_required_fields(self, data: Dict[str, Any], required_fields: List[str]) -> None:
        """Validate that required fields are present and not empty."""
        missing_fields = []
        for field in required_fields:
            if field not in data or data[field] is None or (isinstance(data[field], str) and not data[field].strip()):
                missing_fields.append(field)
        
        if missing_fields:
            raise ValidationError(f"Missing required fields: {', '.join(missing_fields)}")
    
    def validate_tenant_access(self, tenant_id: int, resource_tenant_id: int) -> None:
        """Validate that the user has access to the resource's tenant."""
        if tenant_id != resource_tenant_id:
            raise ValidationError("Access denied: insufficient tenant permissions")
    
    # Utility Methods
    def generate_id(self) -> str:
        """Generate a unique ID."""
        return str(uuid.uuid4())
    
    def get_current_timestamp(self) -> datetime:
        """Get current timestamp."""
        return datetime.now()
    
    async def exists(self, table: str, where: Dict[str, Any]) -> bool:
        """Check if a record exists."""
        where_clause = " AND ".join(f"{k} = ${i + 1}" for i, k in enumerate(where.keys()))
        query = f"SELECT EXISTS(SELECT 1 FROM {table} WHERE {where_clause})"
        return await self.fetch_val(query, *where.values())
    
    async def count(self, table: str, where: Optional[Dict[str, Any]] = None) -> int:
        """Count records in a table."""
        if where:
            where_clause = " AND ".join(f"{k} = ${i + 1}" for i, k in enumerate(where.keys()))
            query = f"SELECT COUNT(*) FROM {table} WHERE {where_clause}"
            return await self.fetch_val(query, *where.values())
        else:
            query = f"SELECT COUNT(*) FROM {table}"
            return await self.fetch_val(query)
    
    # Result Helpers
    def success_result(self, data: Any = None, metadata: Optional[Dict[str, Any]] = None) -> ServiceResult:
        """Create a successful service result."""
        return ServiceResult(success=True, data=data, metadata=metadata)
    
    def error_result(self, error: str, metadata: Optional[Dict[str, Any]] = None) -> ServiceResult:
        """Create an error service result."""
        return ServiceResult(success=False, error=error, metadata=metadata)
    
    # Logging Helpers
    def log_operation(self, operation: str, details: Optional[Dict[str, Any]] = None) -> None:
        """Log a service operation."""
        message = f"{self.__class__.__name__}: {operation}"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def log_error(self, operation: str, error: Exception, details: Optional[Dict[str, Any]] = None) -> None:
        """Log a service error."""
        message = f"{self.__class__.__name__}: {operation} failed - {str(error)}"
        if details:
            message += f" - {details}"
        self.logger.error(message, exc_info=True)
