"""
Unified Authentication Service
=============================

Consolidated authentication service that merges functionality from:
- domains/auth/secure_auth.py
- core/dependencies.py  
- core/secure_dependencies.py

This service provides:
- JWT token creation and validation
- Password hashing and verification
- User authentication and authorization
- Tenant-aware access control
- Database-backed user verification (no caching)

Security principles:
1. ALWAYS verify user exists and is active in database
2. NEVER cache authentication results or user data
3. NEVER trust JWT content without database verification
4. ALWAYS check current permissions on every request
"""

import logging
import time
from datetime import datetime, timedelta, timezone
from typing import Optional, Tuple

from asyncpg import Connection
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from passlib.context import CryptContext

from .base_service import BaseService, ServiceError
from ..exceptions import GikiAIError


class AuthenticationError(GikiAIError):
    """Authentication-specific error."""
    pass


class AuthorizationError(GikiAIError):
    """Authorization-specific error."""
    pass


class UnifiedAuthService(BaseService):
    """
    Unified authentication service handling all auth operations.
    
    Consolidates JWT handling, password management, user verification,
    and tenant access control into a single service.
    """
    
    def __init__(self, conn: Connection, settings):
        super().__init__(conn)
        self.settings = settings
        
        # OAuth2 scheme for token extraction
        self.oauth2_scheme = OAuth2PasswordBearer(
            tokenUrl=f"{settings.API_V1_STR}/auth/login"
        )
        
        # JWT configuration
        self._algorithm = "RS256"
        self._private_key = None
        self._public_key = None
        self._token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        
        # Password hashing context
        bcrypt_rounds = 12  # Production-grade security
        self.pwd_context = CryptContext(
            schemes=["bcrypt"],
            deprecated="auto",
            bcrypt__rounds=bcrypt_rounds,
        )
        
        self.logger.info("UnifiedAuthService initialized with secure configuration")
    
    # Password Management
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        try:
            return self.pwd_context.verify(plain_password, hashed_password)
        except Exception as e:
            self.log_error("password_verification", e)
            return False
    
    def get_password_hash(self, password: str) -> str:
        """Hash a password for storage."""
        try:
            return self.pwd_context.hash(password)
        except Exception as e:
            self.log_error("password_hashing", e)
            raise AuthenticationError("Failed to hash password")
    
    # JWT Token Management
    def create_access_token(
        self, 
        data: dict, 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create a JWT access token."""
        try:
            to_encode = data.copy()
            
            if expires_delta:
                expire = datetime.now(timezone.utc) + expires_delta
            else:
                expire = datetime.now(timezone.utc) + timedelta(
                    minutes=self._token_expire_minutes
                )
            
            to_encode.update({"exp": expire})
            
            private_key = self._get_private_key()
            encoded_jwt = jwt.encode(to_encode, private_key, algorithm=self._algorithm)
            
            self.log_operation("access_token_created", {"expires": expire})
            return encoded_jwt
            
        except Exception as e:
            self.log_error("token_creation", e)
            raise AuthenticationError("Failed to create access token")
    
    def verify_token(self, token: str) -> dict:
        """Verify and decode a JWT token."""
        try:
            public_key = self._get_public_key()
            payload = jwt.decode(token, public_key, algorithms=[self._algorithm])
            
            # Validate token expiration
            exp = payload.get("exp")
            if exp and datetime.fromtimestamp(exp, tz=timezone.utc) < datetime.now(timezone.utc):
                raise AuthenticationError("Token has expired")
            
            return payload
            
        except JWTError as e:
            self.log_error("token_verification", e)
            raise AuthenticationError("Invalid token")
        except Exception as e:
            self.log_error("token_verification", e)
            raise AuthenticationError("Token verification failed")
    
    # User Authentication
    async def authenticate_user(self, email: str, password: str) -> Optional[dict]:
        """Authenticate a user with email and password."""
        try:
            # Get user from database
            user = await self.get_user_by_email(email)
            if not user:
                return None
            
            # Verify password
            if not self.verify_password(password, user["password_hash"]):
                return None
            
            # Check if user is active
            if not user.get("is_active", False):
                return None
            
            self.log_operation("user_authenticated", {"user_id": user["id"], "email": email})
            return user
            
        except Exception as e:
            self.log_error("user_authentication", e, {"email": email})
            return None
    
    async def get_user_by_email(self, email: str) -> Optional[dict]:
        """Get user by email from database."""
        query = """
            SELECT id, email, password_hash, is_active, tenant_id, 
                   created_at, updated_at
            FROM users 
            WHERE email = $1
        """
        return await self.fetch_one(query, email)
    
    async def get_user_by_id(self, user_id: str) -> Optional[dict]:
        """Get user by ID from database."""
        query = """
            SELECT id, email, password_hash, is_active, tenant_id,
                   created_at, updated_at
            FROM users 
            WHERE id = $1
        """
        return await self.fetch_one(query, user_id)
    
    # Tenant Management
    async def get_tenant_by_id(self, tenant_id: int) -> Optional[dict]:
        """Get tenant by ID from database."""
        query = """
            SELECT id, name, domain, settings, subscription_status, 
                   subscription_plan, created_at, updated_at
            FROM tenants
            WHERE id = $1
        """
        return await self.fetch_one(query, tenant_id)
    
    async def get_current_user_with_tenant(self, user_id: str) -> Tuple[dict, dict]:
        """Get current user with their tenant information."""
        # Get user from database
        user = await self.get_user_by_id(user_id)
        if not user:
            raise AuthenticationError("User not found")
        
        if not user.get("is_active", False):
            raise AuthenticationError("User account is inactive")
        
        # Get tenant from database
        tenant = await self.get_tenant_by_id(user["tenant_id"])
        if not tenant:
            raise AuthorizationError("Tenant not found or access denied")
        
        return user, tenant
    
    def validate_tenant_access(self, user_tenant_id: int, resource_tenant_id: int) -> None:
        """Validate that user has access to the resource's tenant."""
        if user_tenant_id != resource_tenant_id:
            raise AuthorizationError("Access denied: insufficient tenant permissions")
    
    # Token Dependencies (for FastAPI)
    async def get_current_user(self, token: str) -> dict:
        """Get current user from JWT token (FastAPI dependency)."""
        try:
            # Verify token
            payload = self.verify_token(token)
            user_id = payload.get("sub")
            
            if not user_id:
                raise AuthenticationError("Invalid token payload")
            
            # Get user from database (no caching)
            user = await self.get_user_by_id(user_id)
            if not user:
                raise AuthenticationError("User not found")
            
            if not user.get("is_active", False):
                raise AuthenticationError("User account is inactive")
            
            return user
            
        except AuthenticationError:
            raise
        except Exception as e:
            self.log_error("get_current_user", e)
            raise AuthenticationError("Authentication failed")
    
    async def get_current_active_user(self, token: str) -> dict:
        """Get current active user (alias for get_current_user)."""
        return await self.get_current_user(token)
    
    # Private helper methods
    def _get_private_key(self) -> str:
        """Get private key for JWT signing."""
        if self._private_key is None:
            from ...core.jwt_keys import get_private_key
            self._private_key = get_private_key()
        return self._private_key
    
    def _get_public_key(self) -> str:
        """Get public key for JWT verification."""
        if self._public_key is None:
            from ...core.jwt_keys import get_public_key
            self._public_key = get_public_key()
        return self._public_key
    
    # User Management
    async def create_user(
        self, 
        email: str, 
        password: str, 
        tenant_id: int,
        is_active: bool = True
    ) -> dict:
        """Create a new user."""
        try:
            # Check if user already exists
            existing_user = await self.get_user_by_email(email)
            if existing_user:
                raise AuthenticationError("User with this email already exists")
            
            # Hash password
            password_hash = self.get_password_hash(password)
            
            # Create user
            user_data = {
                "id": self.generate_id(),
                "email": email,
                "password_hash": password_hash,
                "tenant_id": tenant_id,
                "is_active": is_active,
                "created_at": self.get_current_timestamp(),
                "updated_at": self.get_current_timestamp()
            }
            
            created_user = await self.insert_returning("users", user_data)
            
            # Remove password hash from response
            created_user.pop("password_hash", None)
            
            self.log_operation("user_created", {"user_id": created_user["id"], "email": email})
            return created_user
            
        except AuthenticationError:
            raise
        except Exception as e:
            self.log_error("user_creation", e, {"email": email})
            raise AuthenticationError("Failed to create user")
    
    async def update_user_password(self, user_id: str, new_password: str) -> bool:
        """Update user password."""
        try:
            password_hash = self.get_password_hash(new_password)
            
            result = await self.update_returning(
                "users",
                {"password_hash": password_hash, "updated_at": self.get_current_timestamp()},
                {"id": user_id}
            )
            
            if result:
                self.log_operation("password_updated", {"user_id": user_id})
                return True
            return False
            
        except Exception as e:
            self.log_error("password_update", e, {"user_id": user_id})
            raise AuthenticationError("Failed to update password")
