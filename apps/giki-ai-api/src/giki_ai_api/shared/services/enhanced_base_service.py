"""
Enhanced Base Service Classes
============================

Extended base service classes that consolidate common patterns found across
accuracy, reports, intelligence, and onboarding services.

This builds on the BaseService foundation to provide specialized base classes for:
- AI-powered services (with Google Gen AI integration)
- Report generation services (with date parsing and formatting)
- Accuracy measurement services (with metrics calculation)
- Data processing services (with validation and transformation)
"""

import json
import logging
import uuid
from abc import ABC
from datetime import date, datetime, timezone
from typing import Any, Dict, List, Optional, Union

import google.genai as genai
from asyncpg import Connection

from ...core.config import settings
from ..exceptions import ServiceError, ValidationError
from .base_service import BaseService, ServiceResult


class AIServiceError(ServiceError):
    """Exception for AI service operations."""
    pass


class ReportServiceError(ServiceError):
    """Exception for report service operations."""
    pass


class AccuracyServiceError(ServiceError):
    """Exception for accuracy measurement operations."""
    pass


class AIEnabledService(BaseService):
    """
    Base service class for AI-powered services.
    
    Consolidates common AI integration patterns:
    - Google Gen AI client initialization
    - Model configuration management
    - Prompt processing and response handling
    - AI error handling and retry logic
    """
    
    def __init__(self, conn: Connection, model_name: str = "gemini-2.0-flash-001"):
        super().__init__(conn)
        self.model_name = model_name
        self._ai_client = None
        self._initialize_ai_client()
    
    def _initialize_ai_client(self) -> None:
        """Initialize Google Gen AI client with error handling."""
        try:
            if settings.VERTEX_PROJECT_ID and settings.VERTEX_LOCATION:
                self._ai_client = genai.Client(
                    vertexai=True,
                    project=settings.VERTEX_PROJECT_ID,
                    location=settings.VERTEX_LOCATION
                )
                self.logger.info(f"Initialized Google Gen AI - project: {settings.VERTEX_PROJECT_ID}")
            else:
                raise ValueError("VERTEX_PROJECT_ID and VERTEX_LOCATION must be configured")
        except Exception as e:
            self.logger.error(f"Failed to initialize AI client: {e}")
            raise AIServiceError(
                "Failed to initialize AI model",
                service_name=self.__class__.__name__,
                operation="ai_init",
                original_error=e
            )
    
    @property
    def ai_client(self) -> genai.Client:
        """Get AI client, initializing if needed."""
        if self._ai_client is None:
            self._initialize_ai_client()
        return self._ai_client
    
    async def generate_ai_response(
        self, 
        prompt: str, 
        system_instruction: Optional[str] = None,
        temperature: float = 0.1,
        max_tokens: Optional[int] = None
    ) -> str:
        """Generate AI response with standardized error handling."""
        try:
            # Build generation config
            generation_config = {
                "temperature": temperature,
                "candidate_count": 1,
            }
            if max_tokens:
                generation_config["max_output_tokens"] = max_tokens
            
            # Generate response
            response = await self.ai_client.aio.models.generate_content(
                model=self.model_name,
                contents=prompt,
                config=generation_config,
                system_instruction=system_instruction
            )
            
            return response.text
            
        except Exception as e:
            self.logger.error(f"AI generation failed: {e}")
            raise AIServiceError(
                f"AI response generation failed: {str(e)}",
                service_name=self.__class__.__name__,
                operation="generate_response",
                original_error=e
            )
    
    async def parse_ai_json_response(
        self, 
        prompt: str, 
        system_instruction: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate and parse JSON response from AI."""
        try:
            response_text = await self.generate_ai_response(prompt, system_instruction)
            
            # Clean response text (remove markdown formatting if present)
            cleaned_text = response_text.strip()
            if cleaned_text.startswith("```json"):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.endswith("```"):
                cleaned_text = cleaned_text[:-3]
            cleaned_text = cleaned_text.strip()
            
            return json.loads(cleaned_text)
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse AI JSON response: {e}")
            raise AIServiceError(
                f"Invalid JSON response from AI: {str(e)}",
                service_name=self.__class__.__name__,
                operation="parse_json_response",
                original_error=e
            )


class ReportService(BaseService):
    """
    Base service class for report generation services.
    
    Consolidates common reporting patterns:
    - Date parsing and validation
    - Report configuration management
    - Data aggregation utilities
    - Export format handling
    """
    
    def __init__(self, conn: Connection):
        super().__init__(conn)
    
    def parse_date(self, date_value: Union[str, date, datetime, None]) -> Optional[date]:
        """Parse various date formats to date object."""
        if date_value is None:
            return None
        
        if isinstance(date_value, date):
            return date_value
        
        if isinstance(date_value, datetime):
            return date_value.date()
        
        if isinstance(date_value, str):
            # Try multiple date formats
            date_formats = [
                "%Y-%m-%d",  # 2025-07-01
                "%Y-%m-%dT%H:%M:%S",  # 2025-07-01T00:00:00
                "%Y-%m-%dT%H:%M:%S.%f",  # 2025-07-01T00:00:00.000000
                "%Y-%m-%dT%H:%M:%S%z",  # 2025-07-01T00:00:00+00:00
                "%m/%d/%Y",  # 07/01/2025
                "%d/%m/%Y",  # 01/07/2025
            ]
            
            for date_format in date_formats:
                try:
                    return datetime.strptime(date_value, date_format).date()
                except ValueError:
                    continue
            
            # If all formats fail
            raise ReportServiceError(
                f"Unable to parse date: {date_value}",
                service_name=self.__class__.__name__,
                operation="parse_date"
            )
        
        raise ReportServiceError(
            f"Unsupported date type: {type(date_value)}",
            service_name=self.__class__.__name__,
            operation="parse_date"
        )
    
    def validate_date_range(self, start_date: Optional[date], end_date: Optional[date]) -> None:
        """Validate date range parameters."""
        if start_date and end_date and start_date > end_date:
            raise ValidationError("Start date must be before or equal to end date")
    
    def format_currency(self, amount: float, currency: str = "USD") -> str:
        """Format amount as currency string."""
        if currency == "USD":
            return f"${amount:,.2f}"
        else:
            return f"{amount:,.2f} {currency}"
    
    def calculate_percentage(self, part: float, total: float) -> float:
        """Calculate percentage with division by zero protection."""
        if total == 0:
            return 0.0
        return (part / total) * 100.0
    
    async def aggregate_by_period(
        self, 
        table: str, 
        date_column: str,
        value_column: str,
        start_date: date,
        end_date: date,
        period: str = "month",
        tenant_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Aggregate data by time period."""
        try:
            # Build period grouping
            if period == "month":
                period_expr = f"DATE_TRUNC('month', {date_column})"
            elif period == "week":
                period_expr = f"DATE_TRUNC('week', {date_column})"
            elif period == "day":
                period_expr = f"DATE_TRUNC('day', {date_column})"
            else:
                raise ReportServiceError(f"Unsupported period: {period}")
            
            # Build query
            where_conditions = [f"{date_column} >= $1", f"{date_column} <= $2"]
            params = [start_date, end_date]
            
            if tenant_id:
                where_conditions.append(f"tenant_id = ${len(params) + 1}")
                params.append(tenant_id)
            
            where_clause = " AND ".join(where_conditions)
            
            query = f"""
                SELECT 
                    {period_expr} as period,
                    COUNT(*) as count,
                    SUM({value_column}) as total,
                    AVG({value_column}) as average
                FROM {table}
                WHERE {where_clause}
                GROUP BY {period_expr}
                ORDER BY period
            """
            
            return await self.fetch_all(query, *params)
            
        except Exception as e:
            self.logger.error(f"Period aggregation failed: {e}")
            raise ReportServiceError(
                f"Failed to aggregate data by {period}: {str(e)}",
                service_name=self.__class__.__name__,
                operation="aggregate_by_period",
                original_error=e
            )


class AccuracyMeasurementService(BaseService):
    """
    Base service class for accuracy measurement services.
    
    Consolidates common accuracy measurement patterns:
    - Precision, recall, F1-score calculations
    - Confusion matrix generation
    - Category matching and normalization
    - Statistical analysis utilities
    """
    
    def __init__(self, conn: Connection):
        super().__init__(conn)
    
    def calculate_accuracy_metrics(
        self, 
        true_positives: int, 
        false_positives: int, 
        false_negatives: int
    ) -> Dict[str, float]:
        """Calculate precision, recall, and F1-score."""
        try:
            # Precision = TP / (TP + FP)
            precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0.0
            
            # Recall = TP / (TP + FN)
            recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0.0
            
            # F1-Score = 2 * (Precision * Recall) / (Precision + Recall)
            f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
            
            # Overall accuracy
            total_predictions = true_positives + false_positives + false_negatives
            accuracy = true_positives / total_predictions if total_predictions > 0 else 0.0
            
            return {
                "precision": round(precision, 4),
                "recall": round(recall, 4),
                "f1_score": round(f1_score, 4),
                "accuracy": round(accuracy, 4)
            }
            
        except Exception as e:
            self.logger.error(f"Accuracy calculation failed: {e}")
            raise AccuracyServiceError(
                f"Failed to calculate accuracy metrics: {str(e)}",
                service_name=self.__class__.__name__,
                operation="calculate_accuracy_metrics",
                original_error=e
            )
    
    def normalize_category(self, category: str) -> str:
        """Normalize category string for comparison."""
        if not category:
            return ""
        
        # Convert to lowercase and strip whitespace
        normalized = category.lower().strip()
        
        # Handle common variations
        normalized = normalized.replace("&", "and").replace("-", " ")
        
        # Remove extra whitespace
        normalized = " ".join(normalized.split())
        
        return normalized
    
    def categories_match(self, category1: str, category2: str, fuzzy: bool = True) -> bool:
        """Check if two categories match with optional fuzzy matching."""
        if not category1 or not category2:
            return False
        
        # Exact match
        if category1 == category2:
            return True
        
        if fuzzy:
            # Normalized match
            norm1 = self.normalize_category(category1)
            norm2 = self.normalize_category(category2)
            return norm1 == norm2
        
        return False
    
    def build_confusion_matrix(
        self, 
        predictions: List[Dict[str, str]]
    ) -> Dict[str, Dict[str, int]]:
        """Build confusion matrix from predictions."""
        try:
            matrix = {}
            
            for pred in predictions:
                actual = pred.get("actual", "")
                predicted = pred.get("predicted", "")
                
                if actual not in matrix:
                    matrix[actual] = {}
                
                if predicted not in matrix[actual]:
                    matrix[actual][predicted] = 0
                
                matrix[actual][predicted] += 1
            
            return matrix
            
        except Exception as e:
            self.logger.error(f"Confusion matrix generation failed: {e}")
            raise AccuracyServiceError(
                f"Failed to build confusion matrix: {str(e)}",
                service_name=self.__class__.__name__,
                operation="build_confusion_matrix",
                original_error=e
            )


# Export all enhanced base services
__all__ = [
    "AIEnabledService",
    "ReportService", 
    "AccuracyMeasurementService",
    "AIServiceError",
    "ReportServiceError",
    "AccuracyServiceError",
]
