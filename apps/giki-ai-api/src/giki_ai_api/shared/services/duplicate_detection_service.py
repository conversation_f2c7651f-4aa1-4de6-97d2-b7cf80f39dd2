"""
Unified Duplicate Detection Service
==================================

Consolidated duplicate detection service that handles both transaction-level
and file-level duplicate detection with tenant isolation.

This service consolidates:
- domains/transactions/duplicate_detection_service.py
- domains/files/upload_deduplication.py

Key Features:
- Multiple detection strategies (exact, fuzzy, date-amount)
- File-level duplicate detection (hash, filename, size)
- Transaction-level duplicate detection
- Tenant-aware isolation (never cross-tenant duplicates)
- Configurable sensitivity levels and policies
- Batch processing for large uploads
- Audit trail of duplicate decisions
"""

import hashlib
import logging
from dataclasses import dataclass
from datetime import datetime
from difflib import SequenceMatcher
from typing import Any, Dict, List, Optional, Set

from asyncpg import Connection

from .base_service import BaseService, ServiceError

logger = logging.getLogger(__name__)


@dataclass
class DuplicateMatch:
    """Represents a potential duplicate match."""
    existing_transaction_id: str
    confidence: float
    match_type: str
    match_details: Dict[str, Any]
    recommended_action: str  # 'skip', 'merge', 'keep_both'


@dataclass
class DeduplicationResult:
    """Result of duplicate detection process."""
    total_checked: int
    duplicates_found: int
    duplicates_skipped: int
    unique_transactions: int
    duplicate_matches: List[DuplicateMatch]
    processing_time_ms: float


@dataclass
class FileDeduplicationResult:
    """Result of file upload deduplication process."""
    # File-level results
    is_duplicate_file: bool
    duplicate_upload_id: Optional[str]
    file_hash: str
    
    # Transaction-level results
    transaction_deduplication: Optional[DeduplicationResult]
    unique_transactions_count: int
    duplicate_transactions_count: int
    
    # Recommendations
    recommended_action: str  # 'proceed', 'skip', 'warn', 'force_required'
    customer_message: str
    duplicate_details: Dict[str, Any]


@dataclass
class DuplicationPolicy:
    """Configuration for duplicate handling."""
    # File-level policies
    skip_duplicate_files: bool = True
    skip_duplicate_transactions: bool = True
    confidence_threshold: float = 0.85
    
    # Transaction-level policies
    exact_match_threshold: float = 1.0
    fuzzy_match_threshold: float = 0.95
    date_amount_threshold: float = 0.9
    description_similarity_threshold: float = 0.85
    date_tolerance_days: int = 3
    
    # Flexibility options
    allow_force_upload: bool = False
    merge_similar_transactions: bool = False


class UnifiedDuplicateDetectionService(BaseService):
    """
    Unified service for detecting and handling duplicates with tenant isolation.
    
    Handles both file-level and transaction-level duplicate detection.
    """

    def __init__(self, conn: Connection, policy: Optional[DuplicationPolicy] = None):
        super().__init__(conn)
        self.policy = policy or DuplicationPolicy()
        
        self.logger.info("UnifiedDuplicateDetectionService initialized with tenant-aware detection")

    async def detect_transaction_duplicates(
        self,
        tenant_id: int,
        transactions: List[Dict[str, Any]],
        detection_strategy: str = "comprehensive",
        skip_duplicates: bool = True,
    ) -> DeduplicationResult:
        """
        Detect duplicates in a batch of transactions for a specific tenant.
        """
        import time
        start_time = time.time()
        
        if not transactions:
            return DeduplicationResult(
                total_checked=0,
                duplicates_found=0,
                duplicates_skipped=0,
                unique_transactions=0,
                duplicate_matches=[],
                processing_time_ms=0.0
            )

        self.log_operation(
            f"Starting duplicate detection for {len(transactions)} transactions",
            {"tenant_id": tenant_id, "strategy": detection_strategy}
        )

        # Build lookup of existing transactions for this tenant
        existing_lookup = await self._build_existing_transactions_lookup(tenant_id)
        
        duplicate_matches = []
        duplicates_found = 0
        
        # Check each transaction for duplicates
        for transaction in transactions:
            matches = await self._detect_transaction_duplicates(
                transaction, existing_lookup, detection_strategy, tenant_id
            )
            
            if matches:
                duplicates_found += 1
                duplicate_matches.extend(matches)

        processing_time = (time.time() - start_time) * 1000
        
        result = DeduplicationResult(
            total_checked=len(transactions),
            duplicates_found=duplicates_found,
            duplicates_skipped=duplicates_found if skip_duplicates else 0,
            unique_transactions=len(transactions) - duplicates_found,
            duplicate_matches=duplicate_matches,
            processing_time_ms=processing_time
        )
        
        self.log_operation(
            f"Duplicate detection completed",
            {
                "total_checked": result.total_checked,
                "duplicates_found": result.duplicates_found,
                "processing_time_ms": result.processing_time_ms
            }
        )
        
        return result

    async def check_file_upload_duplicates(
        self,
        tenant_id: int,
        filename: str,
        file_content: bytes,
        transactions: List[Dict[str, Any]]
    ) -> FileDeduplicationResult:
        """
        Comprehensive duplicate check for file uploads.
        
        Checks both file-level and transaction-level duplicates.
        """
        # Calculate file hash for duplicate detection
        file_hash = hashlib.sha256(file_content).hexdigest()
        
        # Check for file-level duplicates
        duplicate_upload = await self._check_duplicate_file(tenant_id, filename, file_hash)
        is_duplicate_file = duplicate_upload is not None
        
        # Check for transaction-level duplicates within the file
        transaction_dedup = await self.detect_transaction_duplicates(tenant_id, transactions)
        
        # Determine recommended action and customer message
        action, message = self._determine_action_and_message(
            is_duplicate_file, duplicate_upload, transaction_dedup
        )
        
        return FileDeduplicationResult(
            is_duplicate_file=is_duplicate_file,
            duplicate_upload_id=duplicate_upload.get("id") if duplicate_upload else None,
            file_hash=file_hash,
            transaction_deduplication=transaction_dedup,
            unique_transactions_count=transaction_dedup.unique_transactions,
            duplicate_transactions_count=transaction_dedup.duplicates_found,
            recommended_action=action,
            customer_message=message,
            duplicate_details={
                "file_duplicate": duplicate_upload,
                "transaction_duplicates": transaction_dedup.duplicate_matches
            }
        )

    async def store_file_hash(
        self,
        tenant_id: int,
        upload_id: str,
        filename: str,
        file_hash: str,
        file_size: int
    ) -> None:
        """Store file hash for future duplicate detection."""
        await self.insert_returning(
            "file_hashes",
            {
                "tenant_id": tenant_id,
                "upload_id": upload_id,
                "filename": filename,
                "file_hash": file_hash,
                "file_size": file_size,
                "created_at": self.get_current_timestamp()
            }
        )

    # Private helper methods
    async def _build_existing_transactions_lookup(self, tenant_id: int) -> Dict[str, Any]:
        """Build lookup structures for existing transactions."""
        query = """
            SELECT id, description, amount, date, account, transaction_type
            FROM transactions 
            WHERE tenant_id = $1
        """
        
        existing_transactions = await self.fetch_all(query, tenant_id)
        
        # Build lookup structures
        lookup = {
            "exact_hash": {},
            "date_amount": {},
            "all_transactions": existing_transactions
        }
        
        for tx in existing_transactions:
            # Exact hash lookup
            exact_hash = self._generate_transaction_hash(tx)
            if exact_hash not in lookup["exact_hash"]:
                lookup["exact_hash"][exact_hash] = []
            lookup["exact_hash"][exact_hash].append(tx)
            
            # Date + amount lookup
            date_amount_key = f"{tx['date']}_{tx['amount']}"
            if date_amount_key not in lookup["date_amount"]:
                lookup["date_amount"][date_amount_key] = []
            lookup["date_amount"][date_amount_key].append(tx)
        
        return lookup

    def _generate_transaction_hash(self, transaction: Dict[str, Any]) -> str:
        """Generate a hash for exact duplicate detection."""
        # Create a normalized string representation
        normalized = f"{transaction.get('description', '').strip().lower()}|{transaction.get('amount', 0)}|{transaction.get('date', '')}"
        return hashlib.md5(normalized.encode()).hexdigest()

    def _calculate_description_similarity(self, desc1: str, desc2: str) -> float:
        """Calculate similarity between two descriptions."""
        if not desc1 or not desc2:
            return 0.0
        
        # Normalize descriptions
        desc1_norm = desc1.strip().lower()
        desc2_norm = desc2.strip().lower()
        
        return SequenceMatcher(None, desc1_norm, desc2_norm).ratio()

    async def _detect_transaction_duplicates(
        self,
        transaction: Dict[str, Any],
        existing_lookup: Dict[str, Any],
        strategy: str,
        tenant_id: int,
    ) -> List[DuplicateMatch]:
        """Detect duplicates for a single transaction."""
        matches = []

        # Strategy 1: Exact Hash Match
        if strategy in ["exact", "comprehensive"]:
            exact_matches = self._find_exact_matches(transaction, existing_lookup, tenant_id)
            matches.extend(exact_matches)

        # Strategy 2: Date + Amount Match
        if strategy in ["fuzzy", "comprehensive"] and not matches:
            date_amount_matches = self._find_date_amount_matches(
                transaction, existing_lookup, tenant_id
            )
            matches.extend(date_amount_matches)

        return matches

    def _find_exact_matches(
        self, transaction: Dict[str, Any], existing_lookup: Dict[str, Any], tenant_id: int
    ) -> List[DuplicateMatch]:
        """Find exact duplicate matches."""
        exact_hash = self._generate_transaction_hash(transaction)
        exact_matches = existing_lookup["exact_hash"].get(exact_hash, [])

        matches = []
        for existing_tx in exact_matches:
            match = DuplicateMatch(
                existing_transaction_id=existing_tx["id"],
                confidence=1.0,
                match_type="exact_hash",
                match_details={
                    "hash": exact_hash,
                    "existing_description": existing_tx.get("description", ""),
                    "new_description": transaction.get("description", ""),
                },
                recommended_action="skip",
            )
            matches.append(match)

        return matches

    def _find_date_amount_matches(
        self, transaction: Dict[str, Any], existing_lookup: Dict[str, Any], tenant_id: int
    ) -> List[DuplicateMatch]:
        """Find matches based on date and amount proximity."""
        matches = []
        tx_date = transaction.get("date")
        tx_amount = transaction.get("amount")

        if not tx_date or tx_amount is None:
            return matches

        # Check exact date+amount first
        date_amount_key = f"{tx_date}_{tx_amount}"
        exact_date_amount_matches = existing_lookup["date_amount"].get(date_amount_key, [])

        for existing_tx in exact_date_amount_matches:
            # Calculate description similarity
            desc_similarity = self._calculate_description_similarity(
                transaction.get("description", ""), existing_tx.get("description", "")
            )

            if desc_similarity >= self.policy.description_similarity_threshold:
                confidence = (desc_similarity + self.policy.date_amount_threshold) / 2
                match = DuplicateMatch(
                    existing_transaction_id=existing_tx["id"],
                    confidence=confidence,
                    match_type="date_amount_description",
                    match_details={
                        "date_match": True,
                        "amount_match": True,
                        "description_similarity": desc_similarity,
                        "existing_description": existing_tx.get("description", ""),
                        "new_description": transaction.get("description", ""),
                    },
                    recommended_action="skip" if confidence > 0.95 else "merge",
                )
                matches.append(match)

        return matches

    async def _check_duplicate_file(
        self, tenant_id: int, filename: str, file_hash: str
    ) -> Optional[Dict[str, Any]]:
        """Check if file has been uploaded before."""
        query = """
            SELECT upload_id, filename, file_hash, created_at
            FROM file_hashes
            WHERE tenant_id = $1 AND (file_hash = $2 OR filename = $3)
            ORDER BY created_at DESC
            LIMIT 1
        """
        return await self.fetch_one(query, tenant_id, file_hash, filename)

    def _determine_action_and_message(
        self,
        is_duplicate_file: bool,
        duplicate_upload: Optional[Dict[str, Any]],
        transaction_dedup: DeduplicationResult
    ) -> tuple[str, str]:
        """Determine recommended action and customer message."""
        if is_duplicate_file and self.policy.skip_duplicate_files:
            return "skip", f"This file appears to be a duplicate of a previous upload. Upload skipped to prevent duplicate data."
        
        if transaction_dedup.duplicates_found > 0 and self.policy.skip_duplicate_transactions:
            return "skip", f"Found {transaction_dedup.duplicates_found} duplicate transactions. Upload skipped to prevent duplicate data."
        
        if is_duplicate_file or transaction_dedup.duplicates_found > 0:
            return "warn", f"Potential duplicates detected. Please review before proceeding."
        
        return "proceed", "No duplicates detected. Safe to proceed with upload."
