"""
Unified Error Management System
==============================

Consolidated error handling system that merges functionality from:
- shared/error_handlers.py
- shared/exceptions.py
- Various domain-specific error handlers

This system provides:
- Standardized error types and responses
- Consistent error logging and monitoring
- Error recovery and retry mechanisms
- User-friendly error messages
- Error analytics and reporting
"""

import logging
import traceback
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from fastapi import FastAPI, HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import BaseModel, ValidationError

from ..exceptions import GikiAIError


class ErrorSeverity(str, Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(str, Enum):
    """Error categories for classification."""
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    NOT_FOUND = "not_found"
    BUSINESS_LOGIC = "business_logic"
    EXTERNAL_SERVICE = "external_service"
    DATABASE = "database"
    NETWORK = "network"
    SYSTEM = "system"
    UNKNOWN = "unknown"


class ErrorContext(BaseModel):
    """Context information for errors."""
    user_id: Optional[str] = None
    tenant_id: Optional[int] = None
    request_id: Optional[str] = None
    endpoint: Optional[str] = None
    method: Optional[str] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    timestamp: datetime = datetime.now()
    additional_data: Dict[str, Any] = {}


class StandardError(BaseModel):
    """Standard error response format."""
    error_id: str
    error_code: str
    message: str
    details: Optional[str] = None
    category: ErrorCategory
    severity: ErrorSeverity
    timestamp: datetime
    context: Optional[ErrorContext] = None
    suggestions: List[str] = []
    retry_after: Optional[int] = None


class UnifiedErrorHandler:
    """
    Unified error handler that provides consistent error handling
    across the entire application.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_stats: Dict[str, int] = {}
        
    def categorize_error(self, error: Exception) -> ErrorCategory:
        """Categorize an error based on its type."""
        if isinstance(error, ValidationError):
            return ErrorCategory.VALIDATION
        elif isinstance(error, RequestValidationError):
            return ErrorCategory.VALIDATION
        elif isinstance(error, HTTPException):
            if error.status_code == 401:
                return ErrorCategory.AUTHENTICATION
            elif error.status_code == 403:
                return ErrorCategory.AUTHORIZATION
            elif error.status_code == 404:
                return ErrorCategory.NOT_FOUND
            elif 400 <= error.status_code < 500:
                return ErrorCategory.BUSINESS_LOGIC
            elif 500 <= error.status_code < 600:
                return ErrorCategory.SYSTEM
        elif isinstance(error, ConnectionError):
            return ErrorCategory.NETWORK
        elif isinstance(error, GikiAIError):
            return ErrorCategory.BUSINESS_LOGIC
        
        return ErrorCategory.UNKNOWN
    
    def determine_severity(self, error: Exception, category: ErrorCategory) -> ErrorSeverity:
        """Determine error severity based on type and category."""
        if category == ErrorCategory.CRITICAL:
            return ErrorSeverity.CRITICAL
        elif category in [ErrorCategory.SYSTEM, ErrorCategory.DATABASE]:
            return ErrorSeverity.HIGH
        elif category in [ErrorCategory.EXTERNAL_SERVICE, ErrorCategory.NETWORK]:
            return ErrorSeverity.MEDIUM
        elif category in [ErrorCategory.VALIDATION, ErrorCategory.BUSINESS_LOGIC]:
            return ErrorSeverity.LOW
        
        return ErrorSeverity.MEDIUM
    
    def generate_error_id(self) -> str:
        """Generate a unique error ID."""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def get_user_friendly_message(self, error: Exception, category: ErrorCategory) -> str:
        """Get a user-friendly error message."""
        messages = {
            ErrorCategory.VALIDATION: "Please check your input and try again.",
            ErrorCategory.AUTHENTICATION: "Please log in to continue.",
            ErrorCategory.AUTHORIZATION: "You don't have permission to perform this action.",
            ErrorCategory.NOT_FOUND: "The requested resource was not found.",
            ErrorCategory.BUSINESS_LOGIC: "Unable to complete the request. Please try again.",
            ErrorCategory.EXTERNAL_SERVICE: "External service is temporarily unavailable.",
            ErrorCategory.DATABASE: "Database error occurred. Please try again later.",
            ErrorCategory.NETWORK: "Network error. Please check your connection.",
            ErrorCategory.SYSTEM: "System error occurred. Our team has been notified.",
            ErrorCategory.UNKNOWN: "An unexpected error occurred. Please try again.",
        }
        
        return messages.get(category, str(error))
    
    def get_suggestions(self, error: Exception, category: ErrorCategory) -> List[str]:
        """Get suggestions for resolving the error."""
        suggestions = {
            ErrorCategory.VALIDATION: [
                "Check that all required fields are filled",
                "Verify that data formats are correct",
                "Ensure values are within acceptable ranges"
            ],
            ErrorCategory.AUTHENTICATION: [
                "Log in with valid credentials",
                "Check if your session has expired",
                "Reset your password if needed"
            ],
            ErrorCategory.AUTHORIZATION: [
                "Contact your administrator for access",
                "Verify you have the correct permissions",
                "Check if your account is active"
            ],
            ErrorCategory.NOT_FOUND: [
                "Check the URL or resource ID",
                "Verify the resource exists",
                "Try refreshing the page"
            ],
            ErrorCategory.NETWORK: [
                "Check your internet connection",
                "Try again in a few moments",
                "Contact support if the problem persists"
            ],
            ErrorCategory.SYSTEM: [
                "Try again in a few minutes",
                "Contact support if the issue continues",
                "Check our status page for updates"
            ]
        }
        
        return suggestions.get(category, ["Try again later", "Contact support if the issue persists"])
    
    def create_standard_error(
        self,
        error: Exception,
        context: Optional[ErrorContext] = None,
        custom_message: Optional[str] = None
    ) -> StandardError:
        """Create a standardized error response."""
        category = self.categorize_error(error)
        severity = self.determine_severity(error, category)
        error_id = self.generate_error_id()
        
        # Generate error code
        error_code = f"{category.value.upper()}_{error_id}"
        
        # Get user-friendly message
        message = custom_message or self.get_user_friendly_message(error, category)
        
        # Get suggestions
        suggestions = self.get_suggestions(error, category)
        
        # Determine retry delay
        retry_after = None
        if category in [ErrorCategory.EXTERNAL_SERVICE, ErrorCategory.NETWORK, ErrorCategory.SYSTEM]:
            retry_after = 30  # 30 seconds
        
        # Update error statistics
        self.error_stats[category.value] = self.error_stats.get(category.value, 0) + 1
        
        return StandardError(
            error_id=error_id,
            error_code=error_code,
            message=message,
            details=str(error) if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL] else None,
            category=category,
            severity=severity,
            timestamp=datetime.now(),
            context=context,
            suggestions=suggestions,
            retry_after=retry_after
        )
    
    def log_error(
        self,
        error: Exception,
        standard_error: StandardError,
        context: Optional[ErrorContext] = None
    ) -> None:
        """Log error with appropriate level and context."""
        log_data = {
            "error_id": standard_error.error_id,
            "error_code": standard_error.error_code,
            "category": standard_error.category.value,
            "severity": standard_error.severity.value,
            "message": str(error),
            "context": context.dict() if context else None,
        }
        
        if standard_error.severity == ErrorSeverity.CRITICAL:
            self.logger.critical("Critical error occurred", extra=log_data, exc_info=True)
        elif standard_error.severity == ErrorSeverity.HIGH:
            self.logger.error("High severity error", extra=log_data, exc_info=True)
        elif standard_error.severity == ErrorSeverity.MEDIUM:
            self.logger.warning("Medium severity error", extra=log_data)
        else:
            self.logger.info("Low severity error", extra=log_data)
    
    def handle_error(
        self,
        error: Exception,
        request: Optional[Request] = None,
        custom_message: Optional[str] = None
    ) -> JSONResponse:
        """Handle an error and return a standardized JSON response."""
        # Extract context from request
        context = None
        if request:
            context = ErrorContext(
                request_id=getattr(request.state, 'request_id', None),
                endpoint=str(request.url.path),
                method=request.method,
                user_agent=request.headers.get('user-agent'),
                ip_address=request.client.host if request.client else None,
            )
        
        # Create standard error
        standard_error = self.create_standard_error(error, context, custom_message)
        
        # Log the error
        self.log_error(error, standard_error, context)
        
        # Determine HTTP status code
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        if isinstance(error, HTTPException):
            status_code = error.status_code
        elif isinstance(error, ValidationError):
            status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        elif isinstance(error, RequestValidationError):
            status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        elif standard_error.category == ErrorCategory.AUTHENTICATION:
            status_code = status.HTTP_401_UNAUTHORIZED
        elif standard_error.category == ErrorCategory.AUTHORIZATION:
            status_code = status.HTTP_403_FORBIDDEN
        elif standard_error.category == ErrorCategory.NOT_FOUND:
            status_code = status.HTTP_404_NOT_FOUND
        elif standard_error.category == ErrorCategory.VALIDATION:
            status_code = status.HTTP_400_BAD_REQUEST
        
        # Create response
        response_data = standard_error.dict()
        
        # Remove sensitive details for client-facing errors
        if standard_error.severity == ErrorSeverity.LOW:
            response_data.pop('details', None)
        
        return JSONResponse(
            status_code=status_code,
            content=response_data,
            headers={
                'X-Error-ID': standard_error.error_id,
                'X-Error-Category': standard_error.category.value,
            }
        )
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics."""
        return {
            "total_errors": sum(self.error_stats.values()),
            "by_category": self.error_stats.copy(),
            "timestamp": datetime.now().isoformat()
        }
    
    def reset_stats(self) -> None:
        """Reset error statistics."""
        self.error_stats.clear()


# Global error handler instance
error_handler = UnifiedErrorHandler()


def register_error_handlers(app: FastAPI) -> None:
    """Register all error handlers with the FastAPI application."""
    
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        return error_handler.handle_error(exc, request)
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
        return error_handler.handle_error(exc, request)
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
        return error_handler.handle_error(exc, request)
    
    @app.exception_handler(ValidationError)
    async def pydantic_validation_exception_handler(request: Request, exc: ValidationError) -> JSONResponse:
        return error_handler.handle_error(exc, request)


def handle_service_error(func):
    """Decorator to handle service-level errors consistently."""
    import functools
    
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            # Let the global error handler deal with it
            raise e
    
    return wrapper
