# Performance Monitoring Migration Guide

## Overview

This guide explains how to migrate from the three separate performance monitoring systems to the unified `UnifiedPerformanceMonitor`. The consolidation eliminates ~1,000+ lines of duplicate code while providing enhanced functionality.

## What Was Consolidated

### 1. Performance Middleware (`shared/middleware/performance.py`)
- **398 lines** of FastAPI middleware with AI-aware thresholds
- Request/response time tracking with context-aware logging
- AI operation detection and intelligent threshold application

### 2. Performance Decorators (`shared/monitoring/performance.py`)  
- **335 lines** of function decorators for operation tracking
- Categorization-specific performance monitoring
- File upload tracking with context managers

### 3. Performance Monitor Service (`shared/services/system/performance_monitor.py`)
- **424 lines** of centralized metrics collection and analysis
- Endpoint statistics and health assessment
- Production-ready metric retention and reporting

## Migration Steps

### Step 1: Update Imports

#### Replace Middleware Imports
```python
# OLD - Replace this
from giki_ai_api.shared.middleware.performance import (
    PerformanceMonitoringMiddleware,
    performance_metrics,
    PerformanceMonitor
)

# NEW - Use this instead
from giki_ai_api.shared.monitoring.unified_performance_monitor import (
    unified_performance_monitor
)
```

#### Replace Decorator Imports
```python
# OLD - Replace this
from giki_ai_api.shared.monitoring.performance import (
    track_performance,
    track_categorization, 
    track_file_upload,
    FileUploadTracker
)

# NEW - Use this instead  
from giki_ai_api.shared.monitoring.unified_performance_monitor import (
    track_performance,
    track_categorization,
    track_file_upload, 
    FileUploadTracker
)
```

#### Replace Service Imports
```python
# OLD - Replace this
from giki_ai_api.shared.services.system.performance_monitor import (
    performance_monitor
)

# NEW - Use this instead
from giki_ai_api.shared.monitoring.unified_performance_monitor import (
    unified_performance_monitor
)
```

### Step 2: Update Middleware Configuration

#### In main.py or app setup:
```python
# OLD - Replace this
from giki_ai_api.shared.middleware.performance import PerformanceMonitoringMiddleware
app.add_middleware(PerformanceMonitoringMiddleware, slow_request_threshold=400.0)

# NEW - Use this instead
from giki_ai_api.shared.monitoring.unified_performance_monitor import unified_performance_monitor
app.add_middleware(unified_performance_monitor.create_middleware())
```

### Step 3: Update Decorator Usage

#### Performance Tracking (No Changes Required)
```python
# This works exactly the same
@track_performance("categorize_transaction")
async def categorize_transaction(transaction_data):
    # Your code here
    pass
```

#### Categorization Tracking (No Changes Required)  
```python
# This works exactly the same
@track_categorization(
    transaction_id="12345",
    category="Food & Dining", 
    confidence=0.85
)
async def process_categorization(transaction):
    # Your code here
    pass
```

#### File Upload Tracking (No Changes Required)
```python
# This works exactly the same
@track_file_upload("transactions.csv", 1024*1024, "production")
async def upload_file(file_data):
    # Your code here
    pass

# Context manager also works the same
with FileUploadTracker("data.xlsx", file_size, "historical"):
    # File processing code
    pass
```

### Step 4: Update Service Method Calls

#### Recording Metrics
```python
# OLD - performance_monitor methods
performance_monitor.record_api_request(method, endpoint, time_ms, status_code)
performance_monitor.record_db_operation(operation, duration_ms, table)
performance_monitor.get_performance_summary(minutes=5)

# NEW - unified_performance_monitor methods (same API)
unified_performance_monitor.record_api_request(method, endpoint, time_ms, status_code)
unified_performance_monitor.record_db_operation(operation, duration_ms, table)
unified_performance_monitor.get_performance_summary(minutes=5)
```

#### Enhanced Metrics Recording
```python
# NEW - Additional functionality available
unified_performance_monitor.record_categorization(
    transaction_id="12345",
    duration_ms=150.0,
    category="Food & Dining",
    confidence=0.85,
    success=True
)

unified_performance_monitor.record_operation(
    operation="batch_processing",
    duration_ms=30000.0,
    success=True,
    metadata={"batch_size": 100}
)
```

### Step 5: Update Health Checks and Monitoring

#### Health Assessment
```python
# OLD - Basic health check
summary = performance_monitor.get_performance_summary(minutes=5)
health = summary.get('overall_health', {})

# NEW - Enhanced AI-aware health assessment
summary = unified_performance_monitor.get_performance_summary(minutes=5)
health = summary['overall_health']
ai_insights = summary['ai_aware_insights']
categorization_perf = summary['categorization_performance'] 
```

#### Router Integration
```python
# Update performance monitoring endpoints
@router.get("/performance/health")
async def get_performance_health():
    return unified_performance_monitor.get_performance_summary(minutes=5)

@router.get("/performance/status") 
async def get_performance_status():
    return unified_performance_monitor.get_current_status()
```

## Enhanced Features in Unified System

### 1. AI-Aware Thresholds
The unified system automatically applies appropriate thresholds based on operation type:

- **Standard API operations**: 400ms (Claude Code optimized)
- **AI operations**: 15 seconds (categorization, insights)
- **Complex AI operations**: 5 minutes (file processing, batch operations)
- **Database operations**: 500ms
- **Authentication**: 1 second (warning), 200ms (info)

### 2. Comprehensive Metric Types
```python
# All metrics are stored with type classification
metrics = unified_performance_monitor.get_performance_summary(minutes=10)

print(metrics['api_performance'])           # API request metrics
print(metrics['database_performance'])      # Database operation metrics  
print(metrics['operations_performance'])    # General operation metrics
print(metrics['categorization_performance']) # AI categorization metrics
print(metrics['file_upload_performance'])   # File upload metrics
print(metrics['ai_aware_insights'])         # AI-specific insights
```

### 3. Enhanced Context Managers
```python
# Track any operation with automatic categorization
with unified_performance_monitor.track_operation("complex_analysis", batch_size=500):
    # Your complex operation here
    process_batch_data()
```

### 4. Intelligent Logging
The unified system provides context-aware logging:
- Different log levels for different operation types
- AI-aware threshold messaging  
- Categorized operation context (STANDARD, AI, COMPLEX_AI, AUTH)
- Performance degradation insights

## Breaking Changes

### None! 
The unified system maintains 100% backward compatibility with existing APIs. All decorator signatures, method names, and return formats remain identical.

### Deprecated Patterns
These patterns still work but are deprecated:

```python
# DEPRECATED - Old performance_metrics global
from giki_ai_api.shared.middleware.performance import performance_metrics
metrics = performance_metrics.get_summary()

# PREFERRED - Use unified system
summary = unified_performance_monitor.get_performance_summary(minutes=5)
```

## Configuration Options

### Custom Thresholds
```python
from giki_ai_api.shared.monitoring.unified_performance_monitor import (
    UnifiedPerformanceMonitor, 
    PerformanceThresholds
)

# Create custom thresholds
custom_thresholds = PerformanceThresholds(
    standard_api_ms=300.0,           # Faster standard threshold
    ai_operations_ms=10000.0,        # Faster AI threshold  
    auth_warning_ms=500.0            # Faster auth threshold
)

# Create custom monitor instance
custom_monitor = UnifiedPerformanceMonitor(
    max_metrics_age_minutes=120,     # 2 hour retention
    thresholds=custom_thresholds
)
```

### Environment-Specific Configuration
```python
# Development - More detailed logging
if settings.ENVIRONMENT == "development":
    unified_performance_monitor.thresholds.standard_api_ms = 200.0
    
# Production - Optimized for scale
elif settings.ENVIRONMENT == "production":
    unified_performance_monitor.thresholds.standard_api_ms = 500.0
```

## Testing Migration

### Unit Tests
```python
# OLD test patterns still work
def test_performance_tracking():
    with unified_performance_monitor.track_operation("test_op"):
        time.sleep(0.1)
    
    status = unified_performance_monitor.get_current_status()
    assert status['total_requests_lifetime'] >= 0

# NEW enhanced testing
def test_ai_aware_performance():
    # Test AI operation tracking
    unified_performance_monitor.record_categorization(
        transaction_id="test_123",
        duration_ms=5000.0,
        category="Test Category",
        confidence=0.9,
        success=True
    )
    
    summary = unified_performance_monitor.get_performance_summary(minutes=1)
    assert summary['categorization_performance']['total_categorizations'] == 1
    assert summary['categorization_performance']['avg_confidence'] == 0.9
```

### Integration Tests
```python
# Test middleware integration
async def test_unified_middleware():
    middleware = unified_performance_monitor.create_middleware()
    # Test with FastAPI test client
    # Verify performance headers are added
    # Check that appropriate thresholds are applied
```

## Performance Benefits

### Memory Efficiency
- **Single deque** for all metrics vs. multiple separate collections
- **Intelligent cleanup** removes old metrics automatically
- **Type-based filtering** for efficient metric analysis

### Processing Efficiency  
- **Unified processing pipeline** eliminates duplicate calculations
- **Context-aware thresholds** reduce false positive alerts
- **Batch metric recording** improves performance

### Monitoring Efficiency
- **Single source of truth** for all performance data
- **Comprehensive health assessment** includes all metric types
- **AI-aware insights** provide contextual performance analysis

## Rollback Plan

If issues arise, you can temporarily rollback by:

1. **Reverting imports** to the old separate systems
2. **Keeping unified system** alongside old systems during transition
3. **Gradual migration** - migrate one component at a time

The old systems remain functional and can coexist with the unified system during transition.

## Support and Troubleshooting

### Common Issues

#### Import Errors
```python
# If you get import errors, ensure you're importing from the new location
from giki_ai_api.shared.monitoring.unified_performance_monitor import unified_performance_monitor
```

#### Missing Metrics
```python
# If metrics seem missing, check the metric type filtering
summary = unified_performance_monitor.get_performance_summary(minutes=15)  # Longer window
all_metrics = [m for m in unified_performance_monitor.metrics]  # All metrics
```

#### Performance Degradation
```python
# Check memory usage and clean old metrics
status = unified_performance_monitor.get_current_status()
print(f"Memory usage: {status['memory_usage_mb']:.2f} MB")

# Force cleanup if needed
unified_performance_monitor._clean_old_metrics()
```

### Debug Mode
```python
# Enable detailed logging for debugging
import logging
logging.getLogger('giki_ai_api.shared.monitoring.unified_performance_monitor').setLevel(logging.DEBUG)
```

This migration provides significant benefits while maintaining full backward compatibility. The unified system is more efficient, more intelligent, and provides better insights into your application's performance characteristics.