"""
Unified Performance Monitoring System - Consolidates all performance tracking patterns.

This module consolidates:
1. PerformanceMonitoringMiddleware (performance.py) - FastAPI middleware with AI-aware thresholds
2. Performance decorators (monitoring/performance.py) - Function decorators for tracking
3. PerformanceMonitor service (services/system/performance_monitor.py) - Centralized metrics

Key features:
- Unified metric collection and storage
- AI-aware performance thresholds 
- Comprehensive middleware, decorators, and service patterns
- Memory-efficient metric retention
- Production-ready health assessment
"""

import asyncio
import functools
import logging
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, Optional

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


@dataclass
class MetricData:
    """Individual metric data point with standardized structure."""
    
    timestamp: float
    value: float
    metric_type: str  # 'api', 'db', 'system', 'categorization', 'file_upload'
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EndpointStats:
    """Statistics for a specific endpoint with comprehensive tracking."""
    
    total_requests: int = 0
    total_time_ms: float = 0.0
    min_time_ms: float = float("inf")
    max_time_ms: float = 0.0
    error_count: int = 0
    slow_requests: int = 0
    status_codes: Dict[int, int] = field(default_factory=lambda: defaultdict(int))
    
    @property
    def avg_time_ms(self) -> float:
        """Calculate average response time."""
        return (
            self.total_time_ms / self.total_requests if self.total_requests > 0 else 0.0
        )
    
    @property
    def error_rate_percent(self) -> float:
        """Calculate error rate percentage."""
        return (self.error_count / self.total_requests * 100) if self.total_requests > 0 else 0.0
    
    @property
    def slow_rate_percent(self) -> float:
        """Calculate slow request rate percentage."""
        return (self.slow_requests / self.total_requests * 100) if self.total_requests > 0 else 0.0

    def update(self, response_time_ms: float, status_code: int = 200, is_slow: bool = False):
        """Update statistics with new request data."""
        self.total_requests += 1
        self.total_time_ms += response_time_ms
        self.min_time_ms = min(self.min_time_ms, response_time_ms)
        self.max_time_ms = max(self.max_time_ms, response_time_ms)
        
        is_error = status_code >= 400
        if is_error:
            self.error_count += 1
        if is_slow:
            self.slow_requests += 1
            
        self.status_codes[status_code] += 1


@dataclass 
class PerformanceThresholds:
    """AI-aware performance thresholds for different operation types."""
    
    # Standard API operations (non-AI)
    standard_api_ms: float = 400.0  # DOUBLED from 200ms for Claude Code efficiency
    
    # AI operations 
    ai_operations_ms: float = 15000.0  # 15 seconds for AI categorization/insights
    complex_ai_ms: float = 300000.0   # 5 minutes for file processing/batch operations
    
    # Database operations
    db_query_ms: float = 500.0        # Database query threshold
    
    # Authentication operations  
    auth_warning_ms: float = 1000.0   # DOUBLED from 500ms
    auth_info_ms: float = 200.0
    
    # File upload operations
    file_upload_small_ms: float = 5000.0   # <1MB files
    file_upload_large_ms: float = 30000.0  # >1MB files
    
    # AI-aware endpoint patterns
    ai_endpoints = [
        "/categorize", "/upload", "/process", "/batch", "/insights", 
        "/schema", "/agent", "/ai", "/intelligence", "/accuracy"
    ]
    
    complex_ai_endpoints = ["/upload", "/process", "/batch", "/historical"]
    
    skip_monitoring_paths = {
        "/health", "/api/v1/health", "/health/env", "/health/performance",
        "/docs", "/openapi.json", "/favicon.ico"
    }
    
    def get_appropriate_threshold(self, path: str, file_size: Optional[int] = None) -> float:
        """Get the appropriate performance threshold for the given endpoint."""
        # Skip monitoring paths
        if path in self.skip_monitoring_paths:
            return float('inf')  # Never mark as slow
            
        # Complex AI operations (file processing)
        if any(complex_path in path for complex_path in self.complex_ai_endpoints):
            return self.complex_ai_ms
            
        # File upload operations with size awareness
        if "/upload" in path and file_size is not None:
            if file_size > 1024 * 1024:  # >1MB
                return self.file_upload_large_ms
            else:
                return self.file_upload_small_ms
                
        # Standard AI operations
        if any(ai_path in path for ai_path in self.ai_endpoints):
            return self.ai_operations_ms
            
        # Authentication operations
        if "/auth/" in path:
            return self.auth_warning_ms
            
        # Database operations (for decorator usage)
        if path.startswith("db_"):
            return self.db_query_ms
            
        # Standard non-AI operations
        return self.standard_api_ms


class UnifiedPerformanceMonitor:
    """
    Unified performance monitoring system consolidating all performance tracking patterns.
    
    Combines:
    - Middleware functionality for request tracking
    - Decorator functionality for operation tracking  
    - Service functionality for metrics collection and analysis
    - AI-aware thresholds and intelligent monitoring
    """
    
    def __init__(
        self,
        max_metrics_age_minutes: int = 60,
        thresholds: Optional[PerformanceThresholds] = None
    ):
        """Initialize unified performance monitor."""
        self.max_metrics_age = max_metrics_age_minutes * 60  # Convert to seconds
        self.thresholds = thresholds or PerformanceThresholds()
        
        # Unified metrics storage
        self.metrics: deque = deque()  # All metrics in one deque with type differentiation
        
        # Endpoint-specific statistics  
        self.endpoint_stats: Dict[str, EndpointStats] = defaultdict(EndpointStats)
        
        # Operation tracking
        self.operation_stats: Dict[str, EndpointStats] = defaultdict(EndpointStats)
        
        # Real-time counters
        self.total_requests = 0
        self.slow_requests = 0
        self.error_requests = 0
        
        logger.info(
            f"Unified performance monitor initialized with {max_metrics_age_minutes}min retention"
        )

    # ====== MIDDLEWARE FUNCTIONALITY ======
    
    def create_middleware(self) -> BaseHTTPMiddleware:
        """Create FastAPI middleware with unified performance monitoring."""
        
        class UnifiedPerformanceMiddleware(BaseHTTPMiddleware):
            def __init__(self, app, monitor: UnifiedPerformanceMonitor):
                super().__init__(app)
                self.monitor = monitor
            
            async def dispatch(self, request: Request, call_next: Callable):
                # Skip monitoring for excluded paths
                if request.url.path in self.monitor.thresholds.skip_monitoring_paths:
                    return await call_next(request)
                
                start_time = time.time()
                request.state.start_time = start_time
                
                try:
                    # Process the request
                    response = await call_next(request)
                    
                    # Calculate metrics
                    end_time = time.time()
                    response_time = end_time - start_time
                    response_time_ms = response_time * 1000
                    
                    # Get file size for upload operations
                    file_size = None
                    if hasattr(request.state, 'file_size'):
                        file_size = request.state.file_size
                    
                    # Get appropriate threshold
                    threshold_ms = self.monitor.thresholds.get_appropriate_threshold(
                        request.url.path, file_size
                    )
                    
                    # Record the request
                    self.monitor.record_api_request(
                        method=request.method,
                        endpoint=request.url.path,
                        response_time_ms=response_time_ms,
                        status_code=response.status_code,
                        tenant_id=getattr(request.state, 'tenant_id', None),
                        threshold_ms=threshold_ms
                    )
                    
                    # Add performance headers
                    response.headers["X-Response-Time"] = f"{response_time_ms:.2f}ms"
                    response.headers["X-Process-Time"] = f"{response_time:.6f}"
                    response.headers["X-Threshold"] = f"{threshold_ms:.0f}ms"
                    
                    return response
                    
                except Exception as e:
                    # Record error metrics
                    end_time = time.time()
                    response_time_ms = (end_time - start_time) * 1000
                    
                    self.monitor.record_api_request(
                        method=request.method,
                        endpoint=request.url.path,
                        response_time_ms=response_time_ms,
                        status_code=500,
                        error=str(e)
                    )
                    
                    logger.error(
                        f"ERROR REQUEST: {request.method} {request.url} - "
                        f"{response_time_ms:.2f}ms - Exception: {str(e)}"
                    )
                    raise
        
        return UnifiedPerformanceMiddleware(None, self)

    # ====== DECORATOR FUNCTIONALITY ======
    
    def track_performance(self, operation_name: str) -> Callable:
        """Decorator to track performance of any operation."""
        
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs) -> Any:
                start_time = time.time()
                error = None
                
                try:
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    error = str(e)
                    raise
                finally:
                    elapsed_ms = (time.time() - start_time) * 1000
                    
                    # Record operation metric
                    self.record_operation(
                        operation=operation_name,
                        duration_ms=elapsed_ms,
                        success=error is None,
                        error=error,
                        metadata={'function': func.__name__}
                    )
            
            @functools.wraps(func) 
            def sync_wrapper(*args, **kwargs) -> Any:
                start_time = time.time()
                error = None
                
                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    error = str(e)
                    raise
                finally:
                    elapsed_ms = (time.time() - start_time) * 1000
                    
                    # Record operation metric
                    self.record_operation(
                        operation=operation_name,
                        duration_ms=elapsed_ms,
                        success=error is None,
                        error=error,
                        metadata={'function': func.__name__}
                    )
            
            # Return appropriate wrapper based on function type
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator
    
    def track_categorization(
        self,
        transaction_id: str,
        category: Optional[str] = None,
        confidence: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Callable:
        """Decorator to track categorization operations with detailed metrics."""
        
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs) -> Any:
                start_time = time.time()
                error = None
                
                try:
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    error = str(e)
                    raise
                finally:
                    elapsed_ms = (time.time() - start_time) * 1000
                    
                    # Record categorization metric
                    self.record_categorization(
                        transaction_id=transaction_id,
                        duration_ms=elapsed_ms,
                        category=category,
                        confidence=confidence,
                        success=error is None,
                        error=error,
                        metadata=metadata or {}
                    )
            
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs) -> Any:
                start_time = time.time()
                error = None
                
                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    error = str(e)
                    raise
                finally:
                    elapsed_ms = (time.time() - start_time) * 1000
                    
                    # Record categorization metric
                    self.record_categorization(
                        transaction_id=transaction_id,
                        duration_ms=elapsed_ms,
                        category=category,
                        confidence=confidence,
                        success=error is None,
                        error=error,
                        metadata=metadata or {}
                    )
            
            # Return appropriate wrapper based on function type
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator
    
    def track_file_upload(self, file_name: str, file_size: int, upload_type: str = "production"):
        """Decorator for tracking file upload operations."""
        
        def decorator(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                error = None
                
                try:
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    error = str(e)
                    raise
                finally:
                    duration_ms = (time.time() - start_time) * 1000
                    
                    # Record file upload metric
                    self.record_file_upload(
                        file_name=file_name,
                        file_size=file_size,
                        upload_type=upload_type,
                        duration_ms=duration_ms,
                        success=error is None,
                        error=error
                    )
            
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                return func(*args, **kwargs)
            
            # Return appropriate wrapper based on function type
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator

    def track_operation(self, operation: str, **metadata):
        """Context manager for tracking operations."""
        
        class OperationTracker:
            def __init__(self, monitor, operation_name, metadata):
                self.monitor = monitor
                self.operation_name = operation_name
                self.metadata = metadata
                self.start_time = None
                self.error = None
            
            def __enter__(self):
                self.start_time = time.time()
                return self
            
            def __exit__(self, exc_type, exc_val, exc_tb):
                if self.start_time:
                    duration_ms = (time.time() - self.start_time) * 1000
                    error = str(exc_val) if exc_val else None
                    
                    self.monitor.record_operation(
                        operation=self.operation_name,
                        duration_ms=duration_ms,
                        success=exc_type is None,
                        error=error,
                        metadata=self.metadata
                    )
        
        return OperationTracker(self, operation, metadata)

    # ====== METRIC RECORDING FUNCTIONALITY ======
    
    def record_api_request(
        self,
        method: str,
        endpoint: str,
        response_time_ms: float,
        status_code: int,
        tenant_id: Optional[int] = None,
        threshold_ms: Optional[float] = None,
        error: Optional[str] = None
    ):
        """Record API request performance metrics."""
        now = time.time()
        is_error = status_code >= 400 or error is not None
        
        # Use provided threshold or calculate
        if threshold_ms is None:
            threshold_ms = self.thresholds.get_appropriate_threshold(endpoint)
        
        is_slow = response_time_ms > threshold_ms
        
        # Update counters
        self.total_requests += 1
        if is_slow:
            self.slow_requests += 1
        if is_error:
            self.error_requests += 1
        
        # Store detailed metric
        metric = MetricData(
            timestamp=now,
            value=response_time_ms,
            metric_type='api',
            metadata={
                'method': method,
                'endpoint': endpoint,
                'status_code': status_code,
                'tenant_id': tenant_id,
                'is_slow': is_slow,
                'is_error': is_error,
                'threshold_ms': threshold_ms,
                'error': error
            }
        )
        self.metrics.append(metric)
        
        # Update endpoint-specific stats
        endpoint_key = f"{method} {endpoint}"
        self.endpoint_stats[endpoint_key].update(response_time_ms, status_code, is_slow)
        
        # Intelligent logging based on operation type and thresholds
        self._log_request_performance(endpoint, method, response_time_ms, threshold_ms, is_slow, is_error, status_code)
        
        # Clean old metrics
        self._clean_old_metrics()
    
    def record_operation(
        self,
        operation: str,
        duration_ms: float,
        success: bool = True,
        error: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Record general operation performance metrics."""
        now = time.time()
        threshold_ms = self.thresholds.get_appropriate_threshold(f"op_{operation}")
        is_slow = duration_ms > threshold_ms
        
        metric = MetricData(
            timestamp=now,
            value=duration_ms,
            metric_type='operation',
            metadata={
                'operation': operation,
                'success': success,
                'error': error,
                'is_slow': is_slow,
                'threshold_ms': threshold_ms,
                **(metadata or {})
            }
        )
        self.metrics.append(metric)
        
        # Update operation stats
        status_code = 200 if success else 500
        self.operation_stats[operation].update(duration_ms, status_code, is_slow)
        
        # Log if significant
        if is_slow or not success:
            level = logging.ERROR if not success else logging.WARNING
            logger.log(
                level,
                f"Operation {operation}: {duration_ms:.2f}ms "
                f"{'(SLOW)' if is_slow else ''} {'(ERROR)' if not success else ''}"
            )
    
    def record_categorization(
        self,
        transaction_id: str,
        duration_ms: float,
        category: Optional[str] = None,
        confidence: Optional[float] = None,
        success: bool = True,
        error: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Record categorization operation metrics."""
        now = time.time()
        
        metric = MetricData(
            timestamp=now,
            value=duration_ms,
            metric_type='categorization',
            metadata={
                'transaction_id': transaction_id,
                'category': category,
                'confidence': confidence,
                'success': success,
                'error': error,
                **(metadata or {})
            }
        )
        self.metrics.append(metric)
        
        # Log categorization details
        logger.info(
            f"Categorization: transaction={transaction_id}, "
            f"category={category or 'N/A'}, "
            f"confidence={confidence or 'N/A'}, "
            f"elapsed_ms={duration_ms:.2f}, "
            f"success={success}"
        )
    
    def record_file_upload(
        self,
        file_name: str,
        file_size: int,
        upload_type: str,
        duration_ms: float,
        success: bool = True,
        error: Optional[str] = None
    ):
        """Record file upload operation metrics."""
        now = time.time()
        
        metric = MetricData(
            timestamp=now,
            value=duration_ms,
            metric_type='file_upload',
            metadata={
                'file_name': file_name,
                'file_size': file_size,
                'upload_type': upload_type,
                'success': success,
                'error': error
            }
        )
        self.metrics.append(metric)
        
        # Log file upload details
        logger.info(
            f"File upload: {file_name} ({file_size} bytes, {upload_type}) - "
            f"{duration_ms:.2f}ms, success={success}"
        )
    
    def record_db_operation(
        self,
        operation: str,
        duration_ms: float,
        table: Optional[str] = None,
        tenant_id: Optional[int] = None,
        success: bool = True
    ):
        """Record database operation performance metrics."""
        now = time.time()
        is_slow = duration_ms > self.thresholds.db_query_ms
        
        metric = MetricData(
            timestamp=now,
            value=duration_ms,
            metric_type='db',
            metadata={
                'operation': operation,
                'table': table,
                'tenant_id': tenant_id,
                'success': success,
                'is_slow': is_slow
            }
        )
        self.metrics.append(metric)
        
        if is_slow:
            logger.warning(
                f"Slow DB operation: {operation} on {table} took {duration_ms:.2f}ms"
            )

    # ====== ANALYSIS AND REPORTING FUNCTIONALITY ======
    
    def get_performance_summary(self, minutes: int = 5) -> Dict[str, Any]:
        """Get comprehensive performance summary for the specified time period."""
        cutoff_time = time.time() - (minutes * 60)
        
        # Filter recent metrics by type
        recent_metrics = [m for m in self.metrics if m.timestamp >= cutoff_time]
        
        api_metrics = [m for m in recent_metrics if m.metric_type == 'api']
        db_metrics = [m for m in recent_metrics if m.metric_type == 'db']
        operation_metrics = [m for m in recent_metrics if m.metric_type == 'operation']
        categorization_metrics = [m for m in recent_metrics if m.metric_type == 'categorization']
        file_upload_metrics = [m for m in recent_metrics if m.metric_type == 'file_upload']
        
        return {
            'time_period_minutes': minutes,
            'timestamp': time.time(),
            'api_performance': self._calculate_api_summary(api_metrics),
            'database_performance': self._calculate_db_summary(db_metrics),
            'operations_performance': self._calculate_operations_summary(operation_metrics),
            'categorization_performance': self._calculate_categorization_summary(categorization_metrics),
            'file_upload_performance': self._calculate_file_upload_summary(file_upload_metrics),
            'endpoint_performance': self._get_top_endpoints(),
            'overall_health': self._assess_comprehensive_health(recent_metrics),
            'ai_aware_insights': self._get_ai_aware_insights(recent_metrics)
        }

    def _log_request_performance(self, endpoint: str, method: str, response_time_ms: float, 
                                threshold_ms: float, is_slow: bool, is_error: bool, status_code: int):
        """Intelligent logging based on AI-aware thresholds and operation types."""
        
        # Determine operation type for logging context
        operation_type = "STANDARD"
        if any(ai_path in endpoint for ai_path in self.thresholds.ai_endpoints):
            operation_type = "AI"
        if any(complex_path in endpoint for complex_path in self.thresholds.complex_ai_endpoints):
            operation_type = "COMPLEX_AI"
        if "/auth/" in endpoint:
            operation_type = "AUTH"
        
        endpoint_key = f"{method} {endpoint}"
        
        # Error logging always happens
        if is_error:
            logger.error(
                f"ERROR {operation_type}: {endpoint_key} - "
                f"status={status_code}, time={response_time_ms:.1f}ms"
            )
            return
        
        # Slow request logging with context
        if is_slow:
            logger.warning(
                f"SLOW {operation_type}: {endpoint_key} - "
                f"{response_time_ms:.1f}ms (>{threshold_ms:.0f}ms threshold)"
            )
            return
        
        # Special auth performance logging
        if operation_type == "AUTH":
            if response_time_ms > self.thresholds.auth_info_ms:
                logger.info(f"AUTH OK: {endpoint} - {response_time_ms:.0f}ms")
            else:
                logger.debug(f"AUTH FAST: {endpoint} - {response_time_ms:.0f}ms")

    def _calculate_api_summary(self, metrics: List[MetricData]) -> Dict[str, Any]:
        """Calculate API performance summary."""
        if not metrics:
            return {'total_requests': 0, 'message': 'No API requests in time period'}
        
        response_times = [m.value for m in metrics]
        error_count = sum(1 for m in metrics if m.metadata.get('is_error', False))
        slow_count = sum(1 for m in metrics if m.metadata.get('is_slow', False))
        
        # AI vs non-AI breakdown
        ai_metrics = [m for m in metrics if any(ai_path in m.metadata.get('endpoint', '') 
                                              for ai_path in self.thresholds.ai_endpoints)]
        standard_metrics = [m for m in metrics if m not in ai_metrics]
        
        return {
            'total_requests': len(metrics),
            'avg_response_time_ms': sum(response_times) / len(response_times),
            'min_response_time_ms': min(response_times),
            'max_response_time_ms': max(response_times),
            'error_count': error_count,
            'error_rate_percent': (error_count / len(metrics)) * 100,
            'slow_requests': slow_count,
            'slow_request_rate_percent': (slow_count / len(metrics)) * 100,
            'requests_per_minute': len(metrics) / 5,
            'ai_requests': len(ai_metrics),
            'standard_requests': len(standard_metrics),
            'ai_avg_time_ms': sum(m.value for m in ai_metrics) / len(ai_metrics) if ai_metrics else 0,
            'standard_avg_time_ms': sum(m.value for m in standard_metrics) / len(standard_metrics) if standard_metrics else 0
        }

    def _calculate_operations_summary(self, metrics: List[MetricData]) -> Dict[str, Any]:
        """Calculate operations performance summary."""
        if not metrics:
            return {'total_operations': 0, 'message': 'No operations in time period'}
        
        durations = [m.value for m in metrics]
        success_count = sum(1 for m in metrics if m.metadata.get('success', True))
        
        return {
            'total_operations': len(metrics),
            'success_count': success_count,
            'success_rate_percent': (success_count / len(metrics)) * 100,
            'avg_duration_ms': sum(durations) / len(durations),
            'min_duration_ms': min(durations),
            'max_duration_ms': max(durations)
        }

    def _calculate_categorization_summary(self, metrics: List[MetricData]) -> Dict[str, Any]:
        """Calculate categorization performance summary."""
        if not metrics:
            return {'total_categorizations': 0, 'message': 'No categorizations in time period'}
        
        durations = [m.value for m in metrics]
        success_count = sum(1 for m in metrics if m.metadata.get('success', True))
        
        # Confidence analysis
        confidences = [m.metadata.get('confidence') for m in metrics 
                      if m.metadata.get('confidence') is not None]
        
        return {
            'total_categorizations': len(metrics),
            'success_count': success_count,
            'success_rate_percent': (success_count / len(metrics)) * 100,
            'avg_duration_ms': sum(durations) / len(durations),
            'avg_confidence': sum(confidences) / len(confidences) if confidences else 0,
            'high_confidence_count': sum(1 for c in confidences if c > 0.8),
            'low_confidence_count': sum(1 for c in confidences if c < 0.5)
        }

    def _calculate_file_upload_summary(self, metrics: List[MetricData]) -> Dict[str, Any]:
        """Calculate file upload performance summary."""
        if not metrics:
            return {'total_uploads': 0, 'message': 'No file uploads in time period'}
        
        durations = [m.value for m in metrics]
        success_count = sum(1 for m in metrics if m.metadata.get('success', True))
        total_size = sum(m.metadata.get('file_size', 0) for m in metrics)
        
        return {
            'total_uploads': len(metrics),
            'success_count': success_count,
            'success_rate_percent': (success_count / len(metrics)) * 100,
            'avg_duration_ms': sum(durations) / len(durations),
            'total_size_bytes': total_size,
            'avg_size_bytes': total_size / len(metrics)
        }

    def _calculate_db_summary(self, metrics: List[MetricData]) -> Dict[str, Any]:
        """Calculate database performance summary."""
        if not metrics:
            return {'total_operations': 0, 'message': 'No DB operations in time period'}
        
        durations = [m.value for m in metrics]
        slow_count = sum(1 for m in metrics if m.metadata.get('is_slow', False))
        
        return {
            'total_operations': len(metrics),
            'avg_duration_ms': sum(durations) / len(durations),
            'min_duration_ms': min(durations),
            'max_duration_ms': max(durations),
            'slow_operations': slow_count,
            'slow_operation_rate_percent': (slow_count / len(metrics)) * 100
        }

    def _get_top_endpoints(self) -> Dict[str, Any]:
        """Get top endpoints by various metrics."""
        # Convert to list and sort
        endpoint_items = list(self.endpoint_stats.items())
        
        by_requests = sorted(endpoint_items, key=lambda x: x[1].total_requests, reverse=True)[:10]
        by_avg_time = sorted(endpoint_items, key=lambda x: x[1].avg_time_ms, reverse=True)[:10]
        by_errors = sorted(endpoint_items, key=lambda x: x[1].error_count, reverse=True)[:10]
        
        return {
            'top_by_requests': [
                {
                    'endpoint': endpoint,
                    'total_requests': stats.total_requests,
                    'avg_time_ms': stats.avg_time_ms,
                    'error_rate_percent': stats.error_rate_percent
                }
                for endpoint, stats in by_requests
                if stats.total_requests > 0
            ],
            'slowest_endpoints': [
                {
                    'endpoint': endpoint,
                    'avg_time_ms': stats.avg_time_ms,
                    'total_requests': stats.total_requests,
                    'slow_rate_percent': stats.slow_rate_percent
                }
                for endpoint, stats in by_avg_time
                if stats.total_requests > 0
            ],
            'most_errors': [
                {
                    'endpoint': endpoint,
                    'error_count': stats.error_count,
                    'total_requests': stats.total_requests,
                    'error_rate_percent': stats.error_rate_percent
                }
                for endpoint, stats in by_errors
                if stats.error_count > 0
            ]
        }

    def _assess_comprehensive_health(self, metrics: List[MetricData]) -> Dict[str, Any]:
        """Comprehensive health assessment including AI-aware metrics."""
        if not metrics:
            return {'status': 'unknown', 'score': 0, 'message': 'No metrics available'}
        
        health_score = 100
        issues = []
        
        # Separate metrics by type for specialized analysis
        api_metrics = [m for m in metrics if m.metric_type == 'api']
        db_metrics = [m for m in metrics if m.metric_type == 'db']
        
        if api_metrics:
            # AI-aware API health assessment
            ai_metrics = [m for m in api_metrics if any(ai_path in m.metadata.get('endpoint', '') 
                                                      for ai_path in self.thresholds.ai_endpoints)]
            standard_metrics = [m for m in api_metrics if m not in ai_metrics]
            
            # Error rate assessment
            error_rate = sum(1 for m in api_metrics if m.metadata.get('is_error')) / len(api_metrics) * 100
            if error_rate > 5:
                health_score -= 30
                issues.append(f"High API error rate: {error_rate:.1f}%")
            
            # Slow request assessment (using appropriate thresholds)
            slow_rate = sum(1 for m in api_metrics if m.metadata.get('is_slow')) / len(api_metrics) * 100
            if slow_rate > 15:  # Higher tolerance due to AI operations
                health_score -= 20
                issues.append(f"High slow request rate: {slow_rate:.1f}%")
            
            # Standard API performance
            if standard_metrics:
                avg_standard = sum(m.value for m in standard_metrics) / len(standard_metrics)
                if avg_standard > 1000:  # 1 second for standard operations
                    health_score -= 25
                    issues.append(f"High standard API response time: {avg_standard:.0f}ms")
        
        if db_metrics:
            # Database performance assessment
            avg_db = sum(m.value for m in db_metrics) / len(db_metrics)
            if avg_db > 500:  # 500ms for DB operations
                health_score -= 20
                issues.append(f"High database response time: {avg_db:.0f}ms")
        
        # Determine status
        if health_score >= 90:
            status = "excellent"
        elif health_score >= 75:
            status = "good"
        elif health_score >= 60:
            status = "fair"
        elif health_score >= 40:
            status = "poor"
        else:
            status = "critical"
        
        return {
            'status': status,
            'score': max(0, health_score),
            'issues': issues,
            'total_metrics_analyzed': len(metrics),
            'recommendation': self._get_health_recommendation(status, issues)
        }

    def _get_ai_aware_insights(self, metrics: List[MetricData]) -> Dict[str, Any]:
        """Generate AI-aware performance insights."""
        api_metrics = [m for m in metrics if m.metric_type == 'api']
        
        if not api_metrics:
            return {'message': 'No API metrics for AI analysis'}
        
        # Categorize by operation type
        ai_metrics = [m for m in api_metrics if any(ai_path in m.metadata.get('endpoint', '') 
                                                  for ai_path in self.thresholds.ai_endpoints)]
        auth_metrics = [m for m in api_metrics if '/auth/' in m.metadata.get('endpoint', '')]
        upload_metrics = [m for m in api_metrics if '/upload' in m.metadata.get('endpoint', '')]
        
        insights = {}
        
        if ai_metrics:
            ai_avg = sum(m.value for m in ai_metrics) / len(ai_metrics)
            insights['ai_operations'] = {
                'count': len(ai_metrics),
                'avg_time_ms': ai_avg,
                'performance_status': 'good' if ai_avg < 10000 else 'needs_attention'
            }
        
        if auth_metrics:
            auth_avg = sum(m.value for m in auth_metrics) / len(auth_metrics)
            insights['authentication'] = {
                'count': len(auth_metrics),
                'avg_time_ms': auth_avg,
                'performance_status': 'good' if auth_avg < 500 else 'needs_attention'
            }
        
        if upload_metrics:
            upload_avg = sum(m.value for m in upload_metrics) / len(upload_metrics)
            insights['file_uploads'] = {
                'count': len(upload_metrics),
                'avg_time_ms': upload_avg,
                'performance_status': 'good' if upload_avg < 30000 else 'needs_attention'
            }
        
        return insights

    def _get_health_recommendation(self, status: str, issues: List[str]) -> str:
        """Get health recommendation based on status and issues."""
        if status == "excellent":
            return "System performance is excellent. Continue monitoring."
        elif status == "good":
            return "System performance is good. Monitor for trends."
        elif status == "fair":
            return "Performance degradation detected. Review recent changes and consider optimization."
        elif status == "poor":
            return "Significant performance issues detected. Immediate investigation recommended."
        else:
            return "Critical performance issues detected. Immediate action required."

    def _clean_old_metrics(self):
        """Remove metrics older than the retention period."""
        cutoff_time = time.time() - self.max_metrics_age
        
        # Clean unified metrics deque
        while self.metrics and self.metrics[0].timestamp < cutoff_time:
            self.metrics.popleft()

    # ====== UTILITY METHODS ======
    
    def reset_all_metrics(self):
        """Reset all metrics (use with caution in production)."""
        self.metrics.clear()
        self.endpoint_stats.clear()
        self.operation_stats.clear()
        
        self.total_requests = 0
        self.slow_requests = 0
        self.error_requests = 0
        
        logger.warning("All unified performance metrics have been reset")

    def get_current_status(self) -> Dict[str, Any]:
        """Get current real-time status."""
        return {
            'timestamp': time.time(),
            'total_requests_lifetime': self.total_requests,
            'slow_requests_lifetime': self.slow_requests,
            'error_requests_lifetime': self.error_requests,
            'metrics_in_memory': len(self.metrics),
            'tracked_endpoints': len(self.endpoint_stats),
            'tracked_operations': len(self.operation_stats),
            'memory_usage_mb': self._estimate_memory_usage()
        }

    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage of performance monitor in MB."""
        # Rough estimation
        metrics_size = len(self.metrics) * 300  # Bytes per metric
        endpoint_size = len(self.endpoint_stats) * 400
        operation_size = len(self.operation_stats) * 400
        
        total_bytes = metrics_size + endpoint_size + operation_size
        return total_bytes / (1024 * 1024)


# ====== CONVENIENCE CLASSES FOR BACKWARD COMPATIBILITY ======

class FileUploadTracker:
    """Context manager for tracking file upload operations (backward compatibility)."""
    
    def __init__(self, file_name: str, file_size: int, upload_type: str = "production"):
        self.file_name = file_name
        self.file_size = file_size
        self.upload_type = upload_type
        self.start_time = None
        self.error = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            
            if exc_type is not None:
                self.error = str(exc_val)
            
            # Use global unified monitor
            unified_performance_monitor.record_file_upload(
                file_name=self.file_name,
                file_size=self.file_size,
                upload_type=self.upload_type,
                duration_ms=duration * 1000,
                success=self.error is None,
                error=self.error
            )
        
        return False  # Don't suppress exceptions


# ====== GLOBAL INSTANCES ======

# Global unified performance monitor instance
unified_performance_monitor = UnifiedPerformanceMonitor()

# Convenience functions for backward compatibility
def track_performance(operation_name: str):
    """Decorator for tracking operation performance."""
    return unified_performance_monitor.track_performance(operation_name)

def track_categorization(transaction_id: str, category: Optional[str] = None, 
                        confidence: Optional[float] = None, metadata: Optional[Dict[str, Any]] = None):
    """Decorator for tracking categorization operations."""
    return unified_performance_monitor.track_categorization(transaction_id, category, confidence, metadata)

def track_file_upload(file_name: str, file_size: int, upload_type: str = "production"):
    """Decorator for tracking file upload operations."""
    return unified_performance_monitor.track_file_upload(file_name, file_size, upload_type)


# ====== HELPER FUNCTIONS ======

def get_unified_performance_monitor() -> Optional[UnifiedPerformanceMonitor]:
    """
    Get the unified performance monitor instance from application state.
    
    This function should be used by routers and services to access the performance monitor
    that was initialized during application startup.
    
    Returns:
        UnifiedPerformanceMonitor instance if available, None otherwise
    """
    try:
        # Try to get from FastAPI app state first
        from fastapi import Request
        import inspect
        
        # Look for request in the call stack to get app state
        frame = inspect.currentframe()
        while frame:
            if 'app' in frame.f_locals:
                app = frame.f_locals['app']
                if hasattr(app, 'state') and hasattr(app.state, 'performance_monitor'):
                    return app.state.performance_monitor
            frame = frame.f_back
        
        # Fallback to global instance
        return unified_performance_monitor
        
    except Exception as e:
        logger.debug(f"Could not access performance monitor from app state: {e}")
        return unified_performance_monitor


# ====== EXPORTS ======

__all__ = [
    # Main classes
    'UnifiedPerformanceMonitor',
    'PerformanceThresholds', 
    'MetricData',
    'EndpointStats',
    
    # Global instance
    'unified_performance_monitor',
    
    # Helper functions
    'get_unified_performance_monitor',
    
    # Convenience functions
    'track_performance',
    'track_categorization', 
    'track_file_upload',
    'FileUploadTracker',
]