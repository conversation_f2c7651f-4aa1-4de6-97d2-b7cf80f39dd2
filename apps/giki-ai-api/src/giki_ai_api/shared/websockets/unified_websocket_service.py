"""
Unified WebSocket Service - Consolidates all WebSocket patterns.

This module consolidates:
1. WebSocketService (shared/services/websocket_service.py) - General WebSocket management
2. WebSocket router patterns (domains/intelligence/websocket_router.py) - Endpoint definitions
3. Agent WebSocket patterns (scattered across domains) - Agent-specific communication
4. File processing WebSocket patterns - Upload/processing progress
5. Authentication WebSocket patterns - Secure WebSocket connections

Key features:
- Unified WebSocket management with auto-reconnection
- Multi-tenant isolation and authentication
- Event-based subscription system with type safety
- Real-time progress tracking for all operations
- Pluggable message handlers for different domains
- Connection pooling and resource management
- Backward compatibility with existing patterns
"""

import asyncio
import json
import logging
import time
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Union

import asyncpg
from fastapi import WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState


logger = logging.getLogger(__name__)


class MessageType(str, Enum):
    """Standardized WebSocket message types."""
    
    # Connection management
    CONNECTION_ESTABLISHED = "connection.established"
    CONNECTION_LOST = "connection.lost"
    HEARTBEAT = "heartbeat"
    
    # Agent communication
    AGENT_MESSAGE = "agent.message"
    AGENT_TYPING = "agent.typing"
    AGENT_STATUS = "agent.status"
    AGENT_TRANSFER = "agent.transfer"
    
    # File processing
    FILE_UPLOADED = "file.uploaded"
    FILE_PROCESSING_STARTED = "file.processing_started"
    FILE_PROCESSING_PROGRESS = "file.processing_progress"
    FILE_PROCESSING_COMPLETED = "file.processing_completed"
    FILE_PROCESSING_ERROR = "file.processing_error"
    
    # Categorization
    CATEGORIZATION_STARTED = "categorization.started"
    CATEGORIZATION_PROGRESS = "categorization.progress"
    CATEGORIZATION_COMPLETED = "categorization.completed"
    CATEGORIZATION_ERROR = "categorization.error"
    
    # Transactions
    TRANSACTION_UPDATED = "transaction.updated"
    TRANSACTION_CATEGORIZED = "transaction.categorized"
    TRANSACTION_SELECTED = "transaction.selected"
    
    # Categories
    CATEGORY_CREATED = "category.created"
    CATEGORY_UPDATED = "category.updated"
    
    # Reports
    REPORT_GENERATE = "report.generate"
    REPORT_READY = "report.ready"
    REPORT_ERROR = "report.error"
    
    # Accuracy
    ACCURACY_UPDATED = "accuracy.updated"
    ACCURACY_TEST_STARTED = "accuracy.test_started"
    ACCURACY_TEST_COMPLETED = "accuracy.test_completed"
    
    # System notifications
    SYSTEM_NOTIFICATION = "system.notification"
    SYSTEM_ERROR = "system.error"
    DATA_REFRESH_REQUIRED = "data.refresh_required"
    UI_STATE_UPDATE = "ui.state_update"


class Priority(str, Enum):
    """Message priority levels."""
    
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class WebSocketMessage:
    """Standardized WebSocket message format."""
    
    type: MessageType
    payload: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    tenant_id: Optional[int] = None
    user_id: Optional[str] = None
    priority: Priority = Priority.MEDIUM
    requires_ack: bool = False
    retry_count: int = 0
    expires_at: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary for JSON serialization."""
        return {
            "type": self.type.value,
            "payload": self.payload,
            "timestamp": self.timestamp,
            "message_id": self.message_id,
            "tenant_id": self.tenant_id,
            "user_id": self.user_id,
            "priority": self.priority.value,
            "requires_ack": self.requires_ack,
            "retry_count": self.retry_count,
            "expires_at": self.expires_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "WebSocketMessage":
        """Create message from dictionary."""
        return cls(
            type=MessageType(data["type"]),
            payload=data["payload"],
            timestamp=data.get("timestamp", time.time()),
            message_id=data.get("message_id", str(uuid.uuid4())),
            tenant_id=data.get("tenant_id"),
            user_id=data.get("user_id"),
            priority=Priority(data.get("priority", Priority.MEDIUM.value)),
            requires_ack=data.get("requires_ack", False),
            retry_count=data.get("retry_count", 0),
            expires_at=data.get("expires_at")
        )
    
    def is_expired(self) -> bool:
        """Check if message has expired."""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at


@dataclass
class ConnectionInfo:
    """Information about a WebSocket connection."""
    
    websocket: WebSocket
    tenant_id: int
    user_id: str
    connected_at: float
    last_heartbeat: float
    subscriptions: Set[MessageType] = field(default_factory=set)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_alive(self, heartbeat_timeout: float = 60.0) -> bool:
        """Check if connection is alive based on heartbeat."""
        return (time.time() - self.last_heartbeat) < heartbeat_timeout
    
    def update_heartbeat(self):
        """Update last heartbeat timestamp."""
        self.last_heartbeat = time.time()


class MessageHandler(ABC):
    """Abstract base class for message handlers."""
    
    @abstractmethod
    async def handle_message(
        self, 
        message: WebSocketMessage, 
        connection: ConnectionInfo,
        websocket_service: "UnifiedWebSocketService"
    ) -> Optional[WebSocketMessage]:
        """
        Handle a WebSocket message.
        
        Args:
            message: The incoming message
            connection: Connection information
            websocket_service: Reference to the service for sending responses
            
        Returns:
            Optional response message
        """
        pass
    
    @abstractmethod
    def get_handled_types(self) -> List[MessageType]:
        """Get list of message types this handler processes."""
        pass


class AgentMessageHandler(MessageHandler):
    """Handles agent-related WebSocket messages."""
    
    async def handle_message(
        self, 
        message: WebSocketMessage, 
        connection: ConnectionInfo,
        websocket_service: "UnifiedWebSocketService"
    ) -> Optional[WebSocketMessage]:
        """Handle agent messages."""
        if message.type == MessageType.AGENT_MESSAGE:
            # Process agent message and generate response
            response_payload = {
                "response": f"Received: {message.payload.get('content', '')}",
                "agent_id": message.payload.get("agent_id"),
                "session_id": message.payload.get("session_id")
            }
            
            return WebSocketMessage(
                type=MessageType.AGENT_MESSAGE,
                payload=response_payload,
                tenant_id=message.tenant_id,
                user_id=message.user_id
            )
        
        elif message.type == MessageType.AGENT_STATUS:
            # Update agent status
            agent_id = message.payload.get("agent_id")
            status = message.payload.get("status")
            
            # Broadcast status update to all connections in tenant
            await websocket_service.broadcast_to_tenant(
                tenant_id=connection.tenant_id,
                message=WebSocketMessage(
                    type=MessageType.AGENT_STATUS,
                    payload={
                        "agent_id": agent_id,
                        "status": status,
                        "timestamp": time.time()
                    }
                )
            )
        
        return None
    
    def get_handled_types(self) -> List[MessageType]:
        return [
            MessageType.AGENT_MESSAGE,
            MessageType.AGENT_TYPING,
            MessageType.AGENT_STATUS,
            MessageType.AGENT_TRANSFER
        ]


class FileProcessingMessageHandler(MessageHandler):
    """Handles file processing related WebSocket messages."""
    
    async def handle_message(
        self, 
        message: WebSocketMessage, 
        connection: ConnectionInfo,
        websocket_service: "UnifiedWebSocketService"
    ) -> Optional[WebSocketMessage]:
        """Handle file processing messages."""
        if message.type in [
            MessageType.FILE_PROCESSING_STARTED,
            MessageType.FILE_PROCESSING_PROGRESS,
            MessageType.FILE_PROCESSING_COMPLETED,
            MessageType.FILE_PROCESSING_ERROR
        ]:
            # File processing messages are typically broadcast to all tenant connections
            await websocket_service.broadcast_to_tenant(
                tenant_id=connection.tenant_id,
                message=message
            )
        
        return None
    
    def get_handled_types(self) -> List[MessageType]:
        return [
            MessageType.FILE_UPLOADED,
            MessageType.FILE_PROCESSING_STARTED,
            MessageType.FILE_PROCESSING_PROGRESS,
            MessageType.FILE_PROCESSING_COMPLETED,
            MessageType.FILE_PROCESSING_ERROR
        ]


class CategorizationMessageHandler(MessageHandler):
    """Handles categorization related WebSocket messages."""
    
    async def handle_message(
        self, 
        message: WebSocketMessage, 
        connection: ConnectionInfo,
        websocket_service: "UnifiedWebSocketService"
    ) -> Optional[WebSocketMessage]:
        """Handle categorization messages."""
        # Categorization progress updates should be broadcast
        if message.type in [
            MessageType.CATEGORIZATION_STARTED,
            MessageType.CATEGORIZATION_PROGRESS,
            MessageType.CATEGORIZATION_COMPLETED,
            MessageType.CATEGORIZATION_ERROR
        ]:
            await websocket_service.broadcast_to_tenant(
                tenant_id=connection.tenant_id,
                message=message
            )
        
        return None
    
    def get_handled_types(self) -> List[MessageType]:
        return [
            MessageType.CATEGORIZATION_STARTED,
            MessageType.CATEGORIZATION_PROGRESS,
            MessageType.CATEGORIZATION_COMPLETED,
            MessageType.CATEGORIZATION_ERROR,
            MessageType.TRANSACTION_CATEGORIZED
        ]


class SystemMessageHandler(MessageHandler):
    """Handles system-level WebSocket messages."""
    
    async def handle_message(
        self, 
        message: WebSocketMessage, 
        connection: ConnectionInfo,
        websocket_service: "UnifiedWebSocketService"
    ) -> Optional[WebSocketMessage]:
        """Handle system messages."""
        if message.type == MessageType.HEARTBEAT:
            # Update connection heartbeat
            connection.update_heartbeat()
            
            # Send heartbeat response
            return WebSocketMessage(
                type=MessageType.HEARTBEAT,
                payload={"status": "alive", "server_time": time.time()},
                tenant_id=message.tenant_id,
                user_id=message.user_id
            )
        
        elif message.type == MessageType.SYSTEM_NOTIFICATION:
            # System notifications can be broadcast to all tenant connections
            await websocket_service.broadcast_to_tenant(
                tenant_id=connection.tenant_id,
                message=message
            )
        
        return None
    
    def get_handled_types(self) -> List[MessageType]:
        return [
            MessageType.HEARTBEAT,
            MessageType.SYSTEM_NOTIFICATION,
            MessageType.SYSTEM_ERROR,
            MessageType.DATA_REFRESH_REQUIRED,
            MessageType.UI_STATE_UPDATE
        ]


class UnifiedWebSocketService:
    """
    Unified WebSocket service that consolidates all WebSocket patterns.
    
    Features:
    - Multi-tenant connection management with isolation
    - Event-based message routing with pluggable handlers
    - Auto-cleanup of stale connections
    - Message queuing and retry mechanisms
    - Subscription-based filtering
    - Performance monitoring and health checks
    """
    
    def __init__(self, heartbeat_interval: float = 30.0, heartbeat_timeout: float = 60.0):
        """Initialize unified WebSocket service."""
        self.connections: Dict[WebSocket, ConnectionInfo] = {}
        self.tenant_connections: Dict[int, Set[WebSocket]] = {}
        self.message_handlers: Dict[MessageType, MessageHandler] = {}
        self.message_queue: Dict[str, List[WebSocketMessage]] = {}  # user_id -> messages
        
        self.heartbeat_interval = heartbeat_interval
        self.heartbeat_timeout = heartbeat_timeout
        
        # Statistics
        self.stats = {
            "connections_total": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "errors": 0,
            "active_connections": 0
        }
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._heartbeat_task: Optional[asyncio.Task] = None
        
        # Register default handlers
        self._register_default_handlers()
        
        logger.info("Unified WebSocket service initialized")
    
    def _register_default_handlers(self):
        """Register default message handlers."""
        handlers = [
            AgentMessageHandler(),
            FileProcessingMessageHandler(),
            CategorizationMessageHandler(),
            SystemMessageHandler()
        ]
        
        for handler in handlers:
            for message_type in handler.get_handled_types():
                self.message_handlers[message_type] = handler
    
    def register_handler(self, message_type: MessageType, handler: MessageHandler):
        """Register a custom message handler."""
        self.message_handlers[message_type] = handler
        logger.info(f"Registered handler for {message_type.value}")
    
    async def connect(
        self, 
        websocket: WebSocket, 
        tenant_id: int, 
        user_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Accept and register a new WebSocket connection."""
        await websocket.accept()
        
        # Create connection info
        connection = ConnectionInfo(
            websocket=websocket,
            tenant_id=tenant_id,
            user_id=user_id,
            connected_at=time.time(),
            last_heartbeat=time.time(),
            metadata=metadata or {}
        )
        
        # Register connection
        self.connections[websocket] = connection
        
        # Add to tenant connections
        if tenant_id not in self.tenant_connections:
            self.tenant_connections[tenant_id] = set()
        self.tenant_connections[tenant_id].add(websocket)
        
        # Update statistics
        self.stats["connections_total"] += 1
        self.stats["active_connections"] = len(self.connections)
        
        logger.info(f"WebSocket connected: tenant={tenant_id}, user={user_id}")
        
        # Send connection established message
        await self.send_to_connection(
            websocket,
            WebSocketMessage(
                type=MessageType.CONNECTION_ESTABLISHED,
                payload={
                    "message": "Connected to giki.ai real-time updates",
                    "capabilities": [msg_type.value for msg_type in MessageType],
                    "server_time": time.time(),
                    "heartbeat_interval": self.heartbeat_interval
                },
                tenant_id=tenant_id,
                user_id=user_id
            )
        )
        
        # Deliver queued messages
        await self._deliver_queued_messages(user_id, websocket)
        
        # Start background tasks if not running
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_connections())
        
        if self._heartbeat_task is None or self._heartbeat_task.done():
            self._heartbeat_task = asyncio.create_task(self._heartbeat_monitor())
    
    def disconnect(self, websocket: WebSocket):
        """Disconnect and cleanup a WebSocket connection."""
        if websocket not in self.connections:
            return
        
        connection = self.connections[websocket]
        tenant_id = connection.tenant_id
        user_id = connection.user_id
        
        # Remove from connections
        del self.connections[websocket]
        
        # Remove from tenant connections
        if tenant_id in self.tenant_connections:
            self.tenant_connections[tenant_id].discard(websocket)
            if not self.tenant_connections[tenant_id]:
                del self.tenant_connections[tenant_id]
        
        # Update statistics
        self.stats["active_connections"] = len(self.connections)
        
        logger.info(f"WebSocket disconnected: tenant={tenant_id}, user={user_id}")
    
    async def send_to_connection(self, websocket: WebSocket, message: WebSocketMessage):
        """Send a message to a specific WebSocket connection."""
        if websocket not in self.connections:
            logger.warning("Attempted to send message to unregistered connection")
            return False
        
        if websocket.application_state != WebSocketState.CONNECTED:
            logger.warning("Attempted to send message to disconnected WebSocket")
            self.disconnect(websocket)
            return False
        
        try:
            await websocket.send_text(json.dumps(message.to_dict()))
            self.stats["messages_sent"] += 1
            return True
        except Exception as e:
            logger.error(f"Error sending message to WebSocket: {e}")
            self.stats["errors"] += 1
            self.disconnect(websocket)
            return False
    
    async def send_to_user(self, user_id: str, message: WebSocketMessage):
        """Send a message to all connections for a specific user."""
        user_connections = [
            ws for ws, conn in self.connections.items() 
            if conn.user_id == user_id
        ]
        
        if not user_connections:
            # Queue message for when user connects
            if user_id not in self.message_queue:
                self.message_queue[user_id] = []
            
            # Limit queue size and remove expired messages
            self.message_queue[user_id].append(message)
            self.message_queue[user_id] = [
                msg for msg in self.message_queue[user_id][-100:]  # Keep last 100
                if not msg.is_expired()
            ]
            
            logger.debug(f"Queued message for offline user: {user_id}")
            return
        
        # Send to all user connections
        for websocket in user_connections:
            await self.send_to_connection(websocket, message)
    
    async def broadcast_to_tenant(self, tenant_id: int, message: WebSocketMessage):
        """Broadcast a message to all connections for a tenant."""
        if tenant_id not in self.tenant_connections:
            logger.debug(f"No connections for tenant {tenant_id}")
            return
        
        connections = list(self.tenant_connections[tenant_id])  # Copy to avoid modification during iteration
        
        for websocket in connections:
            # Check subscription filters
            connection = self.connections.get(websocket)
            if connection and message.type not in connection.subscriptions:
                continue  # Skip if not subscribed to this message type
            
            await self.send_to_connection(websocket, message)
    
    async def broadcast_to_all(self, message: WebSocketMessage):
        """Broadcast a message to all connected clients."""
        connections = list(self.connections.keys())  # Copy to avoid modification during iteration
        
        for websocket in connections:
            await self.send_to_connection(websocket, message)
    
    async def handle_message(self, websocket: WebSocket, raw_message: str):
        """Handle incoming WebSocket message."""
        if websocket not in self.connections:
            logger.warning("Received message from unregistered connection")
            return
        
        connection = self.connections[websocket]
        
        try:
            # Parse message
            message_data = json.loads(raw_message)
            message = WebSocketMessage.from_dict(message_data)
            
            # Update message with connection info if not provided
            if message.tenant_id is None:
                message.tenant_id = connection.tenant_id
            if message.user_id is None:
                message.user_id = connection.user_id
            
            self.stats["messages_received"] += 1
            
            # Get appropriate handler
            handler = self.message_handlers.get(message.type)
            if handler:
                response = await handler.handle_message(message, connection, self)
                if response:
                    await self.send_to_connection(websocket, response)
            else:
                logger.warning(f"No handler registered for message type: {message.type}")
        
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in WebSocket message: {e}")
            await self.send_to_connection(
                websocket,
                WebSocketMessage(
                    type=MessageType.SYSTEM_ERROR,
                    payload={"error": "Invalid JSON format"},
                    tenant_id=connection.tenant_id,
                    user_id=connection.user_id
                )
            )
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
            self.stats["errors"] += 1
            await self.send_to_connection(
                websocket,
                WebSocketMessage(
                    type=MessageType.SYSTEM_ERROR,
                    payload={"error": "Message processing error"},
                    tenant_id=connection.tenant_id,
                    user_id=connection.user_id
                )
            )
    
    def subscribe(self, websocket: WebSocket, message_types: List[MessageType]):
        """Subscribe a connection to specific message types."""
        if websocket not in self.connections:
            logger.warning("Attempted to subscribe unregistered connection")
            return
        
        connection = self.connections[websocket]
        connection.subscriptions.update(message_types)
        
        logger.debug(f"Connection subscribed to {len(message_types)} message types")
    
    def unsubscribe(self, websocket: WebSocket, message_types: List[MessageType]):
        """Unsubscribe a connection from specific message types."""
        if websocket not in self.connections:
            return
        
        connection = self.connections[websocket]
        connection.subscriptions.difference_update(message_types)
    
    async def _deliver_queued_messages(self, user_id: str, websocket: WebSocket):
        """Deliver queued messages to a newly connected user."""
        if user_id not in self.message_queue:
            return
        
        messages = self.message_queue[user_id]
        delivered = 0
        
        for message in messages:
            if not message.is_expired():
                await self.send_to_connection(websocket, message)
                delivered += 1
        
        # Clear delivered messages
        del self.message_queue[user_id]
        
        if delivered > 0:
            logger.info(f"Delivered {delivered} queued messages to user {user_id}")
    
    async def _cleanup_connections(self):
        """Background task to cleanup stale connections."""
        while True:
            try:
                await asyncio.sleep(60)  # Cleanup every minute
                
                stale_connections = []
                
                for websocket, connection in self.connections.items():
                    if not connection.is_alive(self.heartbeat_timeout):
                        stale_connections.append(websocket)
                    elif websocket.application_state != WebSocketState.CONNECTED:
                        stale_connections.append(websocket)
                
                # Cleanup stale connections
                for websocket in stale_connections:
                    self.disconnect(websocket)
                
                if stale_connections:
                    logger.info(f"Cleaned up {len(stale_connections)} stale connections")
            
            except Exception as e:
                logger.error(f"Error in connection cleanup: {e}")
    
    async def _heartbeat_monitor(self):
        """Background task to monitor connection health."""
        while True:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                
                # Send heartbeat to all connections
                heartbeat_message = WebSocketMessage(
                    type=MessageType.HEARTBEAT,
                    payload={"server_time": time.time()}
                )
                
                await self.broadcast_to_all(heartbeat_message)
                
            except Exception as e:
                logger.error(f"Error in heartbeat monitor: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics."""
        return {
            **self.stats,
            "active_tenants": len(self.tenant_connections),
            "queued_messages": sum(len(msgs) for msgs in self.message_queue.values()),
            "registered_handlers": len(self.message_handlers)
        }
    
    def get_connection_info(self, websocket: WebSocket) -> Optional[ConnectionInfo]:
        """Get connection information for a WebSocket."""
        return self.connections.get(websocket)
    
    async def shutdown(self):
        """Shutdown the WebSocket service gracefully."""
        logger.info("Shutting down unified WebSocket service")
        
        # Cancel background tasks
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
        
        if self._heartbeat_task and not self._heartbeat_task.done():
            self._heartbeat_task.cancel()
        
        # Close all connections
        connections = list(self.connections.keys())
        for websocket in connections:
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.close(code=1001, reason="Server shutdown")
            self.disconnect(websocket)
        
        logger.info("Unified WebSocket service shutdown complete")


# ====== CONVENIENCE FUNCTIONS AND GLOBAL INSTANCE ======

# Global service instance
_websocket_service: Optional[UnifiedWebSocketService] = None


def get_websocket_service() -> UnifiedWebSocketService:
    """Get or create the global WebSocket service instance."""
    global _websocket_service
    if _websocket_service is None:
        _websocket_service = UnifiedWebSocketService()
    return _websocket_service


async def initialize_websocket_service(
    heartbeat_interval: float = 30.0,
    heartbeat_timeout: float = 60.0
) -> UnifiedWebSocketService:
    """Initialize the global WebSocket service."""
    global _websocket_service
    _websocket_service = UnifiedWebSocketService(heartbeat_interval, heartbeat_timeout)
    return _websocket_service


# ====== CONVENIENCE MESSAGE BUILDERS ======

def create_agent_message(
    content: str,
    agent_id: str,
    session_id: str,
    tenant_id: int,
    user_id: str,
    metadata: Optional[Dict[str, Any]] = None
) -> WebSocketMessage:
    """Create an agent message."""
    return WebSocketMessage(
        type=MessageType.AGENT_MESSAGE,
        payload={
            "content": content,
            "agent_id": agent_id,
            "session_id": session_id,
            "metadata": metadata or {}
        },
        tenant_id=tenant_id,
        user_id=user_id
    )


def create_file_progress_message(
    file_id: str,
    filename: str,
    progress: int,
    total_rows: int,
    processed_rows: int,
    tenant_id: int,
    user_id: str
) -> WebSocketMessage:
    """Create a file processing progress message."""
    return WebSocketMessage(
        type=MessageType.FILE_PROCESSING_PROGRESS,
        payload={
            "file_id": file_id,
            "filename": filename,
            "progress_percent": progress,
            "total_rows": total_rows,
            "processed_rows": processed_rows,
            "status": "processing"
        },
        tenant_id=tenant_id,
        user_id=user_id
    )


def create_categorization_progress_message(
    batch_id: str,
    total_transactions: int,
    processed_transactions: int,
    tenant_id: int,
    user_id: str,
    accuracy_score: Optional[float] = None
) -> WebSocketMessage:
    """Create a categorization progress message."""
    progress = int((processed_transactions / total_transactions) * 100) if total_transactions > 0 else 0
    
    return WebSocketMessage(
        type=MessageType.CATEGORIZATION_PROGRESS,
        payload={
            "batch_id": batch_id,
            "total_transactions": total_transactions,
            "processed_transactions": processed_transactions,
            "progress_percent": progress,
            "accuracy_score": accuracy_score
        },
        tenant_id=tenant_id,
        user_id=user_id
    )


def create_system_notification(
    title: str,
    message: str,
    notification_type: str = "info",
    tenant_id: Optional[int] = None,
    user_id: Optional[str] = None
) -> WebSocketMessage:
    """Create a system notification message."""
    return WebSocketMessage(
        type=MessageType.SYSTEM_NOTIFICATION,
        payload={
            "title": title,
            "message": message,
            "notification_type": notification_type,  # info, success, warning, error
            "timestamp": time.time()
        },
        tenant_id=tenant_id,
        user_id=user_id
    )


# ====== BACKWARD COMPATIBILITY ======

class WebSocketManager:
    """Backward compatibility class for existing WebSocketManager usage."""
    
    def __init__(self):
        self.service = get_websocket_service()
    
    async def connect(self, websocket: WebSocket, tenant_id: int, user_id: str):
        """Backward compatible connect method."""
        await self.service.connect(websocket, tenant_id, user_id)
    
    def disconnect(self, websocket: WebSocket):
        """Backward compatible disconnect method."""
        self.service.disconnect(websocket)
    
    async def send_personal_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """Backward compatible send personal message method."""
        ws_message = WebSocketMessage(
            type=MessageType.SYSTEM_NOTIFICATION if message.get("type") == "system" else MessageType.AGENT_MESSAGE,
            payload=message.get("data", message)
        )
        await self.service.send_to_connection(websocket, ws_message)
    
    async def broadcast_to_tenant(self, tenant_id: int, message: Dict[str, Any]):
        """Backward compatible broadcast to tenant method."""
        ws_message = WebSocketMessage(
            type=MessageType.SYSTEM_NOTIFICATION if message.get("type") == "system" else MessageType.AGENT_MESSAGE,
            payload=message.get("data", message),
            tenant_id=tenant_id
        )
        await self.service.broadcast_to_tenant(tenant_id, ws_message)


# ====== EXPORTS ======

__all__ = [
    # Main classes
    "UnifiedWebSocketService",
    "WebSocketMessage",
    "ConnectionInfo",
    "MessageHandler",
    "MessageType",
    "Priority",
    
    # Specific handlers
    "AgentMessageHandler",
    "FileProcessingMessageHandler", 
    "CategorizationMessageHandler",
    "SystemMessageHandler",
    
    # Convenience functions
    "get_websocket_service",
    "initialize_websocket_service",
    "create_agent_message",
    "create_file_progress_message",
    "create_categorization_progress_message",
    "create_system_notification",
    
    # Backward compatibility
    "WebSocketManager"
]