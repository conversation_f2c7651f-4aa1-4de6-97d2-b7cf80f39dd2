"""
Unified Base Tools
=================

Consolidated base tool classes that eliminate duplicate patterns across domains.
This consolidates common patterns found in:
- accuracy/tools.py, categories/tools.py, transactions/tools.py, files/tools.py
- intelligence/tools.py, reports/tools.py, dashboard/tools.py, onboarding/tools.py

Key Features:
- Standardized tool function signatures and response formats
- Consistent error handling and logging patterns
- Service integration abstractions
- Database operation utilities
- Tool registration and discovery mechanisms
"""

import logging
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union

import asyncpg
from google.adk.tools import FunctionTool

from ..exceptions import ServiceError
from ..services.base_service import BaseService

logger = logging.getLogger(__name__)

# Type variables
ServiceType = TypeVar('ServiceType', bound=BaseService)
ToolFunction = Callable[..., Dict[str, Any]]


class ToolError(ServiceError):
    """Base exception for tool operations."""
    pass


class ToolResponse:
    """Standardized tool response wrapper."""
    
    def __init__(
        self,
        success: bool = True,
        data: Any = None,
        error: Optional[str] = None,
        error_type: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tool_name: Optional[str] = None
    ):
        self.success = success
        self.data = data
        self.error = error
        self.error_type = error_type
        self.metadata = metadata or {}
        self.tool_name = tool_name
        self.timestamp = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert response to dictionary format."""
        result = {
            "success": self.success,
            "timestamp": self.timestamp
        }
        
        if self.success:
            if self.data is not None:
                result["data"] = self.data
            if self.metadata:
                result["metadata"] = self.metadata
        else:
            result["error"] = self.error
            if self.error_type:
                result["error_type"] = self.error_type
        
        if self.tool_name:
            result["tool_name"] = self.tool_name
        
        return result
    
    @classmethod
    def success(
        cls,
        data: Any = None,
        metadata: Optional[Dict[str, Any]] = None,
        tool_name: Optional[str] = None
    ) -> 'ToolResponse':
        """Create a successful response."""
        return cls(success=True, data=data, metadata=metadata, tool_name=tool_name)
    
    @classmethod
    def error(
        cls,
        error: str,
        error_type: Optional[str] = None,
        tool_name: Optional[str] = None
    ) -> 'ToolResponse':
        """Create an error response."""
        return cls(
            success=False,
            error=error,
            error_type=error_type,
            tool_name=tool_name
        )


class BaseTool(ABC):
    """
    Base class for domain tools with standardized patterns.
    
    Provides:
    - Consistent error handling and logging
    - Service integration abstractions
    - Database operation utilities
    - Standardized response formatting
    """
    
    def __init__(self, service_class: Optional[Type[ServiceType]] = None):
        self.service_class = service_class
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def create_service(self, db: asyncpg.Connection) -> Optional[ServiceType]:
        """Create service instance if service class is provided."""
        if self.service_class:
            return self.service_class(db)
        return None
    
    def log_tool_operation(
        self,
        tool_name: str,
        tenant_id: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log tool operation with standardized format."""
        message = f"Tool: {tool_name}"
        if tenant_id:
            message += f" | Tenant: {tenant_id}"
        if details:
            message += f" | Details: {details}"
        self.logger.info(message)
    
    def log_tool_error(
        self,
        tool_name: str,
        error: Exception,
        tenant_id: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log tool error with standardized format."""
        message = f"Tool Error: {tool_name} | Error: {str(error)}"
        if tenant_id:
            message += f" | Tenant: {tenant_id}"
        if details:
            message += f" | Details: {details}"
        self.logger.error(message, exc_info=True)
    
    async def execute_with_error_handling(
        self,
        tool_func: Callable,
        tool_name: str,
        *args,
        **kwargs
    ) -> Dict[str, Any]:
        """Execute tool function with standardized error handling."""
        try:
            self.log_tool_operation(
                tool_name,
                tenant_id=kwargs.get('tenant_id'),
                details={"args_count": len(args), "kwargs_keys": list(kwargs.keys())}
            )
            
            result = await tool_func(*args, **kwargs)
            
            # Ensure result is a ToolResponse or convert it
            if isinstance(result, ToolResponse):
                return result.to_dict()
            elif isinstance(result, dict):
                # Wrap existing dict response
                return ToolResponse.success(data=result, tool_name=tool_name).to_dict()
            else:
                return ToolResponse.success(data=result, tool_name=tool_name).to_dict()
                
        except Exception as e:
            self.log_tool_error(
                tool_name,
                e,
                tenant_id=kwargs.get('tenant_id')
            )
            
            error_type = type(e).__name__
            return ToolResponse.error(
                error=str(e),
                error_type=error_type,
                tool_name=tool_name
            ).to_dict()


class DatabaseTool(BaseTool):
    """
    Base class for tools that perform direct database operations.
    
    Provides utilities for common database patterns found across tool implementations.
    """
    
    async def fetch_one_safe(
        self,
        db: asyncpg.Connection,
        query: str,
        *args,
        timeout: float = 10.0
    ) -> Optional[Dict[str, Any]]:
        """Safely fetch one row with error handling."""
        try:
            row = await db.fetchrow(query, *args, timeout=timeout)
            return dict(row) if row else None
        except Exception as e:
            self.logger.error(f"Database fetch_one error: {e}")
            raise ToolError(f"Database operation failed: {str(e)}")
    
    async def fetch_all_safe(
        self,
        db: asyncpg.Connection,
        query: str,
        *args,
        timeout: float = 15.0
    ) -> List[Dict[str, Any]]:
        """Safely fetch all rows with error handling."""
        try:
            rows = await db.fetch(query, *args, timeout=timeout)
            return [dict(row) for row in rows]
        except Exception as e:
            self.logger.error(f"Database fetch_all error: {e}")
            raise ToolError(f"Database operation failed: {str(e)}")
    
    async def execute_safe(
        self,
        db: asyncpg.Connection,
        query: str,
        *args,
        timeout: float = 10.0
    ) -> str:
        """Safely execute query with error handling."""
        try:
            return await db.execute(query, *args, timeout=timeout)
        except Exception as e:
            self.logger.error(f"Database execute error: {e}")
            raise ToolError(f"Database operation failed: {str(e)}")


class ServiceTool(BaseTool):
    """
    Base class for tools that use service layer abstractions.
    
    Provides standardized service integration patterns.
    """
    
    def __init__(self, service_class: Type[ServiceType]):
        super().__init__(service_class)
        if not service_class:
            raise ValueError("ServiceTool requires a service_class")
    
    async def with_service(
        self,
        db: asyncpg.Connection,
        service_method: str,
        *args,
        **kwargs
    ) -> Any:
        """Execute service method with error handling."""
        try:
            service = self.create_service(db)
            method = getattr(service, service_method)
            return await method(*args, **kwargs)
        except Exception as e:
            self.logger.error(f"Service method {service_method} failed: {e}")
            raise ToolError(f"Service operation failed: {str(e)}")


class ToolRegistry:
    """Registry for managing and discovering tools across domains."""
    
    def __init__(self):
        self._tools: Dict[str, ToolFunction] = {}
        self._tool_metadata: Dict[str, Dict[str, Any]] = {}
        self._domain_tools: Dict[str, List[str]] = {}
    
    def register_tool(
        self,
        name: str,
        tool_func: ToolFunction,
        domain: str,
        description: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Register a tool function."""
        self._tools[name] = tool_func
        self._tool_metadata[name] = {
            "domain": domain,
            "description": description or f"Tool function: {name}",
            "metadata": metadata or {}
        }
        
        if domain not in self._domain_tools:
            self._domain_tools[domain] = []
        self._domain_tools[domain].append(name)
        
        logger.info(f"Registered tool: {name} (domain: {domain})")
    
    def get_tool(self, name: str) -> Optional[ToolFunction]:
        """Get a registered tool function."""
        return self._tools.get(name)
    
    def get_domain_tools(self, domain: str) -> List[str]:
        """Get all tools for a specific domain."""
        return self._domain_tools.get(domain, [])
    
    def list_tools(self) -> Dict[str, Dict[str, Any]]:
        """List all registered tools with metadata."""
        return self._tool_metadata.copy()
    
    def create_adk_tools(
        self,
        domain: Optional[str] = None,
        tool_names: Optional[List[str]] = None
    ) -> List[FunctionTool]:
        """Create ADK FunctionTool instances."""
        tools = []
        
        if tool_names:
            # Create specific tools
            for name in tool_names:
                if name in self._tools:
                    metadata = self._tool_metadata[name]
                    tools.append(FunctionTool(
                        func=self._tools[name]
                    ))
        elif domain:
            # Create all tools for domain
            domain_tool_names = self.get_domain_tools(domain)
            for name in domain_tool_names:
                metadata = self._tool_metadata[name]
                tools.append(FunctionTool(
                    func=self._tools[name]
                ))
        else:
            # Create all tools
            for name, func in self._tools.items():
                metadata = self._tool_metadata[name]
                tools.append(FunctionTool(
                    func=func
                ))
        
        return tools


# Global tool registry
_global_registry: Optional[ToolRegistry] = None


def get_tool_registry() -> ToolRegistry:
    """Get or create global tool registry."""
    global _global_registry
    if _global_registry is None:
        _global_registry = ToolRegistry()
    return _global_registry


def register_tool(
    name: str,
    domain: str,
    description: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
):
    """Decorator for registering tool functions."""
    def decorator(func: ToolFunction) -> ToolFunction:
        registry = get_tool_registry()
        registry.register_tool(name, func, domain, description, metadata)
        return func
    return decorator


# Export all base tool components
__all__ = [
    "BaseTool",
    "DatabaseTool", 
    "ServiceTool",
    "ToolResponse",
    "ToolRegistry",
    "ToolError",
    "get_tool_registry",
    "register_tool",
]
