"""
Unified Tools Initialization
===========================

This module initializes all unified tools and ensures they are registered
with the global tool registry.

Import this module to automatically register all available unified tools.
"""

import logging

logger = logging.getLogger(__name__)

# Import all unified tools to trigger registration
try:
    from ...domains.accuracy.unified_tools import (
        create_accuracy_test_tool,
        run_accuracy_test_tool,
        run_temporal_validation_tool,
        measure_baseline_accuracy_tool,
        accuracy_tools
    )
    logger.info("Registered accuracy domain tools")
except ImportError as e:
    logger.warning(f"Failed to import accuracy tools: {e}")

try:
    from ...domains.reports.unified_tools import (
        generate_spending_by_category_report_tool,
        generate_spending_by_entity_report_tool,
        generate_transaction_summary_report_tool,
        generate_monthly_trends_report_tool,
        reports_tools
    )
    logger.info("Registered reports domain tools")
except ImportError as e:
    logger.warning(f"Failed to import reports tools: {e}")

try:
    from ...domains.intelligence.consolidated_tools import (
        detect_accounting_system_tool,
        extract_entities_tool,
        process_descriptions_tool,
        analyze_transaction_batch_tool,
        intelligence_tools
    )
    logger.info("Registered intelligence domain tools")
except ImportError as e:
    logger.warning(f"Failed to import intelligence tools: {e}")

# Import base tools and factory
from .base_tools import (
    BaseTool,
    DatabaseTool,
    ServiceTool,
    ToolResponse,
    ToolRegistry,
    ToolError,
    get_tool_registry,
    register_tool
)

from .tool_factory import (
    UnifiedToolFactory,
    get_tool_factory,
    create_tool_factory
)

# Initialize and verify tool registration
def initialize_tools():
    """Initialize all tools and verify registration."""
    registry = get_tool_registry()
    factory = get_tool_factory()
    
    # Get tool statistics
    stats = factory.get_tool_statistics()
    
    logger.info(f"Tool initialization complete:")
    logger.info(f"  - Total tools: {stats['total_tools']}")
    logger.info(f"  - Domains: {stats['domains']}")
    logger.info(f"  - AI-powered tools: {stats['ai_powered_tools']}")
    logger.info(f"  - Available domains: {stats['available_domains']}")
    
    return {
        "registry": registry,
        "factory": factory,
        "statistics": stats
    }

# Auto-initialize when module is imported
_initialization_result = initialize_tools()

# Export all components
__all__ = [
    # Base tools
    "BaseTool",
    "DatabaseTool", 
    "ServiceTool",
    "ToolResponse",
    "ToolRegistry",
    "ToolError",
    "get_tool_registry",
    "register_tool",
    
    # Tool factory
    "UnifiedToolFactory",
    "get_tool_factory",
    "create_tool_factory",
    
    # Initialization
    "initialize_tools",
    
    # Tool instances
    "accuracy_tools",
    "reports_tools", 
    "intelligence_tools",
    
    # Individual tool functions
    "create_accuracy_test_tool",
    "run_accuracy_test_tool",
    "run_temporal_validation_tool",
    "measure_baseline_accuracy_tool",
    "generate_spending_by_category_report_tool",
    "generate_spending_by_entity_report_tool",
    "generate_transaction_summary_report_tool",
    "generate_monthly_trends_report_tool",
    "detect_accounting_system_tool",
    "extract_entities_tool",
    "process_descriptions_tool",
    "analyze_transaction_batch_tool",
]
