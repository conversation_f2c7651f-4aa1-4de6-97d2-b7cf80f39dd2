"""
Base Middleware Classes - Unified middleware patterns
====================================================

This module provides base classes and utilities for creating consistent middleware
across the application. It consolidates common middleware patterns and provides
a unified architecture for request/response processing.

Key features:
- Base middleware class with common functionality
- Request context management
- Performance tracking utilities
- Error handling integration
- Configurable middleware chains
- Standardized logging and metrics
"""

import time
import uuid
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Set, Callable, Union
from dataclasses import dataclass, field

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware


logger = logging.getLogger(__name__)


@dataclass
class MiddlewareConfig:
    """Configuration for middleware behavior."""
    
    # General settings
    enabled: bool = True
    priority: int = 100  # Lower numbers = higher priority
    
    # Path filtering
    include_paths: Optional[Set[str]] = None
    exclude_paths: Optional[Set[str]] = None
    path_patterns: Optional[Set[str]] = None
    
    # Method filtering
    include_methods: Optional[Set[str]] = None
    exclude_methods: Optional[Set[str]] = None
    
    # Performance settings
    timeout_seconds: Optional[float] = None
    max_execution_time: Optional[float] = None
    
    # Logging settings
    log_requests: bool = True
    log_responses: bool = False
    log_performance: bool = True
    
    # Custom settings
    custom_settings: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RequestContext:
    """Unified request context for middleware chain."""
    
    # Request identification
    request_id: str
    correlation_id: str
    
    # Timing information
    start_time: float
    
    # Request metadata
    method: str
    path: str
    user_agent: str
    client_ip: str
    
    # Authentication context
    user_id: Optional[str] = None
    tenant_id: Optional[str] = None
    
    # Performance tracking
    middleware_timings: Dict[str, float] = field(default_factory=dict)
    
    # Custom context data
    custom_data: Dict[str, Any] = field(default_factory=dict)


class BaseUnifiedMiddleware(BaseHTTPMiddleware, ABC):
    """
    Base class for all unified middleware components.
    
    Provides common functionality and standardized patterns for:
    - Request context management
    - Performance tracking
    - Error handling
    - Logging and metrics
    - Path and method filtering
    """
    
    def __init__(self, app, config: Optional[MiddlewareConfig] = None):
        """Initialize base middleware with configuration."""
        super().__init__(app)
        self.config = config or MiddlewareConfig()
        self.name = self.__class__.__name__
        
        # Default exclude paths for performance
        self.default_exclude_paths = {
            "/health",
            "/api/v1/health", 
            "/docs",
            "/redoc",
            "/openapi.json",
            "/static",
            "/favicon.ico"
        }
        
        # Merge with config exclude paths
        if self.config.exclude_paths:
            self.config.exclude_paths.update(self.default_exclude_paths)
        else:
            self.config.exclude_paths = self.default_exclude_paths.copy()
    
    async def dispatch(self, request: Request, call_next: Callable):
        """Main dispatch method with unified processing."""
        
        # Check if middleware should process this request
        if not self._should_process_request(request):
            return await call_next(request)
        
        # Create or get request context
        context = self._get_or_create_context(request)
        
        # Record middleware start time
        middleware_start = time.time()
        
        try:
            # Pre-processing hook
            await self._pre_process(request, context)
            
            # Process request
            response = await self._process_request(request, call_next, context)
            
            # Post-processing hook
            await self._post_process(request, response, context)
            
            return response
            
        except Exception as e:
            # Error handling hook
            return await self._handle_error(request, e, context)
            
        finally:
            # Record middleware execution time
            middleware_time = time.time() - middleware_start
            context.middleware_timings[self.name] = middleware_time
            
            # Cleanup hook
            await self._cleanup(request, context)
    
    def _should_process_request(self, request: Request) -> bool:
        """Determine if this middleware should process the request."""
        
        if not self.config.enabled:
            return False
        
        path = request.url.path
        method = request.method
        
        # Check exclude paths
        if self.config.exclude_paths:
            for exclude_path in self.config.exclude_paths:
                if exclude_path in path:
                    return False
        
        # Check include paths (if specified)
        if self.config.include_paths:
            path_included = any(include_path in path for include_path in self.config.include_paths)
            if not path_included:
                return False
        
        # Check exclude methods
        if self.config.exclude_methods and method in self.config.exclude_methods:
            return False
        
        # Check include methods (if specified)
        if self.config.include_methods and method not in self.config.include_methods:
            return False
        
        return True
    
    def _get_or_create_context(self, request: Request) -> RequestContext:
        """Get existing request context or create a new one."""
        
        # Check if context already exists
        if hasattr(request.state, 'unified_context'):
            return request.state.unified_context
        
        # Create new context
        context = RequestContext(
            request_id=getattr(request.state, 'request_id', str(uuid.uuid4())),
            correlation_id=getattr(request.state, 'correlation_id', str(uuid.uuid4())),
            start_time=getattr(request.state, 'start_time', time.time()),
            method=request.method,
            path=request.url.path,
            user_agent=request.headers.get("user-agent", ""),
            client_ip=self._get_client_ip(request),
        )
        
        # Store context in request state
        request.state.unified_context = context
        request.state.request_id = context.request_id
        request.state.correlation_id = context.correlation_id
        request.state.start_time = context.start_time
        
        return context
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct client
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"
    
    @abstractmethod
    async def _process_request(self, request: Request, call_next: Callable, context: RequestContext) -> Response:
        """Process the request - must be implemented by subclasses."""
        pass
    
    async def _pre_process(self, request: Request, context: RequestContext):
        """Pre-processing hook - override in subclasses if needed."""
        pass
    
    async def _post_process(self, request: Request, response: Response, context: RequestContext):
        """Post-processing hook - override in subclasses if needed."""
        pass
    
    async def _handle_error(self, request: Request, error: Exception, context: RequestContext) -> Response:
        """Error handling hook - override in subclasses if needed."""
        logger.error(f"Error in {self.name}: {error}", extra={
            "request_id": context.request_id,
            "correlation_id": context.correlation_id,
            "path": context.path,
            "method": context.method
        })
        
        # Re-raise the error to be handled by error handling middleware
        raise error
    
    async def _cleanup(self, request: Request, context: RequestContext):
        """Cleanup hook - override in subclasses if needed."""
        pass


class MiddlewareChain:
    """
    Manages a chain of unified middleware components.
    
    Provides utilities for:
    - Middleware registration and ordering
    - Configuration management
    - Performance monitoring
    - Debugging and diagnostics
    """
    
    def __init__(self):
        self.middleware_configs: Dict[str, MiddlewareConfig] = {}
        self.middleware_instances: List[BaseUnifiedMiddleware] = []
        self.performance_stats: Dict[str, Dict[str, float]] = {}
    
    def register_middleware(self, middleware_class: type, config: Optional[MiddlewareConfig] = None):
        """Register a middleware class with optional configuration."""
        name = middleware_class.__name__
        self.middleware_configs[name] = config or MiddlewareConfig()
        logger.info(f"Registered middleware: {name}")
    
    def get_middleware_stats(self) -> Dict[str, Any]:
        """Get performance statistics for all middleware."""
        return {
            "registered_middleware": list(self.middleware_configs.keys()),
            "performance_stats": self.performance_stats,
            "total_middleware": len(self.middleware_configs)
        }
    
    def clear_stats(self):
        """Clear performance statistics."""
        self.performance_stats.clear()


# Global middleware chain instance
middleware_chain = MiddlewareChain()
