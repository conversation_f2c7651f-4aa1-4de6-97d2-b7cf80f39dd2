"""
Unified Middleware Package for giki-ai API
==========================================

This package provides a comprehensive, unified middleware architecture that
consolidates all middleware patterns across the application.

Key components:
- Base middleware classes with common functionality
- Unified middleware implementations
- Enhanced versions of existing middleware
- Middleware management and configuration utilities

Usage:
    from giki_ai_api.shared.middleware import middleware_manager

    # Configure standard middleware stack
    middleware_manager.configure_standard_stack(app)

    # Or configure full stack with all components
    middleware_manager.configure_full_stack(app)
"""

# Base middleware architecture
from .base_middleware import (
    BaseUnifiedMiddleware,
    MiddlewareConfig,
    RequestContext,
    MiddlewareChain,
    middleware_chain
)

# Unified middleware implementations (temporarily disabled)
# from .unified_middleware import (
#     HealthCheckBypassMiddleware,
#     UnifiedMonitoringMiddleware,
#     UnifiedLoggingMiddleware,
#     UnifiedSecurityMiddleware,
#     MiddlewareManager,
#     middleware_manager
# )

# Enhanced middleware components (temporarily disabled)
# from .enhanced_middleware import (
#     UnifiedErrorHandlingMiddleware,
#     UnifiedPerformanceMiddleware,
#     UnifiedTimeoutMiddleware,
#     UnifiedCachingMiddleware,
#     UnifiedRateLimitingMiddleware
# )

# Legacy middleware (for backward compatibility)
from .performance import PerformanceMonitoringMiddleware, performance_metrics
from .caching import ResponseCacheMiddleware
from .error_handling import ErrorHandlingMiddleware
from .rate_limiting import RateLimitMiddleware
from .timeout_middleware import TimeoutMiddleware

__all__ = [
    # Base architecture
    "BaseUnifiedMiddleware",
    "MiddlewareConfig",
    "RequestContext",
    "MiddlewareChain",
    "middleware_chain",

    # Unified implementations
    "HealthCheckBypassMiddleware",
    "UnifiedMonitoringMiddleware",
    "UnifiedLoggingMiddleware",
    "UnifiedSecurityMiddleware",
    "MiddlewareManager",
    "middleware_manager",

    # Enhanced components
    "UnifiedErrorHandlingMiddleware",
    "UnifiedPerformanceMiddleware",
    "UnifiedTimeoutMiddleware",
    "UnifiedCachingMiddleware",
    "UnifiedRateLimitingMiddleware",

    # Legacy components (backward compatibility)
    "PerformanceMonitoringMiddleware",
    "performance_metrics",
    "ResponseCacheMiddleware",
    "ErrorHandlingMiddleware",
    "RateLimitMiddleware",
    "TimeoutMiddleware"
]
