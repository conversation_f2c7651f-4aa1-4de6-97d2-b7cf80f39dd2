# Unified Middleware Migration Guide

## Overview

This guide explains how to migrate from the existing middleware implementations to the new unified middleware architecture. The unified middleware provides better performance, consistency, and maintainability.

## Key Benefits of Unified Middleware

### 1. **Consolidated Patterns**
- Single base class for all middleware
- Consistent configuration and context management
- Standardized error handling and logging
- Unified performance tracking

### 2. **Improved Performance**
- Reduced overhead through shared context
- Intelligent path filtering
- Optimized health check bypass
- Better resource management

### 3. **Enhanced Monitoring**
- Comprehensive request context tracking
- Unified performance metrics
- Better error correlation
- Centralized logging context

### 4. **Simplified Configuration**
- Single configuration system
- Environment-based middleware selection
- Easy middleware stack management
- Consistent settings across all middleware

## Migration Steps

### Step 1: Update main.py

Replace the existing middleware setup in `main.py`:

```python
# OLD: Individual middleware setup
from ..shared.middleware.performance import PerformanceMonitoringMiddleware
from ..shared.middleware.caching import ResponseCacheMiddleware
from ..shared.middleware.error_handling import ErrorHandlingMiddleware
from ..shared.middleware.rate_limiting import RateLimitMiddleware
from ..shared.middleware.timeout_middleware import TimeoutMiddleware

# Add individual middleware
app.add_middleware(PerformanceMonitoringMiddleware)
app.add_middleware(ResponseCacheMiddleware)
# ... etc

# NEW: Unified middleware setup
from ..shared.middleware import middleware_manager

# Configure the full unified middleware stack
middleware_manager.configure_full_stack(
    app,
    enable_caching=True,
    enable_rate_limiting=True,
    enable_timeout=True
)
```

### Step 2: Remove Inline Middleware

Remove the inline middleware functions from `main.py`:

```python
# REMOVE: These inline middleware functions
@app.middleware("http")
async def ultra_fast_path_middleware(request, call_next):
    # ... remove this

@app.middleware("http") 
async def monitoring_middleware(request, call_next):
    # ... remove this

@app.middleware("http")
async def logging_context_middleware(request: Request, call_next):
    # ... remove this
```

These are now handled by the unified middleware components.

### Step 3: Update Security Middleware

Replace the security middleware imports:

```python
# OLD: Separate security middleware
from ..core.security_middleware import security_headers_middleware, request_id_middleware

app.middleware("http")(security_headers_middleware)
app.middleware("http")(request_id_middleware)

# NEW: Included in unified security middleware
# No additional setup needed - handled by middleware_manager.configure_full_stack()
```

### Step 4: Configuration Options

The unified middleware supports flexible configuration:

```python
from ..shared.middleware import middleware_manager, MiddlewareConfig

# Standard stack (basic middleware)
middleware_manager.configure_standard_stack(app)

# Full stack with all components
middleware_manager.configure_full_stack(
    app,
    enable_caching=os.getenv("ENABLE_CACHING", "true").lower() == "true",
    enable_rate_limiting=os.getenv("ENABLE_RATE_LIMITING", "true").lower() == "true", 
    enable_timeout=os.getenv("ENABLE_TIMEOUT", "true").lower() == "true"
)

# Custom configuration for specific middleware
custom_config = MiddlewareConfig(
    enabled=True,
    exclude_paths={"/health", "/docs"},
    log_performance=True,
    custom_settings={"max_cache_size": 10000}
)

middleware_manager.add_middleware(app, UnifiedCachingMiddleware, custom_config)
```

## Feature Mapping

### Request Context

The unified middleware provides a comprehensive request context:

```python
# OLD: Scattered context in request.state
request.state.request_id = "..."
request.state.correlation_id = "..."
request.state.start_time = time.time()

# NEW: Unified context object
context = request.state.unified_context
print(f"Request ID: {context.request_id}")
print(f"Correlation ID: {context.correlation_id}")
print(f"User ID: {context.user_id}")
print(f"Performance: {context.middleware_timings}")
```

### Performance Monitoring

```python
# OLD: Individual performance tracking
from ..shared.middleware.performance import performance_metrics

stats = performance_metrics.get_stats()

# NEW: Unified monitoring
from ..shared.middleware import middleware_manager

info = middleware_manager.get_middleware_info()
```

### Error Handling

```python
# OLD: Separate error handling
from ..shared.middleware.error_handling import setup_error_handling

setup_error_handling(app)

# NEW: Included in unified stack
# Error handling is automatically configured with unified middleware
```

## Environment Variables

The unified middleware respects these environment variables:

```bash
# Enable/disable specific middleware components
ENABLE_CACHING=true
ENABLE_RATE_LIMITING=true
ENABLE_TIMEOUT=true
ENABLE_PERFORMANCE_MONITORING=true

# Middleware-specific settings
CACHE_MAX_SIZE=5000
CACHE_DEFAULT_TTL=600
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000
DEFAULT_TIMEOUT=30
```

## Backward Compatibility

The unified middleware maintains backward compatibility:

1. **Legacy middleware classes** are still available for gradual migration
2. **Existing configurations** continue to work
3. **API contracts** remain unchanged
4. **Performance characteristics** are maintained or improved

## Testing the Migration

1. **Start the application** with unified middleware
2. **Check health endpoints** respond quickly (< 1ms)
3. **Verify logging** includes request context
4. **Test error handling** returns proper error responses
5. **Monitor performance** metrics are collected
6. **Validate caching** works for appropriate endpoints

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```python
   # Fix: Update imports to use unified middleware
   from ..shared.middleware import middleware_manager
   ```

2. **Missing Context**
   ```python
   # Fix: Access unified context
   context = request.state.unified_context
   ```

3. **Performance Regression**
   ```python
   # Fix: Check middleware ordering and configuration
   middleware_manager.get_middleware_info()
   ```

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger("giki_ai_api.shared.middleware").setLevel(logging.DEBUG)
```

## Next Steps

After successful migration:

1. **Remove legacy middleware files** (optional)
2. **Update documentation** to reference unified middleware
3. **Monitor performance** and adjust configurations as needed
4. **Consider custom middleware** using the unified base classes

## Support

For questions or issues with the migration:

1. Check the middleware logs for detailed error information
2. Use the middleware manager's diagnostic methods
3. Review the unified middleware source code for implementation details
4. Test with the standard middleware stack first, then add components incrementally
