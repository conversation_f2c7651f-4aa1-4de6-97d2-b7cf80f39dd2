"""
Database connection management using asyncpg.

This module provides direct PostgreSQL connection pooling and query execution
without SQLAlchemy ORM overhead.
"""

import asyncio
import logging
import os
import time
from contextlib import asynccontextmanager
from typing import Any, Callable, Dict, List, Optional, TypeVar
from urllib.parse import urlparse

import asyncpg
from asyncpg import Pool
from asyncpg.exceptions import (
    InterfaceError,
    InternalServerError,
    PostgresConnectionError,
    PostgresError,
    TooManyConnectionsError,
)

from .unified_config import settings

logger = logging.getLogger(__name__)

T = TypeVar("T")

# Database retry configuration
DB_RETRY_ATTEMPTS = 3
DB_RETRY_DELAY = 1.0  # seconds
DB_RETRY_BACKOFF = 2.0  # exponential backoff multiplier
DB_MAX_RETRY_DELAY = 10.0  # maximum delay between retries


def get_database_url() -> str:
    """Get the database URL from environment or settings."""
    db_url = os.environ.get("DATABASE_URL") or settings.DATABASE_URL
    if not db_url:
        raise ValueError("DATABASE_URL not configured")

    # Convert SQLAlchemy-style URLs to asyncpg format
    if "postgresql+asyncpg://" in db_url:
        db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")

    return db_url


# Global connection pool
_pool: Optional[Pool] = None
_pool_lock = asyncio.Lock()

# Export for test compatibility
connection_pool = _pool


def parse_database_url(url: str) -> Dict[str, Any]:
    """Parse database URL into connection parameters."""
    parsed = urlparse(url)

    # Handle special characters in password
    password = parsed.password
    if password:
        password = password.replace("%40", "@")

    return {
        "host": parsed.hostname,
        "port": parsed.port or 5432,
        "user": parsed.username,
        "password": password,
        "database": parsed.path.lstrip("/"),
        "ssl": "prefer" if "sslmode=disable" not in url else None,
    }


async def retry_db_operation(
    operation: Callable[..., T],
    *args,
    max_attempts: int = DB_RETRY_ATTEMPTS,
    initial_delay: float = DB_RETRY_DELAY,
    backoff: float = DB_RETRY_BACKOFF,
    max_delay: float = DB_MAX_RETRY_DELAY,
    **kwargs,
) -> T:
    """Retry a database operation with exponential backoff."""
    delay = initial_delay
    last_exception = None

    for attempt in range(max_attempts):
        try:
            return await operation(*args, **kwargs)
        except (
            PostgresConnectionError,
            InterfaceError,
            InternalServerError,
            TooManyConnectionsError,
        ) as e:
            last_exception = e
            if attempt < max_attempts - 1:
                logger.warning(
                    f"Database operation failed (attempt {attempt + 1}/{max_attempts}): {e}. "
                    f"Retrying in {delay:.1f}s..."
                )
                await asyncio.sleep(delay)
                delay = min(delay * backoff, max_delay)
            else:
                logger.error(
                    f"Database operation failed after {max_attempts} attempts: {e}"
                )
        except PostgresError as e:
            # For other Postgres errors, log and re-raise without retry
            logger.error(f"Non-retryable database error: {e}")
            raise
        except Exception as e:
            # For non-database errors, re-raise immediately
            logger.error(f"Unexpected error in database operation: {e}")
            raise

    if last_exception:
        raise last_exception
    raise RuntimeError("Database operation failed without exception")


async def create_pool(
    min_size: int = 10,  # PERFORMANCE-OPTIMIZED: Increased for better concurrency
    max_size: int = 30,  # PERFORMANCE-OPTIMIZED: Increased for peak load handling
    max_queries: int = 500000,  # PERFORMANCE-OPTIMIZED: Increased for better cache efficiency
    max_inactive_connection_lifetime: float = 300.0,  # PERFORMANCE-OPTIMIZED: Balanced recycling
    command_timeout: float = 10.0,  # PERFORMANCE-OPTIMIZED: Increased for complex queries
    server_settings: Optional[Dict[str, str]] = None,
) -> Pool:
    """Create and return a connection pool with retry logic."""
    db_url = os.environ.get("DATABASE_URL") or settings.DATABASE_URL

    if not db_url:
        raise ValueError("DATABASE_URL not configured")

    # Parse connection parameters
    conn_params = parse_database_url(db_url)

    logger.info(
        f"Creating connection pool: host={conn_params['host']}, "
        f"database={conn_params['database']}, pool_size={min_size}-{max_size}"
    )

    # PERFORMANCE-OPTIMIZED server settings for complex queries
    default_server_settings = {
        "application_name": "giki_ai_api",
        "statement_timeout": "10s",  # PERFORMANCE-OPTIMIZED: Increased for complex queries
        "lock_timeout": "2s",  # PERFORMANCE-OPTIMIZED: Balanced lock timeout
        "idle_in_transaction_session_timeout": "10s",  # PERFORMANCE-OPTIMIZED: Allow longer transactions
        "timezone": "UTC",
        "tcp_keepalives_idle": "300",
        "tcp_keepalives_interval": "30",
        "tcp_keepalives_count": "3",
        "work_mem": "16MB",  # PERFORMANCE-OPTIMIZED: Increased for complex queries
        # Removed "log_statement": "none" - requires superuser privileges
        # Removed "shared_preload_libraries": "" - requires server restart
    }
    if server_settings:
        default_server_settings.update(server_settings)

    async def _create_pool():
        pool = await asyncpg.create_pool(
            **conn_params,
            min_size=min_size,
            max_size=max_size,
            max_queries=max_queries,
            max_inactive_connection_lifetime=max_inactive_connection_lifetime,
            command_timeout=command_timeout,
            statement_cache_size=5000,  # PERFORMANCE-OPTIMIZED: Increased for better query caching
            server_settings=default_server_settings,
        )

        # Test the pool
        async with pool.acquire() as conn:
            await conn.fetchval("SELECT 1")

        logger.info("Database pool created successfully")
        return pool

    try:
        # Use retry logic for pool creation
        return await retry_db_operation(_create_pool)
    except Exception as e:
        logger.error(f"Failed to create database pool after retries: {e}")
        raise


async def get_pool() -> Pool:
    """Get or create the global connection pool."""
    global _pool

    if _pool is None:
        async with _pool_lock:
            if _pool is None:
                _pool = await create_pool()

    return _pool


@asynccontextmanager
async def get_database_connection():
    """Get a database connection from the pool with health check and comprehensive error handling."""
    try:
        pool = await get_pool()
    except Exception as e:
        logger.error(f"Failed to get connection pool: {e}")
        # Return a more user-friendly error instead of crashing
        from fastapi import HTTPException

        raise HTTPException(
            status_code=503,
            detail="Database service temporarily unavailable. Please try again later.",
        )

    start_time = time.time()
    connection = None
    try:
        # Use timeout to prevent indefinite waiting
        connection = await asyncio.wait_for(
            pool.acquire(),
            timeout=5.0,  # 5 second timeout for acquiring connection
        )

        # Health check the connection
        try:
            await connection.fetchval("SELECT 1", timeout=1.0)
        except Exception as e:
            logger.warning(f"Connection health check failed: {e}")
            # Connection is bad, close it and let pool create a new one
            await connection.close()
            connection = None
            raise

        acquisition_time = (time.time() - start_time) * 1000
        if acquisition_time > 100:  # Log slow acquisitions
            logger.warning(f"Slow connection acquisition: {acquisition_time:.1f}ms")

        yield connection

    except asyncio.TimeoutError:
        elapsed = time.time() - start_time
        logger.error(
            f"Connection pool timeout after {elapsed:.1f}s - pool may be exhausted"
        )
        from fastapi import HTTPException

        raise HTTPException(
            status_code=503,
            detail="Database connection timeout. The service is experiencing high load.",
        )
    except asyncpg.exceptions.TooManyConnectionsError as e:
        logger.error(f"Too many database connections: {e}")
        from fastapi import HTTPException

        raise HTTPException(
            status_code=503,
            detail="Database connection limit reached. Please try again later.",
        )
    except Exception as e:
        logger.error(f"Failed to acquire database connection: {e}", exc_info=True)
        from fastapi import HTTPException

        # Don't expose internal error details to users
        raise HTTPException(
            status_code=503,
            detail="Database service error. Please contact support if this persists.",
        )
    finally:
        # Ensure connection is released even if an error occurred
        if connection and not connection.is_closed():
            try:
                await pool.release(connection)
            except Exception as e:
                logger.error(f"Error releasing connection: {e}")


async def close_pool():
    """Close the global connection pool."""
    global _pool

    if _pool is not None:
        await _pool.close()
        _pool = None
        logger.info("Database pool closed")


@asynccontextmanager
async def get_db():
    """
    Get a database connection from the pool with comprehensive error handling.

    Usage:
        async with get_db() as conn:
            result = await conn.fetch("SELECT * FROM users")
    """
    try:
        pool = await get_pool()
    except Exception as e:
        logger.error(f"Failed to get connection pool in get_db: {e}")
        from fastapi import HTTPException

        raise HTTPException(
            status_code=503,
            detail="Database service temporarily unavailable. Please try again later.",
        )

    connection = None
    try:
        # Use timeout to prevent indefinite waiting
        connection = await asyncio.wait_for(
            pool.acquire(),
            timeout=5.0,  # 5 second timeout
        )

        # Quick health check
        await connection.fetchval("SELECT 1", timeout=1.0)

        yield connection

    except asyncio.TimeoutError:
        logger.error("Database connection timeout in get_db")
        from fastapi import HTTPException

        raise HTTPException(
            status_code=503,
            detail="Database connection timeout. The service is experiencing high load.",
        )
    except asyncpg.exceptions.TooManyConnectionsError as e:
        logger.error(f"Too many database connections in get_db: {e}")
        from fastapi import HTTPException

        raise HTTPException(
            status_code=503,
            detail="Database connection limit reached. Please try again later.",
        )
    except Exception as e:
        # Don't wrap HTTPExceptions - let them propagate as-is
        from fastapi import HTTPException

        if isinstance(e, HTTPException):
            raise
        logger.error(f"Database error in get_db: {e}", exc_info=True)
        raise HTTPException(
            status_code=503,
            detail="Database service error. Please contact support if this persists.",
        )
    finally:
        # Ensure connection is properly released
        if connection and not connection.is_closed():
            try:
                await pool.release(connection)
            except Exception as e:
                logger.error(f"Error releasing connection in get_db: {e}")


@asynccontextmanager
async def transaction():
    """
    Create a database transaction context with automatic retry on deadlock and comprehensive error handling.

    Usage:
        async with transaction() as conn:
            await conn.execute("INSERT INTO ...")
            await conn.execute("UPDATE ...")
    """
    try:
        pool = await get_pool()
    except Exception as e:
        logger.error(f"Failed to get connection pool for transaction: {e}")
        from fastapi import HTTPException

        raise HTTPException(
            status_code=503,
            detail="Database service temporarily unavailable. Please try again later.",
        )

    max_deadlock_retries = 3
    last_error = None

    for attempt in range(max_deadlock_retries):
        connection = None
        try:
            # Acquire connection with timeout
            connection = await asyncio.wait_for(pool.acquire(), timeout=5.0)

            async with connection.transaction():
                yield connection
                return  # Success - exit the function

        except asyncpg.exceptions.DeadlockDetectedError as e:
            last_error = e
            if attempt < max_deadlock_retries - 1:
                logger.warning(
                    f"Deadlock detected (attempt {attempt + 1}/{max_deadlock_retries}), retrying..."
                )
                await asyncio.sleep(0.1 * (2**attempt))  # Exponential backoff
            else:
                logger.error(
                    f"Transaction failed after {max_deadlock_retries} deadlock retries"
                )
                from fastapi import HTTPException

                raise HTTPException(
                    status_code=503,
                    detail="Database deadlock detected. Please retry your request.",
                )
        except asyncio.TimeoutError:
            logger.error("Database connection timeout in transaction")
            from fastapi import HTTPException

            raise HTTPException(
                status_code=503,
                detail="Database connection timeout. The service is experiencing high load.",
            )
        except asyncpg.exceptions.SerializationError as e:
            logger.error(f"Transaction serialization error: {e}")
            from fastapi import HTTPException

            raise HTTPException(
                status_code=409,
                detail="Transaction conflict detected. Please retry your request.",
            )
        except asyncpg.exceptions.ForeignKeyViolationError as e:
            logger.error(f"Foreign key violation in transaction: {e}")
            from fastapi import HTTPException

            raise HTTPException(
                status_code=400, detail="Invalid reference to related data."
            )
        except asyncpg.exceptions.UniqueViolationError as e:
            logger.error(f"Unique constraint violation in transaction: {e}")
            from fastapi import HTTPException

            raise HTTPException(
                status_code=409,
                detail="Duplicate data detected. Please check your input.",
            )
        except Exception as e:
            # Don't wrap HTTPExceptions - let them propagate as-is
            from fastapi import HTTPException

            if isinstance(e, HTTPException):
                raise
            logger.error(f"Transaction error: {e}", exc_info=True)
            raise HTTPException(
                status_code=503,
                detail="Database transaction error. Please contact support if this persists.",
            )
        finally:
            # Ensure connection is properly released
            if connection and not connection.is_closed():
                try:
                    await pool.release(connection)
                except Exception as e:
                    logger.error(f"Error releasing connection in transaction: {e}")

    # If we get here, all retries failed
    if last_error:
        raise last_error


class Database:
    """Database helper class for common operations."""

    @staticmethod
    async def fetch_one(
        query: str, *args, timeout: float = 10.0
    ) -> Optional[asyncpg.Record]:
        """Fetch a single row with retry logic."""

        async def _fetch():
            async with get_db() as conn:
                return await conn.fetchrow(query, *args, timeout=timeout)

        return await retry_db_operation(_fetch)

    @staticmethod
    async def fetch_all(
        query: str, *args, timeout: float = 15.0
    ) -> List[asyncpg.Record]:
        """Fetch all rows with retry logic."""

        async def _fetch():
            async with get_db() as conn:
                return await conn.fetch(query, *args, timeout=timeout)

        return await retry_db_operation(_fetch)

    @staticmethod
    async def fetch_val(
        query: str, *args, column: int = 0, timeout: float = 10.0
    ) -> Any:
        """Fetch a single value."""
        async with get_db() as conn:
            return await conn.fetchval(query, *args, column=column, timeout=timeout)

    @staticmethod
    async def execute(query: str, *args, timeout: float = 10.0) -> str:
        """Execute a query and return status with retry logic."""

        async def _execute():
            async with get_db() as conn:
                return await conn.execute(query, *args, timeout=timeout)

        return await retry_db_operation(_execute)

    @staticmethod
    async def execute_many(
        query: str, args_list: List[tuple], timeout: float = 30.0
    ) -> None:
        """Execute a query multiple times with different arguments."""
        async with get_db() as conn:
            await conn.executemany(query, args_list, timeout=timeout)

    @staticmethod
    async def insert_returning(
        table: str, data: Dict[str, Any], returning: str = "*"
    ) -> asyncpg.Record:
        """Insert a row and return specified columns."""
        columns = ", ".join(data.keys())
        placeholders = ", ".join(f"${i + 1}" for i in range(len(data)))
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders}) RETURNING {returning}"

        async with get_db() as conn:
            return await conn.fetchrow(query, *data.values())

    @staticmethod
    async def update_returning(
        table: str, data: Dict[str, Any], where: Dict[str, Any], returning: str = "*"
    ) -> List[asyncpg.Record]:
        """Update rows and return specified columns."""
        set_clause = ", ".join(f"{k} = ${i + 1}" for i, k in enumerate(data.keys()))
        where_clause = " AND ".join(
            f"{k} = ${i + 1 + len(data)}" for i, k in enumerate(where.keys())
        )
        query = f"UPDATE {table} SET {set_clause} WHERE {where_clause} RETURNING {returning}"

        async with get_db() as conn:
            return await conn.fetch(query, *data.values(), *where.values())


# Maintain backward compatibility for FastAPI dependency injection
async def get_db_session():
    """FastAPI dependency for database connections."""
    async with get_db() as conn:
        yield conn


# Alias for backward compatibility
db = Database()


class PoolMonitor:
    """Monitor database pool health and performance."""

    def __init__(self):
        self.last_check = time.time()
        self.check_interval = 60.0  # seconds
        self.unhealthy_threshold = 5  # consecutive failures before marking unhealthy
        self.consecutive_failures = 0

    async def check_pool_health(self) -> Dict[str, Any]:
        """Check pool health and return metrics."""
        global _pool

        if _pool is None:
            return {"status": "not_initialized", "healthy": False}

        try:
            # Get pool statistics
            stats = {
                "status": "healthy",
                "healthy": True,
                "size": _pool.get_size(),
                "min_size": _pool.get_min_size(),
                "max_size": _pool.get_max_size(),
                "free_connections": _pool.get_idle_size(),
                "used_connections": _pool.get_size() - _pool.get_idle_size(),
                "total_queries": getattr(_pool, "_query_count", 0),
            }

            # Test connection
            start_time = time.time()
            async with _pool.acquire() as conn:
                await conn.fetchval("SELECT 1", timeout=2.0)

            stats["health_check_latency_ms"] = (time.time() - start_time) * 1000

            # Reset failure counter on success
            self.consecutive_failures = 0

            return stats

        except Exception as e:
            self.consecutive_failures += 1
            logger.error(f"Pool health check failed: {e}")

            return {
                "status": "unhealthy",
                "healthy": False,
                "error": str(e),
                "consecutive_failures": self.consecutive_failures,
                "should_recreate": self.consecutive_failures
                >= self.unhealthy_threshold,
            }

    async def ensure_healthy_pool(self) -> None:
        """Ensure pool is healthy, recreate if necessary."""
        current_time = time.time()

        # Check if enough time has passed since last check
        if current_time - self.last_check < self.check_interval:
            return

        self.last_check = current_time
        health = await self.check_pool_health()

        if health.get("should_recreate", False):
            logger.warning("Pool is unhealthy, attempting to recreate...")
            await self.recreate_pool()

    async def recreate_pool(self) -> None:
        """Recreate the connection pool."""
        global _pool

        try:
            # Close existing pool
            if _pool is not None:
                await close_pool()

            # Create new pool
            async with _pool_lock:
                _pool = await create_pool()

            self.consecutive_failures = 0
            logger.info("Pool recreated successfully")

        except Exception as e:
            logger.error(f"Failed to recreate pool: {e}")
            raise


# Global pool monitor instance
pool_monitor = PoolMonitor()


async def get_pool_with_health_check() -> Pool:
    """Get or create the global connection pool with health monitoring."""
    # Ensure pool is healthy
    await pool_monitor.ensure_healthy_pool()

    # Get pool normally
    return await get_pool()
