import logging
from typing import TYPE_CHECKING, Optional

from asyncpg import Connection
from fastapi import Depends, HTTPException, Request, status

from ..domains.auth.models import Tenant as TenantModel, User
from ..domains.auth.secure_auth import get_current_active_user
from .unified_config import settings
from .database import get_db_session

if TYPE_CHECKING:
    from ..shared.ai.vertex_client import VertexAIClient

logger = logging.getLogger(__name__)

# Global instance of the service, to be initialized at startup
categorization_service_instance = None  # Type hint removed to satisfy Ruff for now


async def get_tenant_by_id(tenant_id: int, conn: Connection) -> TenantModel | None:
    """
    Fetch tenant details from database by ID.

    Args:
        tenant_id: The tenant ID to look up
        conn: Async database connection

    Returns:
        TenantModel instance if found, None otherwise
    """
    try:
        # Query the tenant by ID
        query = """
            SELECT id, name, domain, settings, subscription_status, 
                   subscription_plan, created_at, updated_at
            FROM tenants
            WHERE id = $1
        """
        row = await conn.fetchrow(query, tenant_id)

        if row:
            # Convert row to dict and handle JSONB fields
            tenant_data = dict(row)

            # Ensure settings is a dict (JSONB fields might be returned as strings)
            if isinstance(tenant_data.get("settings"), str):
                import json

                try:
                    tenant_data["settings"] = json.loads(tenant_data["settings"])
                except (json.JSONDecodeError, TypeError):
                    tenant_data["settings"] = {}

            tenant = TenantModel(**tenant_data)
            logger.info(f"Found tenant {tenant.id}: {tenant.name}")
            return tenant
        else:
            logger.warning(f"Tenant with ID {tenant_id} not found in database")
            return None

    except Exception as e:
        logger.error(f"Error fetching tenant {tenant_id}: {e}")
        return None


async def get_current_user_with_tenant(
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_session),
) -> tuple[User, TenantModel]:
    # User model has tenant_id as required field
    tenant_id = current_user.tenant_id

    tenant = await get_tenant_by_id(tenant_id=tenant_id, conn=conn)
    if not tenant:
        logger.error(
            f"Tenant not found for tenant_id: {tenant_id} for user {current_user.email}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Tenant '{tenant_id}' not found or access denied.",
        )
    return current_user, tenant


async def get_current_tenant_id(
    current_user: User = Depends(get_current_active_user),
) -> int:
    """
    Get tenant ID from current authenticated user.

    Since get_current_active_user always queries the database,
    we know the tenant_id is current and valid.
    """
    return current_user.tenant_id


async def get_categorization_service():  # Return type hint removed
    """
    Dependency function to get the initialized CategorizationService.
    """
    try:
        from ..shared.ai.unified_ai import AIServiceConfig, UnifiedAIService
    except ImportError as e:
        logger.error(f"Failed to import UnifiedAIService: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="AI service is not available. Please try again later.",
        )

    # Create AI service config
    config = AIServiceConfig(
        vertex_project_id=settings.VERTEX_PROJECT_ID,
        vertex_location=settings.VERTEX_LOCATION,
        model_name="gemini-1.5-pro",
        temperature=0.1,
        max_tokens=8192,
    )

    # Create and return the service
    ai_service = UnifiedAIService(config)
    return ai_service


async def get_vertex_ai_client():
    """
    Dependency function to get the Vertex AI client.
    """
    try:
        from ..shared.ai.vertex_client import VertexAIClient
        
        client = VertexAIClient(
            project_id=settings.VERTEX_PROJECT_ID,
            location=settings.VERTEX_LOCATION,
        )
        return client
    except ImportError as e:
        logger.error(f"Failed to import VertexAIClient: {e}")
        return None
    except Exception as e:
        logger.error(f"Failed to create VertexAIClient: {e}")
        return None


async def get_accuracy_service(conn: Connection = Depends(get_db_session)):
    """Dependency to get AccuracyMeasurementService instance."""
    try:
        from ..domains.accuracy.service import AccuracyMeasurementService
    except ImportError as e:
        logger.error(f"Failed to import AccuracyMeasurementService: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Accuracy measurement service is not available. Please try again later.",
        )

    return AccuracyMeasurementService(conn)
