# Pre-commit configuration for giki.ai API
# Enforces unified architecture patterns and code quality

repos:
  # Ruff for fast Python linting and formatting
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.9
    hooks:
      # Run the linter
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
        types: [python]
      # Run the formatter
      - id: ruff-format
        types: [python]

  # Custom consolidation linting
  - repo: local
    hooks:
      - id: consolidation-lint
        name: Consolidation Architecture Linting
        entry: python scripts/lint_consolidation.py
        language: system
        files: ^src/.*\.py$
        types: [python]
        pass_filenames: false
        args: [src/]
        description: "Enforce unified architecture patterns and detect deprecated usage"

  # Security scanning
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, src/, -f, json, -o, bandit-report.json]
        files: ^src/.*\.py$
        exclude: ^tests/

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile, black, --line-length=88]

  # Standard pre-commit hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        exclude: \.md$
      - id: end-of-file-fixer
        exclude: \.md$
      - id: check-yaml
        args: [--unsafe]
      - id: check-added-large-files
        args: [--maxkb=1000]
      - id: check-merge-conflict
      - id: debug-statements
      - id: detect-private-key

  # Type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
        files: ^src/.*\.py$
        args: [--ignore-missing-imports, --no-strict-optional]

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        files: Dockerfile.*

# Configuration
default_stages: [commit]
fail_fast: false

# Hook-specific configurations
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false