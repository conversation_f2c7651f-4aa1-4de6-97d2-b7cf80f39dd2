# GitHub Actions workflow for consolidation linting
# Enforces unified architecture patterns in pull requests

name: Consolidation Architecture Lint

on:
  pull_request:
    branches: [master, main]
    paths:
      - 'apps/giki-ai-api/src/**/*.py'
      - 'apps/giki-ai-api/pyproject.toml'
      - 'apps/giki-ai-api/.pre-commit-config.yaml'
  push:
    branches: [master, main]
    paths:
      - 'apps/giki-ai-api/src/**/*.py'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    working-directory: apps/giki-ai-api

jobs:
  consolidation-lint:
    name: Consolidation Architecture Linting
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'pip'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install ruff bandit pre-commit
          pip install -e .[dev]

      - name: Run Ruff linting
        run: |
          echo "🔍 Running Ruff linting..."
          ruff check src/ --output-format=github
        continue-on-error: true

      - name: Run Ruff formatting check
        run: |
          echo "🎨 Checking code formatting..."
          ruff format --check src/
        continue-on-error: true

      - name: Run consolidation architecture linting
        run: |
          echo "🏗️ Running consolidation architecture linting..."
          python scripts/lint_consolidation.py src/
          echo "Consolidation linting completed"

      - name: Run security checks
        run: |
          echo "🔒 Running security checks..."
          bandit -r src/ -f json -o bandit-report.json || true
          if [ -f bandit-report.json ]; then
            echo "Security scan results:"
            cat bandit-report.json | python -m json.tool
          fi
        continue-on-error: true

      - name: Check for deprecated patterns
        run: |
          echo "🚫 Checking for deprecated patterns..."
          
          # Check for direct APIRouter usage (should use UnifiedBaseRouter)
          if grep -r "router = APIRouter(" src/ --include="*.py"; then
            echo "❌ Found direct APIRouter usage - use UnifiedBaseRouter instead"
            exit 1
          fi
          
          # Check for non-unified service patterns
          if grep -r "class.*Service(" src/ --include="*.py" | grep -v "BaseService" | grep -v "UnifiedBaseService"; then
            echo "⚠️ Found non-unified service patterns - consider using BaseService"
          fi
          
          # Check for scattered config usage
          if grep -r "os.getenv\|os.environ" src/ --include="*.py" | grep -v "# LEGACY"; then
            echo "⚠️ Found direct environment variable access - use unified config"
          fi
          
          echo "✅ Deprecated pattern check completed"

      - name: Check for required unified patterns
        run: |
          echo "✅ Checking for required unified patterns..."
          
          # Check that new routers use UnifiedBaseRouter
          for router_file in $(find src/ -name "*_router.py" -not -path "*/legacy/*"); do
            if [ -f "$router_file" ]; then
              if ! grep -q "UnifiedBaseRouter" "$router_file"; then
                echo "❌ Router $router_file should inherit from UnifiedBaseRouter"
                exit 1
              fi
            fi
          done
          
          # Check that RouterFactory is used in main.py
          if ! grep -q "RouterFactory" src/giki_ai_api/core/main.py; then
            echo "❌ main.py should use RouterFactory for router registration"
            exit 1
          fi
          
          echo "✅ Required pattern check completed"

      - name: Generate consolidation report
        if: always()
        run: |
          echo "📊 Generating consolidation compliance report..."
          
          # Count unified vs legacy patterns
          unified_routers=$(find src/ -name "*unified*router*.py" | wc -l)
          legacy_routers=$(find src/ -name "router.py" -not -path "*/unified*" | wc -l)
          
          echo "### Consolidation Status Report" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Component | Unified | Legacy | Progress |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|---------|--------|----------|" >> $GITHUB_STEP_SUMMARY
          echo "| Routers | $unified_routers | $legacy_routers | $(( unified_routers * 100 / (unified_routers + legacy_routers) ))% |" >> $GITHUB_STEP_SUMMARY
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "#### ✅ Consolidation Goals Met:" >> $GITHUB_STEP_SUMMARY
          echo "- Unified router system implemented" >> $GITHUB_STEP_SUMMARY
          echo "- RouterFactory pattern enforced" >> $GITHUB_STEP_SUMMARY
          echo "- Linting rules prevent regressions" >> $GITHUB_STEP_SUMMARY
          
          if [ $legacy_routers -gt 0 ]; then
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "#### 🔄 Next Steps:" >> $GITHUB_STEP_SUMMARY
            echo "- $legacy_routers legacy routers remaining" >> $GITHUB_STEP_SUMMARY
            echo "- Migration to unified patterns in progress" >> $GITHUB_STEP_SUMMARY
          fi

      - name: Upload reports
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: consolidation-reports
          path: |
            apps/giki-ai-api/bandit-report.json
          retention-days: 7

  architecture-compliance:
    name: Architecture Compliance Check
    runs-on: ubuntu-latest
    timeout-minutes: 5
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check unified architecture compliance
        run: |
          echo "🏗️ Checking unified architecture compliance..."
          
          # Verify RouterFactory usage
          if ! grep -q "RouterFactory" apps/giki-ai-api/src/giki_ai_api/core/main.py; then
            echo "❌ main.py must use RouterFactory for router registration"
            exit 1
          fi
          
          # Verify unified router structure
          unified_router_count=$(find apps/giki-ai-api/src -name "unified_*router.py" | wc -l)
          if [ $unified_router_count -lt 8 ]; then
            echo "❌ Expected at least 8 unified routers, found $unified_router_count"
            exit 1
          fi
          
          # Verify base router exists
          if [ ! -f "apps/giki-ai-api/src/giki_ai_api/shared/routers/base_router.py" ]; then
            echo "❌ UnifiedBaseRouter system not found"
            exit 1
          fi
          
          echo "✅ Architecture compliance verified"
          echo "  - RouterFactory system: ✅"
          echo "  - Unified routers: $unified_router_count ✅"
          echo "  - Base router system: ✅"