import { test, expect } from '@playwright/test';

/**
 * Agent-Based Export Validation
 * 
 * Tests export functionality through the agent system (/api/v1/agent/command)
 * This validates the actual export architecture discovered in the API
 */

test.describe('Agent Export System Validation', () => {
  test('Agent export command API validation', async ({ page }) => {
    console.log('🤖 Testing agent export command system...');

    // Test the agent export command endpoint directly
    const exportRequest = {
      command: '/export',
      parameters: {
        format: 'excel',
        include_categories: true,
        date_range: 'last_30_days'
      },
      context: {
        user_intent: 'export_transactions_for_accounting'
      }
    };

    // First, verify agent endpoint exists and responds
    try {
      const agentResponse = await page.request.post('http://localhost:8000/api/v1/agent/command', {
        data: exportRequest,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log(`Agent endpoint response status: ${agentResponse.status()}`);
      
      if (agentResponse.status() === 401) {
        console.log('✅ Agent endpoint exists but requires authentication (expected)');
      } else if (agentResponse.status() === 422) {
        console.log('✅ Agent endpoint exists but validation failed (expected without auth)');
      } else if (agentResponse.status() === 200) {
        const responseBody = await agentResponse.json();
        console.log('✅ Agent endpoint responding:', responseBody);
      } else {
        console.log(`⚠️ Unexpected status: ${agentResponse.status()}`);
      }

      // Verify the endpoint exists (not 404)
      expect(agentResponse.status()).not.toBe(404);
      
    } catch (error) {
      console.log('❌ Agent endpoint test failed:', error);
      throw error;
    }

    console.log('🎉 Agent export system validation completed');
  });

  test('Export format commands validation', async ({ page }) => {
    console.log('📋 Testing export format command variations...');

    const exportFormats = [
      'excel', 'csv', 'quickbooks', 'xero', 'tally', 
      'sage', 'wave', 'freshbooks', 'zoho', 'pdf'
    ];

    for (const format of exportFormats) {
      const formatRequest = {
        command: '/export',
        parameters: {
          format: format,
          include_metadata: true
        }
      };

      try {
        const response = await page.request.post('http://localhost:8000/api/v1/agent/command', {
          data: formatRequest,
          headers: {
            'Content-Type': 'application/json'
          }
        });

        // Should get proper response (not 404 - endpoint not found)
        expect([200, 401, 422].includes(response.status())).toBeTruthy();
        console.log(`✅ Export format ${format} - endpoint responds (${response.status()})`);
        
      } catch (error) {
        console.log(`⚠️ Export format ${format} test issue:`, error);
      }
    }

    console.log('🎉 Export format validation completed');
  });

  test('Agent command structure validation', async ({ page }) => {
    console.log('🔍 Testing agent command structure...');

    // Test various command structures to understand the agent system
    const testCommands = [
      { command: '/help', description: 'Help command' },
      { command: '/export', description: 'Export command' },
      { command: '/upload', description: 'Upload command' },
      { command: '/categorize', description: 'Categorize command' },
      { command: '/analyze', description: 'Analyze command' }
    ];

    for (const cmd of testCommands) {
      try {
        const response = await page.request.post('http://localhost:8000/api/v1/agent/command', {
          data: { command: cmd.command },
          headers: {
            'Content-Type': 'application/json'
          }
        });

        console.log(`${cmd.description}: ${response.status()}`);
        
        // Endpoint should exist for all commands
        expect(response.status()).not.toBe(404);
        
      } catch (error) {
        console.log(`⚠️ Command ${cmd.command} test failed:`, error);
      }
    }

    console.log('🎉 Agent command structure validation completed');
  });

  test('Frontend agent integration check', async ({ page }) => {
    console.log('🌐 Testing frontend agent integration...');

    // Navigate to the application
    await page.goto('http://localhost:4200');
    
    // Check if the application loads
    await expect(page.locator('body')).toBeVisible({ timeout: 10000 });
    console.log('✅ Application loads successfully');

    // Check if there are any agent-related elements or scripts
    const hasAgentElements = await page.evaluate(() => {
      // Look for agent-related elements, classes, or variables
      const agentIndicators = [
        document.querySelector('[class*="agent"]'),
        document.querySelector('[data-testid*="agent"]'),
        document.querySelector('[id*="agent"]'),
        // Check for agent in window object
        typeof (window as any).agent !== 'undefined',
        typeof (window as any).AgentClient !== 'undefined'
      ];
      
      return agentIndicators.some(indicator => !!indicator);
    });

    if (hasAgentElements) {
      console.log('✅ Agent integration elements found in frontend');
    } else {
      console.log('ℹ️ No obvious agent elements found (may be internal)');
    }

    // Try to navigate to different sections to see what's available
    const sections = ['/', '/dashboard', '/reports', '/upload'];
    
    for (const section of sections) {
      try {
        await page.goto(`http://localhost:4200${section}`);
        await page.waitForTimeout(1000);
        
        const title = await page.title();
        console.log(`✅ Section ${section} loads: "${title}"`);
        
      } catch (error) {
        console.log(`⚠️ Section ${section} issue:`, error);
      }
    }

    console.log('🎉 Frontend integration check completed');
  });

  test('Export workflow readiness assessment', async ({ page }) => {
    console.log('📊 Assessing export workflow readiness...');

    // Check API health
    const healthResponse = await page.request.get('http://localhost:8000/health');
    expect(healthResponse.status()).toBe(200);
    console.log('✅ API server healthy');

    // Check agent system availability
    const agentResponse = await page.request.post('http://localhost:8000/api/v1/agent/command', {
      data: { command: '/help' }
    });
    expect([200, 401, 422].includes(agentResponse.status())).toBeTruthy();
    console.log('✅ Agent system available');

    // Check if frontend can connect to backend
    await page.goto('http://localhost:4200');
    
    // Look for any error indicators
    const hasErrors = await page.evaluate(() => {
      return document.querySelector('.error, [class*="error"], [data-error]') !== null;
    });

    if (!hasErrors) {
      console.log('✅ No obvious frontend errors detected');
    } else {
      console.log('⚠️ Some frontend errors may be present');
    }

    // Assessment summary
    console.log('📋 Export Workflow Readiness Assessment:');
    console.log('  ✅ API server operational');
    console.log('  ✅ Agent system responding');
    console.log('  ✅ Frontend application loading');
    console.log('  ℹ️ Export functionality appears to be agent-based');
    console.log('  ℹ️ Full workflow testing requires authentication setup');

    console.log('🎉 Export workflow readiness assessment completed');
  });
});