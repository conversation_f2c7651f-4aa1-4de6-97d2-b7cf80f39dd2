import { test, expect } from '@playwright/test';
import { AuthHelper } from '../../scripts/auth-helper';

/**
 * Complete Export Journey Test
 * 
 * Tests the complete customer workflow: upload → categorize → export
 * This validates the core MIS value proposition and business workflow
 */

const TEST_FILES = {
  // Test with realistic financial data
  hdfc: '/Users/<USER>/giki-ai-workspace/libs/test-data/synthetic/indian-banks/HDFC.xlsx',
  icici: '/Users/<USER>/giki-ai-workspace/libs/test-data/synthetic/indian-banks/ICICI.csv',
  sbi: '/Users/<USER>/giki-ai-workspace/libs/test-data/synthetic/indian-banks/SBI.xlsx'
};

test.describe('Complete Export Journey', () => {
  let authHelper: AuthHelper;

  test.beforeEach(async ({ page }) => {
    authHelper = new AuthHelper(page);
    
    // Start fresh for each test
    await page.goto('http://localhost:4200');
    
    // Authenticate as business owner
    await authHelper.loginAsOwner();
    
    // Verify successful authentication
    await expect(page.locator('[data-testid="dashboard"]')).toBeVisible({ timeout: 10000 });
  });

  test('Complete export journey - HDFC bank data', async ({ page }) => {
    console.log('🎯 Starting complete export journey test with HDFC data...');

    // Step 1: Navigate to file upload
    console.log('📁 Step 1: File Upload...');
    await page.click('[data-testid="upload-link"]');
    await expect(page.locator('h1')).toContainText(['Upload', 'File']);

    // Step 2: Upload file and wait for processing
    console.log('⬆️ Step 2: Uploading HDFC file...');
    await page.setInputFiles('[data-testid="file-input"]', TEST_FILES.hdfc);
    await page.click('[data-testid="upload-button"]');

    // Wait for processing to complete
    await expect(page.locator('[data-testid="processing-complete"]')).toBeVisible({ timeout: 60000 });
    
    // Verify transactions were processed
    const transactionCount = await page.locator('[data-testid="transaction-count"]').textContent();
    console.log(`✅ Processed ${transactionCount} transactions`);
    expect(parseInt(transactionCount || '0')).toBeGreaterThan(0);

    // Step 3: Navigate to transaction review
    console.log('📊 Step 3: Transaction Review...');
    await page.click('[data-testid="review-transactions-button"]');
    await expect(page.locator('[data-testid="transaction-table"]')).toBeVisible();

    // Verify AI categorization has occurred (baseline 87% accuracy)
    const categorizedTransactions = await page.locator('[data-testid="categorized-transaction"]').count();
    const totalTransactions = await page.locator('[data-testid="transaction-row"]').count();
    const accuracyRate = (categorizedTransactions / totalTransactions) * 100;
    
    console.log(`🤖 AI Categorization: ${categorizedTransactions}/${totalTransactions} (${accuracyRate.toFixed(1)}%)`);
    expect(accuracyRate).toBeGreaterThanOrEqual(80); // Allow some variance from 87% baseline

    // Step 4: Verify and approve categorizations
    console.log('✅ Step 4: Verification and Approval...');
    
    // Check a few categorizations for business appropriateness
    const firstCategory = await page.locator('[data-testid="transaction-row"]:first-child [data-testid="category-name"]').textContent();
    expect(firstCategory).toBeTruthy();
    console.log(`First transaction categorized as: ${firstCategory}`);

    // Bulk approve all categorizations (simulating satisfied customer)
    await page.click('[data-testid="select-all-transactions"]');
    await page.click('[data-testid="bulk-approve-button"]');
    await expect(page.locator('[data-testid="bulk-approve-success"]')).toBeVisible();

    // Step 5: Navigate to exports
    console.log('📋 Step 5: Export Generation...');
    await page.click('[data-testid="export-link"]');
    await expect(page.locator('h1')).toContainText(['Export', 'Reports']);

    // Step 6: Test multiple export formats
    const exportFormats = ['quickbooks', 'xero', 'tally', 'excel', 'csv'];
    
    for (const format of exportFormats) {
      console.log(`📁 Testing ${format.toUpperCase()} export...`);
      
      // Select export format
      await page.click(`[data-testid="export-format-${format}"]`);
      
      // Configure export options if needed
      if (format === 'quickbooks' || format === 'xero') {
        await page.check('[data-testid="include-tax-categories"]');
      }
      
      // Generate export
      await page.click('[data-testid="generate-export-button"]');
      
      // Wait for export completion
      await expect(page.locator('[data-testid="export-ready"]')).toBeVisible({ timeout: 30000 });
      
      // Verify download link is available
      const downloadLink = page.locator(`[data-testid="download-${format}"]`);
      await expect(downloadLink).toBeVisible();
      
      // Verify file format compliance
      const fileInfo = await downloadLink.getAttribute('data-file-info');
      expect(fileInfo).toContain(format);
      
      console.log(`✅ ${format.toUpperCase()} export generated successfully`);
      
      // Clear selection for next format
      await page.click('[data-testid="clear-selection"]');
    }

    // Step 7: Verify export completeness
    console.log('🔍 Step 7: Export Validation...');
    
    // Check that all transaction data is included in exports
    await page.click('[data-testid="export-format-excel"]'); // Use Excel for validation
    await page.click('[data-testid="generate-export-button"]');
    await expect(page.locator('[data-testid="export-ready"]')).toBeVisible({ timeout: 30000 });
    
    // Verify export contains all transactions
    const exportedTransactions = await page.locator('[data-testid="exported-transaction-count"]').textContent();
    expect(parseInt(exportedTransactions || '0')).toEqual(totalTransactions);
    
    console.log(`📊 Export validation: ${exportedTransactions}/${totalTransactions} transactions exported`);

    // Step 8: Business value measurement
    console.log('💰 Step 8: Business Value Assessment...');
    
    // Navigate to insights/analytics
    await page.click('[data-testid="insights-link"]');
    
    // Measure time savings (should show significant improvement vs manual)
    const timeSavings = await page.locator('[data-testid="time-savings-metric"]').textContent();
    console.log(`⏱️ Time savings: ${timeSavings}`);
    
    // Verify accuracy improvements
    const accuracyImprovement = await page.locator('[data-testid="accuracy-improvement"]').textContent();
    console.log(`🎯 Accuracy improvement: ${accuracyImprovement}`);
    
    // Verify workflow efficiency
    const workflowEfficiency = await page.locator('[data-testid="workflow-efficiency"]').textContent();
    console.log(`⚡ Workflow efficiency: ${workflowEfficiency}`);

    console.log('🎉 Complete export journey test completed successfully!');
  });

  test('Multi-file processing and consolidated export', async ({ page }) => {
    console.log('🎯 Starting multi-file processing test...');

    // Step 1: Upload multiple bank files
    console.log('📁 Step 1: Multi-file Upload...');
    await page.click('[data-testid="upload-link"]');
    
    // Enable multi-file mode
    await page.click('[data-testid="multi-file-mode"]');
    
    // Upload multiple files
    await page.setInputFiles('[data-testid="file-input"]', [
      TEST_FILES.hdfc,
      TEST_FILES.icici,
      TEST_FILES.sbi
    ]);
    
    await page.click('[data-testid="upload-all-button"]');

    // Wait for all files to process
    await expect(page.locator('[data-testid="all-files-processed"]')).toBeVisible({ timeout: 120000 });
    
    // Verify consolidated transaction count
    const totalTransactions = await page.locator('[data-testid="total-transaction-count"]').textContent();
    console.log(`✅ Total transactions processed: ${totalTransactions}`);
    expect(parseInt(totalTransactions || '0')).toBeGreaterThan(100); // Should have significant data

    // Step 2: Unified categorization and review
    console.log('🔍 Step 2: Unified Review...');
    await page.click('[data-testid="review-all-transactions"]');
    
    // Verify bank separation is maintained
    const bankFilters = await page.locator('[data-testid="bank-filter"]').count();
    expect(bankFilters).toBeGreaterThanOrEqual(3); // HDFC, ICICI, SBI

    // Step 3: Consolidated export
    console.log('📋 Step 3: Consolidated Export...');
    await page.click('[data-testid="export-link"]');
    
    // Generate QuickBooks export for all data
    await page.click('[data-testid="export-format-quickbooks"]');
    await page.check('[data-testid="include-all-banks"]');
    await page.click('[data-testid="generate-export-button"]');
    
    await expect(page.locator('[data-testid="export-ready"]')).toBeVisible({ timeout: 45000 });
    
    // Verify consolidated export contains all transactions
    const exportedCount = await page.locator('[data-testid="exported-transaction-count"]').textContent();
    expect(parseInt(exportedCount || '0')).toEqual(parseInt(totalTransactions || '0'));
    
    console.log('🎉 Multi-file processing test completed successfully!');
  });

  test('Export format compliance validation', async ({ page }) => {
    console.log('🎯 Starting export format compliance test...');

    // Step 1: Setup with test data
    await page.click('[data-testid="upload-link"]');
    await page.setInputFiles('[data-testid="file-input"]', TEST_FILES.hdfc);
    await page.click('[data-testid="upload-button"]');
    await expect(page.locator('[data-testid="processing-complete"]')).toBeVisible({ timeout: 60000 });

    // Step 2: Navigate to exports
    await page.click('[data-testid="export-link"]');

    // Step 3: Test all 10 accounting software formats
    const accountingFormats = [
      { name: 'quickbooks', displayName: 'QuickBooks Online' },
      { name: 'xero', displayName: 'Xero' },
      { name: 'tally', displayName: 'Tally' },
      { name: 'sage', displayName: 'Sage' },
      { name: 'wave', displayName: 'Wave Accounting' },
      { name: 'freshbooks', displayName: 'FreshBooks' },
      { name: 'zoho', displayName: 'Zoho Books' },
      { name: 'excel', displayName: 'Excel' },
      { name: 'csv', displayName: 'CSV' },
      { name: 'pdf', displayName: 'PDF Report' }
    ];

    for (const format of accountingFormats) {
      console.log(`📋 Testing ${format.displayName} format compliance...`);
      
      // Select format
      await page.click(`[data-testid="export-format-${format.name}"]`);
      
      // Verify format-specific options are available
      if (['quickbooks', 'xero', 'sage'].includes(format.name)) {
        await expect(page.locator('[data-testid="chart-of-accounts-mapping"]')).toBeVisible();
      }
      
      if (['tally', 'sage'].includes(format.name)) {
        await expect(page.locator('[data-testid="tax-configuration"]')).toBeVisible();
      }
      
      // Generate export
      await page.click('[data-testid="generate-export-button"]');
      await expect(page.locator('[data-testid="export-ready"]')).toBeVisible({ timeout: 30000 });
      
      // Verify format-specific compliance
      const complianceStatus = await page.locator('[data-testid="format-compliance-status"]').textContent();
      expect(complianceStatus).toContain('COMPLIANT');
      
      // Verify file extension and format
      const downloadLink = page.locator(`[data-testid="download-${format.name}"]`);
      const fileName = await downloadLink.getAttribute('download');
      
      // Verify correct file extensions
      if (format.name === 'quickbooks') expect(fileName).toMatch(/\.(iif|qbo)$/);
      else if (format.name === 'excel') expect(fileName).toMatch(/\.xlsx$/);
      else if (format.name === 'csv') expect(fileName).toMatch(/\.csv$/);
      else if (format.name === 'pdf') expect(fileName).toMatch(/\.pdf$/);
      
      console.log(`✅ ${format.displayName} export compliant - file: ${fileName}`);
      
      // Clear for next test
      await page.click('[data-testid="clear-selection"]');
    }

    console.log('🎉 All 10 accounting formats validated successfully!');
  });

  test('Performance and business value validation', async ({ page }) => {
    console.log('🎯 Starting performance and business value test...');

    const startTime = Date.now();

    // Step 1: Complete workflow with timing
    await page.click('[data-testid="upload-link"]');
    
    const uploadStart = Date.now();
    await page.setInputFiles('[data-testid="file-input"]', TEST_FILES.hdfc);
    await page.click('[data-testid="upload-button"]');
    await expect(page.locator('[data-testid="processing-complete"]')).toBeVisible({ timeout: 60000 });
    const uploadTime = Date.now() - uploadStart;
    
    console.log(`⏱️ File processing time: ${uploadTime}ms`);
    expect(uploadTime).toBeLessThan(120000); // Should complete within 2 minutes

    // Step 2: Categorization accuracy validation
    await page.click('[data-testid="review-transactions-button"]');
    
    const accuracyStart = Date.now();
    await expect(page.locator('[data-testid="transaction-table"]')).toBeVisible();
    const accuracyTime = Date.now() - accuracyStart;
    
    console.log(`⏱️ Categorization display time: ${accuracyTime}ms`);
    expect(accuracyTime).toBeLessThan(5000); // Should load within 5 seconds

    // Step 3: Export performance
    await page.click('[data-testid="export-link"]');
    
    const exportStart = Date.now();
    await page.click('[data-testid="export-format-excel"]');
    await page.click('[data-testid="generate-export-button"]');
    await expect(page.locator('[data-testid="export-ready"]')).toBeVisible({ timeout: 30000 });
    const exportTime = Date.now() - exportStart;
    
    console.log(`⏱️ Export generation time: ${exportTime}ms`);
    expect(exportTime).toBeLessThan(30000); // Should complete within 30 seconds

    // Step 4: Overall workflow timing
    const totalTime = Date.now() - startTime;
    console.log(`⏱️ Total workflow time: ${totalTime}ms`);
    expect(totalTime).toBeLessThan(180000); // Complete workflow under 3 minutes

    // Step 5: Business value metrics
    await page.click('[data-testid="insights-link"]');
    
    // Verify time savings calculation
    const manualTime = await page.locator('[data-testid="manual-process-time"]').textContent();
    const automatedTime = await page.locator('[data-testid="automated-process-time"]').textContent();
    const timeSavings = await page.locator('[data-testid="time-savings-percentage"]').textContent();
    
    console.log(`📊 Manual process time: ${manualTime}`);
    console.log(`📊 Automated process time: ${automatedTime}`);
    console.log(`📊 Time savings: ${timeSavings}`);
    
    // Verify minimum 3x speed improvement
    const savingsPercent = parseInt(timeSavings?.replace('%', '') || '0');
    expect(savingsPercent).toBeGreaterThanOrEqual(66); // At least 66% time savings (3x faster)

    console.log('🎉 Performance and business value validation completed!');
  });
});