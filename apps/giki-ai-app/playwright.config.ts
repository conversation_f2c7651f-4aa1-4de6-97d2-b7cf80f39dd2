import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright E2E Testing Configuration
 * Comprehensive end-to-end testing setup for giki.ai platform
 */
export default defineConfig({
  // Test directory
  testDir: './tests/e2e',
  
  // Run tests in files in parallel
  fullyParallel: true,
  
  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,
  
  // Retry on CI only
  retries: process.env.CI ? 2 : 0,
  
  // Opt out of parallel tests on CI
  workers: process.env.CI ? 1 : undefined,
  
  // Reporter to use - simplified for development
  reporter: [
    ['list'],
    ['json', { outputFile: 'test-results/e2e-results.json' }],
  ],
  
  // Shared settings for all the projects below
  use: {
    // Base URL to use in actions like `await page.goto('/')`
    baseURL: 'http://localhost:4200',
    
    // Collect trace when retrying the failed test
    trace: 'on-first-retry',
    
    // Take screenshot on failure
    screenshot: 'only-on-failure',
    
    // Record video on failure
    video: 'retain-on-failure',
    
    // Global timeout for each test
    actionTimeout: 10000,
    
    // Timeout for each assertion
    expectTimeout: 5000,
    
    // Context options to fix localStorage SecurityError
    contextOptions: {
      // Remove storage-access permission as it's not valid and causes errors
      // localStorage should work without explicit permissions in Playwright
    },
  },

  // Configure projects - SINGLE BROWSER for faster development testing
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // Disable animations for consistent testing
        launchOptions: {
          args: ['--disable-animations', '--disable-transitions'],
        },
      },
    },
  ],

  // Global setup and teardown
  globalSetup: './tests/e2e/global-setup.ts',
  globalTeardown: './tests/e2e/global-teardown.ts',

  // Run your local dev server before starting the tests
  // Note: Start servers manually with `pnpm serve:all` before running tests
  webServer: process.env.CI ? undefined : {
    command: 'echo "Please start servers manually with: pnpm serve:all"',
    url: 'http://localhost:4200',
    reuseExistingServer: true,
    timeout: 5000,
  },

  // Test timeout
  timeout: 30000,

  // Expect timeout for assertions
  expect: {
    timeout: 5000,
  },

  // Output directory for test artifacts
  outputDir: 'test-results/e2e-artifacts',

  // Test metadata
  metadata: {
    platform: process.platform,
    project: 'giki-ai-app',
    version: '1.0.0',
  },
});