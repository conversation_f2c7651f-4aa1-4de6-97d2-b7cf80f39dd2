/**
 * Export Readiness Widget - Dashboard Component
 * 
 * Shows real export readiness status and provides quick access to export functionality
 */
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { RefreshCcw, AlertCircle, CheckCircle, ChevronDown, ChevronUp } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Progress } from '@/shared/components/ui/progress';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { DashboardCardSkeleton } from '@/shared/components/ui/skeleton';
import { useExportReadiness } from '../hooks/useExportReadiness';

interface ExportReadinessWidgetProps {
  className?: string;
}

export const ExportReadinessWidget: React.FC<ExportReadinessWidgetProps> = ({
  className = '',
}) => {
  const navigate = useNavigate();
  const { 
    ready, 
    readyFormats, 
    totalFormats, 
    lastCheck, 
    criticalIssues, 
    formats,
    isLoading, 
    error, 
    refresh 
  } = useExportReadiness();

  // Calculate average compliance score
  const avgComplianceScore = formats.length > 0 
    ? Math.round(formats.reduce((sum, f) => sum + (f.complianceScore || 0), 0) / formats.length)
    : 0;

  // Local state for expandable details
  const [showDetails, setShowDetails] = useState(false);

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={`${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold">Export Readiness</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <DashboardCardSkeleton />
          <DashboardCardSkeleton />
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={`${className}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">Export Readiness</CardTitle>
            <Badge variant="destructive">Error</Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">{error}</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={refresh}
            className="w-full"
          >
            <RefreshCcw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  const readinessPercentage = totalFormats > 0 ? (readyFormats / totalFormats) * 100 : 0;

  return (
    <Card className={`cursor-pointer hover:shadow-md transition-shadow ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Export Readiness</CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant={ready ? "default" : "secondary"}>
              {ready ? (
                <>
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Ready
                </>
              ) : (
                <>
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Not Ready
                </>
              )}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                refresh();
              }}
              disabled={isLoading}
            >
              <RefreshCcw className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Accounting formats ready</span>
              <span className="font-medium">
                {readyFormats} / {totalFormats}
              </span>
            </div>
            <Progress value={readinessPercentage} className="h-2" />
          </div>

          {/* Compliance Score */}
          {avgComplianceScore > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Average compliance</span>
                <span className="font-medium text-primary">
                  {avgComplianceScore}%
                </span>
              </div>
              <Progress value={avgComplianceScore} className="h-1" />
            </div>
          )}
        </div>

        {!ready && criticalIssues.length > 0 && (
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-700">To enable export:</p>
            <ul className="text-sm text-gray-600 space-y-1">
              {criticalIssues.slice(0, 2).map((issue, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>{issue}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Format Details Toggle */}
        {formats.length > 0 && (
          <div className="border-t pt-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="w-full justify-between text-xs text-gray-600 h-auto p-2"
            >
              <span>Format Details</span>
              {showDetails ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
            </Button>
            
            {showDetails && (
              <div className="mt-2 space-y-1">
                {formats.slice(0, 3).map((format) => (
                  <div key={format.id} className="flex items-center justify-between text-xs">
                    <span className="truncate flex-1 mr-2">{format.name}</span>
                    <div className="flex items-center space-x-1">
                      {format.complianceScore !== undefined && (
                        <span className="text-gray-500">{format.complianceScore}%</span>
                      )}
                      {format.ready ? (
                        <CheckCircle className="h-3 w-3 text-green-600" />
                      ) : (
                        <AlertCircle className="h-3 w-3 text-yellow-600" />
                      )}
                    </div>
                  </div>
                ))}
                {formats.length > 3 && (
                  <div className="text-center text-xs text-gray-500 pt-1">
                    +{formats.length - 3} more formats
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        <Button
          className="w-full"
          variant={ready ? "default" : "outline"}
          onClick={() => navigate('/reports?export=true')}
        >
          {ready ? 'Export Data' : 'View Export Status'}
          <span className="ml-2">→</span>
        </Button>

        {lastCheck && (
          <p className="text-xs text-gray-500 text-center">
            Last checked: {lastCheck.toLocaleTimeString()}
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default ExportReadinessWidget;