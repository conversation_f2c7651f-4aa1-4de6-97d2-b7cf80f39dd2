/**
 * Export History Widget - Dashboard Component
 * 
 * Shows recent export history with quick download and re-export actions
 */
import React, { useState } from 'react';
import { Download, RefreshCw, Trash2, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { DashboardCardSkeleton } from '@/shared/components/ui/skeleton';
import { useToast } from '@/shared/components/ui/use-toast';
import { useExportHistory } from '../hooks/useExportHistory';

interface ExportHistoryWidgetProps {
  className?: string;
  maxItems?: number;
}

export const ExportHistoryWidget: React.FC<ExportHistoryWidgetProps> = ({
  className = '',
  maxItems = 5,
}) => {
  const { exports, isLoading, error, refresh, downloadExport, reExport, deleteExport } = useExportHistory();
  const { toast } = useToast();
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const handleDownload = async (exportId: string) => {
    try {
      setActionLoading(`download-${exportId}`);
      await downloadExport(exportId);
      toast({
        title: 'Download Started',
        description: 'Export file download initiated.',
      });
    } catch (error) {
      toast({
        title: 'Download Failed',
        description: (error as Error).message || 'Failed to download export.',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleReExport = async (exportId: string) => {
    try {
      setActionLoading(`reexport-${exportId}`);
      await reExport(exportId);
      toast({
        title: 'Re-Export Started',
        description: 'Opening export page with previous settings.',
      });
    } catch (error) {
      toast({
        title: 'Re-Export Failed',
        description: (error as Error).message || 'Failed to start re-export.',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async (exportId: string) => {
    try {
      setActionLoading(`delete-${exportId}`);
      await deleteExport(exportId);
      toast({
        title: 'Export Removed',
        description: 'Export removed from history.',
      });
    } catch (error) {
      toast({
        title: 'Delete Failed',
        description: (error as Error).message || 'Failed to remove export.',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-3 w-3 text-green-600" />;
      case 'failed':
        return <XCircle className="h-3 w-3 text-red-600" />;
      case 'processing':
        return <Clock className="h-3 w-3 text-blue-600" />;
      default:
        return <AlertCircle className="h-3 w-3 text-gray-400" />;
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default' as const;
      case 'failed':
        return 'destructive' as const;
      case 'processing':
        return 'secondary' as const;
      default:
        return 'outline' as const;
    }
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold">Export History</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <DashboardCardSkeleton key={i} />
          ))}
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">Export History</CardTitle>
            <Badge variant="destructive">Error</Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">{error}</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={refresh}
            className="w-full"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  const displayExports = exports.slice(0, maxItems);

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Export History</CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">{exports.length}</Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={refresh}
              disabled={isLoading}
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {displayExports.length === 0 ? (
          <div className="text-center py-6 text-gray-500">
            <Download className="h-8 w-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No exports yet</p>
            <p className="text-xs text-gray-400">Your export history will appear here</p>
          </div>
        ) : (
          displayExports.map((exportItem) => (
            <div
              key={exportItem.id}
              className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-sm font-medium truncate">
                    {exportItem.formatName}
                  </span>
                  <Badge
                    variant={getStatusVariant(exportItem.status)}
                    className="text-xs"
                  >
                    <span className="flex items-center space-x-1">
                      {getStatusIcon(exportItem.status)}
                      <span>{exportItem.status}</span>
                    </span>
                  </Badge>
                </div>
                <div className="flex items-center space-x-3 text-xs text-gray-500">
                  <span>{exportItem.fileSize}</span>
                  <span>{formatDate(exportItem.createdAt)}</span>
                  {exportItem.expiresAt && exportItem.expiresAt < new Date() && (
                    <span className="text-red-500">Expired</span>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-1">
                {exportItem.status === 'completed' && (!exportItem.expiresAt || exportItem.expiresAt > new Date()) && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDownload(exportItem.id)}
                    disabled={actionLoading === `download-${exportItem.id}`}
                    className="h-8 w-8 p-0"
                  >
                    <Download className="h-3 w-3" />
                  </Button>
                )}
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleReExport(exportItem.id)}
                  disabled={actionLoading === `reexport-${exportItem.id}`}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className="h-3 w-3" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDelete(exportItem.id)}
                  disabled={actionLoading === `delete-${exportItem.id}`}
                  className="h-8 w-8 p-0 text-gray-400 hover:text-red-600"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ))
        )}
        
        {exports.length > maxItems && (
          <div className="text-center pt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open('/reports?history=true', '_blank')}
              className="text-xs text-gray-500"
            >
              View all {exports.length} exports →
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ExportHistoryWidget;