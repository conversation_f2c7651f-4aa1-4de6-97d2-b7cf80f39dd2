/**
 * Trends Analysis Page - Business Intelligence
 * AI-powered spending trend analysis with real backend integration
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { But<PERSON> } from '@/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  Calendar,
  Brain,
  Target,
  AlertTriangle,
  RefreshCw,
} from 'lucide-react';
import { reportService, dashboardService } from '@/shared/services/base';
import { useToast } from '@/shared/components/ui/use-toast';

interface TrendData {
  period: string;
  value: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
}

interface CategoryTrend {
  category: string;
  currentAmount: number;
  previousAmount: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  transactions: number;
  avgTransactionSize: number;
}

interface PredictiveInsight {
  type: 'opportunity' | 'risk' | 'anomaly' | 'pattern';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  confidence: number;
  recommendation: string;
}

export const TrendsAnalysisPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState('3_months');
  const [selectedMetric, setSelectedMetric] = useState('expenses');
  const [monthlyTrends, setMonthlyTrends] = useState<TrendData[]>([]);
  const [categoryTrends, setCategoryTrends] = useState<CategoryTrend[]>([]);
  const [predictiveInsights, setPredictiveInsights] = useState<
    PredictiveInsight[]
  >([]);
  const [overallMetrics, setOverallMetrics] = useState({
    totalTransactions: 0,
    avgMonthlyGrowth: 0,
    seasonalityIndex: 0,
    volatilityScore: 0,
  });

  // Load trends data from backend
  const loadTrendsData = useCallback(async () => {
    try {
      setIsLoading(true);

      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(
        startDate.getMonth() -
          (selectedTimeframe === '6_months'
            ? 6
            : selectedTimeframe === '12_months'
              ? 12
              : 3),
      );

      // Get monthly trends
      const trendsResult = await reportService.getMonthlyTrends(
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0],
      );

      if (trendsResult.success) {
        const trends = trendsResult.data;
        const trendData: TrendData[] = trends.map((trend, index, arr) => {
          const value =
            selectedMetric === 'income' ? trend.income : trend.expenses;
          const previousValue =
            index > 0
              ? selectedMetric === 'income'
                ? arr[index - 1].income
                : arr[index - 1].expenses
              : value;
          const change =
            previousValue > 0
              ? ((value - previousValue) / previousValue) * 100
              : 0;

          return {
            period: trend.month,
            value,
            change,
            trend: change > 2 ? 'up' : change < -2 ? 'down' : 'stable',
          };
        });
        setMonthlyTrends(trendData);

        // Calculate overall metrics
        const avgGrowth =
          trendData.reduce((sum, t) => sum + t.change, 0) / trendData.length;
        const volatility = Math.sqrt(
          trendData.reduce(
            (sum, t) => sum + Math.pow(t.change - avgGrowth, 2),
            0,
          ) / trendData.length,
        );

        setOverallMetrics((prev) => ({
          ...prev,
          avgMonthlyGrowth: avgGrowth,
          volatilityScore: volatility,
        }));
      }

      // Get category trends for comparison
      const categoryResult = await dashboardService.getCategoryBreakdown();

      if (categoryResult.success) {
        // Get previous period for comparison
        const prevStartDate = new Date(startDate);
        prevStartDate.setMonth(prevStartDate.getMonth() - 3);
        const prevEndDate = new Date(startDate);

        const prevCategoryResult = await dashboardService.getCategoryBreakdown();

        if (prevCategoryResult.success) {
          const categoryTrendsData: CategoryTrend[] = categoryResult.data
            .map((current) => {
              const previous = prevCategoryResult.data.find(
                (p) => p.category === current.category,
              );
              const previousAmount = previous?.amount || 0;
              const change =
                previousAmount > 0
                  ? ((current.amount - previousAmount) / previousAmount) * 100
                  : 0;

              return {
                category: current.category,
                currentAmount: current.amount,
                previousAmount,
                change,
                trend: change > 5 ? 'up' : change < -5 ? 'down' : 'stable',
                transactions: current.transaction_count,
                avgTransactionSize:
                  current.transaction_count > 0
                    ? current.amount / current.transaction_count
                    : 0,
              };
            })
            .sort((a, b) => Math.abs(b.change) - Math.abs(a.change));

          setCategoryTrends(categoryTrendsData.slice(0, 10)); // Top 10 by change magnitude
        }
      }
    } catch (error) {
      console.error('Error loading trends data:', error);
      toast({
        title: 'Error Loading Trends',
        description: 'Failed to load trends analysis. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [selectedTimeframe, selectedMetric, toast]);

  // Generate AI-powered insights
  const generateInsights = useCallback(() => {
    try {
      setIsAnalyzing(true);

      // Analyze patterns in the data to generate insights
      const insights: PredictiveInsight[] = [];

      // Growth trend analysis
      if (overallMetrics.avgMonthlyGrowth > 10) {
        insights.push({
          type: 'opportunity',
          title: 'Strong Growth Trajectory',
          description: `Your ${selectedMetric} are growing at ${overallMetrics.avgMonthlyGrowth.toFixed(1)}% monthly, well above industry average.`,
          impact: 'high',
          confidence: 92,
          recommendation:
            'Consider scaling operations or increasing investment to capitalize on this growth.',
        });
      } else if (overallMetrics.avgMonthlyGrowth < -5) {
        insights.push({
          type: 'risk',
          title: 'Declining Trend Detected',
          description: `Your ${selectedMetric} are declining at ${Math.abs(overallMetrics.avgMonthlyGrowth).toFixed(1)}% monthly.`,
          impact: 'high',
          confidence: 88,
          recommendation:
            'Review operational efficiency and consider cost optimization strategies.',
        });
      }

      // Volatility analysis
      if (overallMetrics.volatilityScore > 15) {
        insights.push({
          type: 'risk',
          title: 'High Volatility Detected',
          description: `Your ${selectedMetric} show high volatility (${overallMetrics.volatilityScore.toFixed(1)}%), indicating unpredictable cash flow.`,
          impact: 'medium',
          confidence: 85,
          recommendation:
            'Implement cash flow forecasting and maintain higher cash reserves.',
        });
      }

      // Category-specific insights
      const topGrowingCategory = categoryTrends.find((c) => c.trend === 'up');
      if (topGrowingCategory) {
        insights.push({
          type: 'pattern',
          title: 'Category Growth Pattern',
          description: `${topGrowingCategory.category} shows strong growth of ${topGrowingCategory.change.toFixed(1)}% with ${topGrowingCategory.transactions} transactions.`,
          impact: 'medium',
          confidence: 79,
          recommendation:
            'Analyze drivers behind this category growth for potential replication.',
        });
      }

      const anomalyCategory = categoryTrends.find(
        (c) => Math.abs(c.change) > 50,
      );
      if (anomalyCategory) {
        insights.push({
          type: 'anomaly',
          title: 'Unusual Category Activity',
          description: `${anomalyCategory.category} shows unusual ${Math.abs(anomalyCategory.change).toFixed(1)}% change in spending pattern.`,
          impact: 'medium',
          confidence: 83,
          recommendation:
            'Investigate this anomaly to understand underlying business changes.',
        });
      }

      setPredictiveInsights(insights);
    } catch (error) {
      console.error('Error generating insights:', error);
      toast({
        title: 'Analysis Error',
        description: 'Failed to generate insights. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsAnalyzing(false);
    }
  }, [overallMetrics, categoryTrends, selectedMetric, toast]);

  useEffect(() => {
    void loadTrendsData();
  }, [loadTrendsData]);

  useEffect(() => {
    if (monthlyTrends.length > 0 && categoryTrends.length > 0) {
      void generateInsights();
    }
  }, [monthlyTrends, categoryTrends, generateInsights]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const getInsightIcon = (type: PredictiveInsight['type']) => {
    switch (type) {
      case 'opportunity':
        return <Target className="w-5 h-5 text-success" />;
      case 'risk':
        return <AlertTriangle className="w-5 h-5 text-error" />;
      case 'pattern':
        return <Brain className="w-5 h-5 text-blue-600" />;
      case 'anomaly':
        return <RefreshCw className="w-5 h-5 text-yellow-600" />;
    }
  };

  const getInsightColor = (type: PredictiveInsight['type']) => {
    switch (type) {
      case 'opportunity':
        return 'border-green-200 bg-green-50';
      case 'risk':
        return 'border-red-200 bg-red-50';
      case 'pattern':
        return 'border-blue-200 bg-blue-50';
      case 'anomaly':
        return 'border-yellow-200 bg-yellow-50';
    }
  };

  if (isLoading) {
    return (
      <div className="p-8 space-y-6 bg-white min-h-screen">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div
              className="w-8 h-8 border-4 border-t-transparent rounded-full animate-spin mx-auto mb-4"
              style={{ borderColor: 'var(--giki-primary)', borderTopColor: 'transparent' }}
            />
            <p className="text-gray-600">Loading trends analysis...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 space-y-6 bg-white min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Advanced Trends Analysis
          </h1>
          <p className="text-gray-600">
            AI-powered spending pattern analysis with predictive insights
          </p>
        </div>
        <div className="flex gap-3">
          <Select
            value={selectedTimeframe}
            onValueChange={setSelectedTimeframe}
          >
            <SelectTrigger className="w-40">
              <Calendar className="w-4 h-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3_months">Last 3 Months</SelectItem>
              <SelectItem value="6_months">Last 6 Months</SelectItem>
              <SelectItem value="12_months">Last 12 Months</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedMetric} onValueChange={setSelectedMetric}>
            <SelectTrigger className="w-32">
              <BarChart3 className="w-4 h-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="expenses">Expenses</SelectItem>
              <SelectItem value="income">Income</SelectItem>
            </SelectContent>
          </Select>
          <Button
            onClick={() => void generateInsights()}
            disabled={isAnalyzing}
            className="bg-brand-primary hover:bg-brand-primary-hover text-white"
          >
            {isAnalyzing ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Brain className="w-4 h-4 mr-2" />
                Generate Insights
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              Average Growth
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="text-3xl font-bold font-mono text-gray-900">
                {formatPercentage(overallMetrics.avgMonthlyGrowth)}
              </div>
              {overallMetrics.avgMonthlyGrowth > 0 ? (
                <TrendingUp className="w-6 h-6 text-success ml-2" />
              ) : (
                <TrendingDown className="w-6 h-6 text-error ml-2" />
              )}
            </div>
            <p className="text-sm text-gray-600 mt-1">Monthly average</p>
          </CardContent>
        </Card>

        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              Volatility Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold font-mono text-gray-900">
              {overallMetrics.volatilityScore.toFixed(1)}
            </div>
            <p className="text-sm text-gray-600 mt-1">
              {overallMetrics.volatilityScore < 10
                ? 'Low'
                : overallMetrics.volatilityScore < 20
                  ? 'Medium'
                  : 'High'}{' '}
              variance
            </p>
          </CardContent>
        </Card>

        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              Trend Categories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold font-mono text-gray-900">
              {categoryTrends.length}
            </div>
            <p className="text-sm text-gray-600 mt-1">Active categories</p>
          </CardContent>
        </Card>

        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              AI Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold font-mono text-gray-900">
              {predictiveInsights.length}
            </div>
            <p className="text-sm text-gray-600 mt-1">Generated insights</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="trends" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-gray-100">
          <TabsTrigger
            value="trends"
            className="data-[state=active]:bg-brand-primary data-[state=active]:text-white"
          >
            Monthly Trends
          </TabsTrigger>
          <TabsTrigger
            value="categories"
            className="data-[state=active]:bg-brand-primary data-[state=active]:text-white"
          >
            Category Analysis
          </TabsTrigger>
          <TabsTrigger
            value="insights"
            className="data-[state=active]:bg-brand-primary data-[state=active]:text-white"
          >
            AI Insights
          </TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" style={{ color: 'var(--giki-primary)' }} />
                {selectedMetric.charAt(0).toUpperCase() +
                  selectedMetric.slice(1)}{' '}
                Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {monthlyTrends.map((trend, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-success/10 transition-colors"
                  >
                    <div className="flex items-center gap-4">
                      <div className="text-lg font-semibold text-gray-900 min-w-[120px]">
                        {trend.period}
                      </div>
                      <div className="text-2xl font-mono font-bold text-gray-900">
                        {formatCurrency(trend.value)}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className={`text-lg font-bold ${
                          trend.trend === 'up'
                            ? 'text-success'
                            : trend.trend === 'down'
                              ? 'text-error'
                              : 'text-gray-600'
                        }`}
                      >
                        {formatPercentage(trend.change)}
                      </div>
                      {trend.trend === 'up' && (
                        <TrendingUp className="w-5 h-5 text-success" />
                      )}
                      {trend.trend === 'down' && (
                        <TrendingDown className="w-5 h-5 text-error" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="w-5 h-5" style={{ color: 'var(--giki-primary)' }} />
                Category Trend Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {categoryTrends.map((category, index) => (
                  <div
                    key={index}
                    className="border border-gray-200 rounded-lg p-4 hover:border-brand-primary transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-lg font-semibold text-gray-900">
                        {category.category}
                      </div>
                      <div
                        className={`flex items-center gap-2 ${
                          category.trend === 'up'
                            ? 'text-success'
                            : category.trend === 'down'
                              ? 'text-error'
                              : 'text-gray-600'
                        }`}
                      >
                        <span className="text-lg font-bold">
                          {formatPercentage(category.change)}
                        </span>
                        {category.trend === 'up' && (
                          <TrendingUp className="w-5 h-5" />
                        )}
                        {category.trend === 'down' && (
                          <TrendingDown className="w-5 h-5" />
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">Current: </span>
                        {formatCurrency(category.currentAmount)}
                      </div>
                      <div>
                        <span className="font-medium">Transactions: </span>
                        {category.transactions}
                      </div>
                      <div>
                        <span className="font-medium">Avg Size: </span>
                        {formatCurrency(category.avgTransactionSize)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid gap-6">
            {predictiveInsights.length === 0 ? (
              <Card className="p-8 text-center">
                <CardContent>
                  <Brain className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <div className="text-xl text-gray-600 mb-2">
                    No insights generated yet
                  </div>
                  <div className="text-sm text-gray-500">
                    Click "Generate Insights" to analyze your data patterns
                  </div>
                </CardContent>
              </Card>
            ) : (
              predictiveInsights.map((insight, index) => (
                <Card
                  key={index}
                  className={`${getInsightColor(insight.type)} border-l-4`}
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        {getInsightIcon(insight.type)}
                        <div>
                          <CardTitle className="text-lg">
                            {insight.title}
                          </CardTitle>
                          <div className="flex items-center gap-2 mt-1">
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${
                                insight.impact === 'high'
                                  ? 'bg-red-100 text-red-700'
                                  : insight.impact === 'medium'
                                    ? 'bg-yellow-100 text-yellow-700'
                                    : 'bg-green-100 text-green-700'
                              }`}
                            >
                              {insight.impact.toUpperCase()} IMPACT
                            </span>
                            <span className="text-xs text-gray-600">
                              {insight.confidence}% confidence
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 mb-3">{insight.description}</p>
                    <div className="bg-white/50 rounded-lg p-3">
                      <div className="text-sm font-medium text-gray-800 mb-1">
                        Recommendation:
                      </div>
                      <div className="text-sm text-gray-700">
                        {insight.recommendation}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TrendsAnalysisPage;
