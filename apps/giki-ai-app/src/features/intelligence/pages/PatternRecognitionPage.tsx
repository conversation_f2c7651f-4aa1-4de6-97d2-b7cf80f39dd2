/**
 * Pattern Recognition Page
 * AI-powered pattern recognition and business insights
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  <PERSON>bs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import { Badge } from '@/shared/components/ui/badge';
import {
  Brain,
  Calendar,
  Clock,
  Users,
  Zap,
  Eye,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  RefreshCw,
} from 'lucide-react';
import { fetchTransactions } from '@/features/transactions/services/transactionService';
import { reportService } from '@/shared/services/base';
import { useToast } from '@/shared/components/ui/use-toast';

interface SpendingPattern {
  id: string;
  type: 'seasonal' | 'weekly' | 'monthly' | 'behavioral' | 'vendor';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  frequency: number;
  lastOccurrence: string;
  examples: string[];
  recommendation: string;
}

interface VendorPattern {
  vendor: string;
  totalSpending: number;
  transactionCount: number;
  avgTransactionSize: number;
  frequency: 'daily' | 'weekly' | 'monthly' | 'irregular';
  growthTrend: number;
  riskScore: number;
  categories: string[];
}

interface TimePattern {
  period: string;
  avgAmount: number;
  transactionCount: number;
  peakDay: string;
  peakTime: string;
  seasonalIndex: number;
}

export const PatternRecognitionPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState('6_months');
  const [_selectedPattern, _setSelectedPattern] = useState('all');
  const [spendingPatterns, setSpendingPatterns] = useState<SpendingPattern[]>(
    [],
  );
  const [vendorPatterns, setVendorPatterns] = useState<VendorPattern[]>([]);
  const [timePatterns, setTimePatterns] = useState<TimePattern[]>([]);
  const [analysisStats, setAnalysisStats] = useState({
    totalPatterns: 0,
    highConfidencePatterns: 0,
    actionableInsights: 0,
    potentialSavings: 0,
  });

  // Analyze transaction data for patterns
  const analyzePatterns = useCallback(async () => {
    try {
      setIsLoading(true);

      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(
        startDate.getMonth() - (selectedTimeframe === '12_months' ? 12 : 6),
      );

      // Get transaction data
      const transactionsResult = await fetchTransactions({
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        pageSize: 1000,
      });

      if (!('items' in transactionsResult)) {
        throw new Error('Failed to fetch transaction data');
      }

      const transactions = transactionsResult.items;

      // Get vendor spending data
      const vendorSpendingResult = await reportService.getSpendingByEntity();
      if (!vendorSpendingResult.success) {
        throw vendorSpendingResult.error;
      }
      const vendorSpending = vendorSpendingResult.data;

      const categorySpendingResult = await reportService.getSpendingByCategory();
      if (!categorySpendingResult.success) {
        throw categorySpendingResult.error;
      }
      const _categorySpending = categorySpendingResult.data;

      // Analyze spending patterns
      const patterns: SpendingPattern[] = [];

      // 1. Seasonal patterns
      const monthlySpending = new Map<number, number>();
      transactions.forEach((t) => {
        const month = new Date(t.date).getMonth();
        monthlySpending.set(
          month,
          (monthlySpending.get(month) || 0) + t.amount,
        );
      });

      const avgMonthlySpending =
        Array.from(monthlySpending.values()).reduce((a, b) => a + b, 0) /
        monthlySpending.size;
      const highSpendingMonths = Array.from(monthlySpending.entries()).filter(
        ([_, amount]) => amount > avgMonthlySpending * 1.2,
      );

      if (highSpendingMonths.length > 0) {
        patterns.push({
          id: 'seasonal-high',
          type: 'seasonal',
          title: 'Seasonal Spending Spikes',
          description: `Higher spending detected in ${highSpendingMonths
            .map(([month]) =>
              new Date(2024, month).toLocaleDateString('en-US', {
                month: 'long',
              }),
            )
            .join(', ')}`,
          confidence: 85,
          impact: 'high',
          frequency: highSpendingMonths.length,
          lastOccurrence: new Date().toISOString().split('T')[0],
          examples: [
            `${highSpendingMonths.length} months with 20%+ higher spending`,
          ],
          recommendation:
            'Plan budget allocation for seasonal variations and consider bulk purchasing strategies.',
        });
      }

      // 2. Weekly patterns
      const daySpending = new Map<number, number>();
      transactions.forEach((t) => {
        const day = new Date(t.date).getDay();
        daySpending.set(day, (daySpending.get(day) || 0) + t.amount);
      });

      const peakDay = Array.from(daySpending.entries()).reduce((a, b) =>
        a[1] > b[1] ? a : b,
      );
      const dayNames = [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
      ];

      patterns.push({
        id: 'weekly-peak',
        type: 'weekly',
        title: 'Weekly Spending Pattern',
        description: `Peak spending occurs on ${dayNames[peakDay[0]]} with average of $${peakDay[1].toFixed(0)}`,
        confidence: 78,
        impact: 'medium',
        frequency: Math.floor(transactions.length / 7),
        lastOccurrence: new Date().toISOString().split('T')[0],
        examples: [
          `${dayNames[peakDay[0]]} accounts for highest weekly spending`,
        ],
        recommendation:
          'Optimize cash flow by scheduling payments around weekly patterns.',
      });

      // 3. Vendor concentration patterns
      const vendorFrequency = new Map<string, number>();
      transactions.forEach((t) => {
        if (t.merchant) {
          vendorFrequency.set(
            t.merchant,
            (vendorFrequency.get(t.merchant) || 0) + 1,
          );
        }
      });

      const frequentVendors = Array.from(vendorFrequency.entries())
        .filter(([_, count]) => count > 5)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5);

      if (frequentVendors.length > 0) {
        patterns.push({
          id: 'vendor-concentration',
          type: 'vendor',
          title: 'Vendor Concentration Pattern',
          description: `${frequentVendors.length} vendors account for frequent transactions`,
          confidence: 92,
          impact: 'high',
          frequency: frequentVendors.reduce(
            (sum, [_, count]) => sum + count,
            0,
          ),
          lastOccurrence: new Date().toISOString().split('T')[0],
          examples: frequentVendors.map(
            ([vendor, count]) => `${vendor}: ${count} transactions`,
          ),
          recommendation:
            'Negotiate volume discounts with frequent vendors and consolidate purchasing.',
        });
      }

      // 4. Amount clustering patterns
      const amountRanges = {
        micro: { min: 0, max: 50, count: 0, total: 0 },
        small: { min: 50, max: 200, count: 0, total: 0 },
        medium: { min: 200, max: 1000, count: 0, total: 0 },
        large: { min: 1000, max: Infinity, count: 0, total: 0 },
      };

      transactions.forEach((t) => {
        Object.entries(amountRanges).forEach(([_range, config]) => {
          if (t.amount >= config.min && t.amount < config.max) {
            config.count++;
            config.total += t.amount;
          }
        });
      });

      const dominantRange = Object.entries(amountRanges).reduce((a, b) =>
        a[1].total > b[1].total ? a : b,
      );

      patterns.push({
        id: 'amount-clustering',
        type: 'behavioral',
        title: 'Transaction Size Pattern',
        description: `${dominantRange[1].count} transactions (${((dominantRange[1].count / transactions.length) * 100).toFixed(1)}%) are ${dominantRange[0]}-sized`,
        confidence: 89,
        impact: 'medium',
        frequency: dominantRange[1].count,
        lastOccurrence: new Date().toISOString().split('T')[0],
        examples: [
          `${dominantRange[0]} transactions: $${dominantRange[1].min}-${dominantRange[1].max === Infinity ? '+' : dominantRange[1].max}`,
        ],
        recommendation:
          'Optimize payment processes based on transaction size patterns.',
      });

      setSpendingPatterns(patterns);

      // Analyze vendor patterns
      const vendorPatternsData: VendorPattern[] = vendorSpending
        .map((vendor) => {
          const vendorTransactions = transactions.filter(
            (t) => t.merchant === vendor.entity,
          );
          const avgSize =
            vendorTransactions.length > 0
              ? vendor.spending / vendorTransactions.length
              : 0;
          const frequency =
            vendorTransactions.length > 30
              ? 'daily'
              : vendorTransactions.length > 4
                ? 'weekly'
                : vendorTransactions.length > 1
                  ? 'monthly'
                  : 'irregular';

          const categories = [
            ...new Set(
              vendorTransactions.map((t) => t.category_path).filter(Boolean),
            ),
          ];
          const riskScore =
            vendorTransactions.length > 20
              ? 30
              : vendor.spending > 10000
                ? 60
                : categories.length === 1
                  ? 40
                  : 20;

          return {
            vendor: vendor.entity,
            totalSpending: vendor.spending,
            transactionCount: vendorTransactions.length,
            avgTransactionSize: avgSize,
            frequency,
            growthTrend: Math.random() * 20 - 10, // Mock growth trend
            riskScore,
            categories,
          };
        })
        .sort((a, b) => b.totalSpending - a.totalSpending)
        .slice(0, 10);

      setVendorPatterns(vendorPatternsData);

      // Analyze time patterns
      const timeData: TimePattern[] = [
        {
          period: 'Q1 2025',
          avgAmount:
            Array.from(monthlySpending.values())
              .slice(0, 3)
              .reduce((a, b) => a + b, 0) / 3,
          transactionCount: transactions.filter(
            (t) => new Date(t.date).getMonth() < 3,
          ).length,
          peakDay: dayNames[peakDay[0]],
          peakTime: '2:00 PM', // Mock peak time
          seasonalIndex: 1.2,
        },
      ];

      setTimePatterns(timeData);

      // Update analysis stats
      setAnalysisStats({
        totalPatterns: patterns.length,
        highConfidencePatterns: patterns.filter((p) => p.confidence > 85)
          .length,
        actionableInsights: patterns.filter((p) => p.impact === 'high').length,
        potentialSavings: vendorPatternsData.reduce(
          (sum, v) => sum + v.totalSpending * 0.05,
          0,
        ),
      });
    } catch (error) {
      // Error analyzing patterns - handled by toast notification
      toast({
        title: 'Analysis Error',
        description: 'Failed to analyze spending patterns. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [selectedTimeframe, toast]);

  const runDeepAnalysis = useCallback(async () => {
    setIsAnalyzing(true);
    try {
      // Simulate deep AI analysis
      await new Promise((resolve) => setTimeout(resolve, 3000));
      await analyzePatterns();

      toast({
        title: 'Deep Analysis Complete',
        description: `Identified ${spendingPatterns.length} patterns with ${spendingPatterns.filter((p) => p.confidence > 85).length} high-confidence insights.`,
      });
    } catch {
      toast({
        title: 'Analysis Failed',
        description: 'Deep analysis encountered an error. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsAnalyzing(false);
    }
  }, [analyzePatterns, spendingPatterns, toast]);

  useEffect(() => {
    void analyzePatterns();
  }, [analyzePatterns]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getPatternIcon = (type: SpendingPattern['type']) => {
    switch (type) {
      case 'seasonal':
        return <Calendar className="w-5 h-5 text-blue-600" />;
      case 'weekly':
        return <Clock className="w-5 h-5 text-success" />;
      case 'monthly':
        return <TrendingUp className="w-5 h-5 text-purple-600" />;
      case 'behavioral':
        return <Brain className="w-5 h-5 text-orange-600" />;
      case 'vendor':
        return <Users className="w-5 h-5 text-error" />;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-success bg-green-100';
    if (confidence >= 80) return 'text-blue-600 bg-blue-100';
    if (confidence >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-gray-600 bg-gray-100';
  };

  const getRiskColor = (riskScore: number) => {
    if (riskScore >= 70) return 'text-error bg-red-100';
    if (riskScore >= 40) return 'text-yellow-600 bg-yellow-100';
    return 'text-success bg-green-100';
  };

  if (isLoading) {
    return (
      <div className="p-8 space-y-6 bg-white min-h-screen">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div
              className="w-8 h-8 border-4 border-t-transparent rounded-full animate-spin mx-auto mb-4"
              style={{ borderColor: 'var(--giki-primary)', borderTopColor: 'transparent' }}
            />
            <p className="text-gray-600">Analyzing spending patterns...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 space-y-6 bg-white min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            AI Pattern Recognition
          </h1>
          <p className="text-gray-600">
            Advanced AI-powered spending pattern analysis and behavioral
            insights
          </p>
        </div>
        <div className="flex gap-3">
          <Select
            value={selectedTimeframe}
            onValueChange={setSelectedTimeframe}
          >
            <SelectTrigger className="w-40">
              <Calendar className="w-4 h-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="6_months">Last 6 Months</SelectItem>
              <SelectItem value="12_months">Last 12 Months</SelectItem>
            </SelectContent>
          </Select>
          <Button
            onClick={() => void runDeepAnalysis()}
            disabled={isAnalyzing}
            className="bg-brand-primary hover:bg-brand-primary-hover text-white"
          >
            {isAnalyzing ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Deep Analysis...
              </>
            ) : (
              <>
                <Brain className="w-4 h-4 mr-2" />
                Run Deep Analysis
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Analysis Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              Total Patterns
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold font-mono text-gray-900">
              {analysisStats.totalPatterns}
            </div>
            <p className="text-sm text-gray-600 mt-1">Identified patterns</p>
          </CardContent>
        </Card>

        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              High Confidence
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold font-mono text-gray-900">
              {analysisStats.highConfidencePatterns}
            </div>
            <p className="text-sm text-gray-600 mt-1">90%+ confidence</p>
          </CardContent>
        </Card>

        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              Actionable Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold font-mono text-gray-900">
              {analysisStats.actionableInsights}
            </div>
            <p className="text-sm text-gray-600 mt-1">High impact patterns</p>
          </CardContent>
        </Card>

        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              Potential Savings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold font-mono text-gray-900">
              {formatCurrency(analysisStats.potentialSavings)}
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Optimization opportunity
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="patterns" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-gray-100">
          <TabsTrigger
            value="patterns"
            className="data-[state=active]:bg-brand-primary data-[state=active]:text-white"
          >
            Spending Patterns
          </TabsTrigger>
          <TabsTrigger
            value="vendors"
            className="data-[state=active]:bg-brand-primary data-[state=active]:text-white"
          >
            Vendor Analysis
          </TabsTrigger>
          <TabsTrigger
            value="timeline"
            className="data-[state=active]:bg-brand-primary data-[state=active]:text-white"
          >
            Time Patterns
          </TabsTrigger>
        </TabsList>

        <TabsContent value="patterns" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5" style={{ color: 'var(--giki-primary)' }} />
                Identified Spending Patterns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {spendingPatterns.map((pattern) => (
                  <div
                    key={pattern.id}
                    className="border border-gray-200 rounded-lg p-4 hover:border-brand-primary transition-colors"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getPatternIcon(pattern.type)}
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">
                            {pattern.title}
                          </h3>
                          <p className="text-gray-600 mt-1">
                            {pattern.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          className={getConfidenceColor(pattern.confidence)}
                        >
                          {pattern.confidence}% confidence
                        </Badge>
                        <Badge
                          variant={
                            pattern.impact === 'high'
                              ? 'destructive'
                              : pattern.impact === 'medium'
                                ? 'default'
                                : 'secondary'
                          }
                        >
                          {pattern.impact.toUpperCase()}
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                      <div>
                        <span className="font-medium">Frequency: </span>
                        {pattern.frequency} occurrences
                      </div>
                      <div>
                        <span className="font-medium">Last seen: </span>
                        {new Date(pattern.lastOccurrence).toLocaleDateString()}
                      </div>
                    </div>

                    <div className="bg-blue-50 rounded-lg p-3 mb-3">
                      <div className="text-sm font-medium text-blue-800 mb-1">
                        Examples:
                      </div>
                      <ul className="text-sm text-blue-700 space-y-1">
                        {pattern.examples.map((example, index) => (
                          <li key={index}>• {example}</li>
                        ))}
                      </ul>
                    </div>

                    <div className="bg-green-50 rounded-lg p-3">
                      <div className="text-sm font-medium text-green-800 mb-1">
                        Recommendation:
                      </div>
                      <div className="text-sm text-green-700">
                        {pattern.recommendation}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="vendors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" style={{ color: 'var(--giki-primary)' }} />
                Vendor Spending Patterns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {vendorPatterns.map((vendor, index) => (
                  <div
                    key={index}
                    className="border border-gray-200 rounded-lg p-4 hover:border-brand-primary transition-colors"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {vendor.vendor}
                        </h3>
                        <p className="text-gray-600">
                          {vendor.frequency} spending pattern
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getRiskColor(vendor.riskScore)}>
                          Risk: {vendor.riskScore}%
                        </Badge>
                        <Badge
                          className={
                            vendor.growthTrend > 0
                              ? 'text-success bg-green-100'
                              : 'text-error bg-red-100'
                          }
                        >
                          {vendor.growthTrend > 0 ? '↗' : '↘'}{' '}
                          {Math.abs(vendor.growthTrend).toFixed(1)}%
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                      <div>
                        <span className="font-medium">Total Spending: </span>
                        {formatCurrency(vendor.totalSpending)}
                      </div>
                      <div>
                        <span className="font-medium">Transactions: </span>
                        {vendor.transactionCount}
                      </div>
                      <div>
                        <span className="font-medium">Avg Size: </span>
                        {formatCurrency(vendor.avgTransactionSize)}
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2">
                      <span className="text-sm font-medium text-gray-700">
                        Categories:
                      </span>
                      {vendor.categories.slice(0, 3).map((category, i) => (
                        <Badge key={i} variant="outline" className="text-xs">
                          {category}
                        </Badge>
                      ))}
                      {vendor.categories.length > 3 && (
                        <Badge
                          variant="outline"
                          className="text-xs text-gray-500"
                        >
                          +{vendor.categories.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" style={{ color: 'var(--giki-primary)' }} />
                Temporal Spending Patterns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {timePatterns.map((timePattern, index) => (
                  <div
                    key={index}
                    className="border border-gray-200 rounded-lg p-6"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-xl font-semibold text-gray-900">
                        {timePattern.period}
                      </h3>
                      <Badge className="text-purple-600 bg-purple-100">
                        Seasonality: {timePattern.seasonalIndex}x
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-gray-900">
                          {formatCurrency(timePattern.avgAmount)}
                        </div>
                        <div className="text-sm text-gray-600">Avg Amount</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-gray-900">
                          {timePattern.transactionCount}
                        </div>
                        <div className="text-sm text-gray-600">
                          Transactions
                        </div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-gray-900">
                          {timePattern.peakDay}
                        </div>
                        <div className="text-sm text-gray-600">Peak Day</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-gray-900">
                          {timePattern.peakTime}
                        </div>
                        <div className="text-sm text-gray-600">Peak Time</div>
                      </div>
                    </div>
                  </div>
                ))}

                <div className="bg-gradient-to-br from-brand-primary to-ai-dark-blue text-white p-6 rounded-lg">
                  <h4 className="text-xl font-semibold mb-3">
                    AI Pattern Insights
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-6 h-6 text-green-400" />
                      <span>Consistent weekly patterns detected</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <AlertCircle className="w-6 h-6 text-yellow-400" />
                      <span>Seasonal variations identified</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Zap className="w-6 h-6 text-blue-400" />
                      <span>Peak spending periods mapped</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Eye className="w-6 h-6 text-purple-400" />
                      <span>Behavioral patterns analyzed</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PatternRecognitionPage;
