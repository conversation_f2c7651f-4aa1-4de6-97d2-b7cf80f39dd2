/**
 * Agent Communication Hook
 * 
 * Provides a unified interface for agent interactions with WebSocket support
 */

import { useState, useCallback, useEffect } from 'react';
import { useWebSocketMessage, MessageType } from '@/shared/services/websocket/UnifiedWebSocketService.tsx';
import { agentsApi, type ChatResponse } from '@/shared/services/agentsApi';

interface AgentState {
  isConnected: boolean;
  isTyping: boolean;
  isProcessing: boolean;
  currentCommand?: string;
  lastResponse?: ChatResponse;
  error?: string;
}

interface UseAgentCommunicationReturn {
  state: AgentState;
  sendMessage: (message: string, context?: Record<string, any>) => Promise<void>;
  sendCommand: (command: string, parameters?: Record<string, any>) => Promise<void>;
  clearError: () => void;
  getStatus: () => Promise<any>;
  getCommands: () => Promise<any>;
}

export const useAgentCommunication = (): UseAgentCommunicationReturn => {
  const [state, setState] = useState<AgentState>({
    isConnected: false,
    isTyping: false,
    isProcessing: false,
  });

  // WebSocket event listeners
  useWebSocketMessage(MessageType.AGENT_TYPING, () => {
    setState(prev => ({ ...prev, isTyping: true }));
  });

  useWebSocketMessage(MessageType.AGENT_STATUS, (payload: any) => {
    if (payload.status === 'processing') {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        isTyping: false,
        currentCommand: payload.currentTask,
      }));
    } else if (payload.status === 'online') {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        currentCommand: undefined,
      }));
    }
  });

  useWebSocketMessage(MessageType.AGENT_MESSAGE, (payload: any) => {
    setState(prev => ({
      ...prev,
      isTyping: false,
      isProcessing: false,
      lastResponse: {
        success: true,
        message: payload.content || payload.message,
        data: payload.data,
      },
      error: undefined,
    }));
  });

  useWebSocketMessage(MessageType.SYSTEM_ERROR, (payload: any) => {
    setState(prev => ({
      ...prev,
      isTyping: false,
      isProcessing: false,
      error: payload.error || payload.message,
    }));
  });

  // Send natural language message
  const sendMessage = useCallback(async (message: string, context?: Record<string, any>) => {
    try {
      setState(prev => ({ ...prev, error: undefined }));
      
      const response = await agentsApi.sendNaturalLanguage(message, context);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to send message');
      }
      
      // Response will be handled by WebSocket listener
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error',
      }));
    }
  }, []);

  // Send specific command
  const sendCommand = useCallback(async (command: string, parameters?: Record<string, any>) => {
    try {
      setState(prev => ({ ...prev, error: undefined }));
      
      const response = await agentsApi.executeCommand(command, parameters);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to execute command');
      }
      
      // Response will be handled by WebSocket listener
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error',
      }));
    }
  }, []);

  // Clear error state
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: undefined }));
  }, []);

  // Get agent status
  const getStatus = useCallback(async () => {
    try {
      const status = await agentsApi.getStatus();
      setState(prev => ({ ...prev, isConnected: status.database_connected }));
      return status;
    } catch (error) {
      console.error('Error getting agent status:', error);
      setState(prev => ({ ...prev, isConnected: false }));
      throw error;
    }
  }, []);

  // Get available commands
  const getCommands = useCallback(async () => {
    try {
      return await agentsApi.getCommands();
    } catch (error) {
      console.error('Error getting agent commands:', error);
      throw error;
    }
  }, []);

  // Initialize agent status on mount
  useEffect(() => {
    getStatus().catch(console.error);
  }, [getStatus]);

  return {
    state,
    sendMessage,
    sendCommand,
    clearError,
    getStatus,
    getCommands,
  };
};