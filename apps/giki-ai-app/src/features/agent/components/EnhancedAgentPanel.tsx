/**
 * Enhanced Agent Panel with Real-time WebSocket Communication
 * 
 * Features:
 * - Real-time typing indicators
 * - WebSocket event handling for agent responses
 * - Live processing status updates
 * - Bidirectional communication
 * - Command suggestions and autocomplete
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  MessageCircle, 
  Send, 
  Loader2, 
  Zap, 
  Activity,
  AlertCircle,
  Bot
} from 'lucide-react';
import { useWebSocketMessage, MessageType, useUnifiedWebSocket } from '@/shared/services/websocket/UnifiedWebSocketService.tsx';
import { agentsApi, type ChatResponse } from '@/shared/services/agentsApi';
import { brandGradients } from '@/shared/utils/brandGradients';

interface Message {
  id: string;
  type: 'user' | 'agent' | 'system';
  content: string;
  timestamp: Date;
  data?: any;
  processing?: boolean;
}

interface ProcessingState {
  isProcessing: boolean;
  command?: string;
  stage?: string;
}

interface EnhancedAgentPanelProps {
  className?: string;
  initialMessage?: string;
  onAgentResponse?: (response: ChatResponse) => void;
}

export const EnhancedAgentPanel: React.FC<EnhancedAgentPanelProps> = ({
  className = '',
  initialMessage,
  onAgentResponse,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState(initialMessage || '');
  const [isTyping, setIsTyping] = useState(false);
  const [processingState, setProcessingState] = useState<ProcessingState>({
    isProcessing: false,
  });
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { isConnected: wsConnected } = useUnifiedWebSocket();

  // WebSocket event listeners for real-time agent communication
  useWebSocketMessage(MessageType.AGENT_TYPING, () => {
    setIsTyping(true);
    // Auto-hide typing indicator after 3 seconds
    setTimeout(() => setIsTyping(false), 3000);
  });

  useWebSocketMessage(MessageType.AGENT_STATUS, (payload: any) => {
    if (payload.status === 'processing_started') {
      setProcessingState({
        isProcessing: true,
        command: payload.command,
        stage: 'processing',
      });
      setIsTyping(false);
    } else if (payload.status === 'processing_completed') {
      setProcessingState({
        isProcessing: false,
      });
    }
  });

  useWebSocketMessage(MessageType.AGENT_MESSAGE, (payload: any) => {
    const agentMessage: Message = {
      id: Date.now().toString(),
      type: 'agent',
      content: payload.message,
      timestamp: new Date(),
      data: payload.data,
    };
    
    setMessages(prev => [...prev, agentMessage]);
    setIsTyping(false);
    setProcessingState({ isProcessing: false });
    
    // Notify parent component
    if (onAgentResponse) {
      onAgentResponse({
        success: true,
        message: payload.message,
        data: payload.data,
      });
    }
  });

  useWebSocketMessage(MessageType.SYSTEM_ERROR, (payload: any) => {
    const errorMessage: Message = {
      id: Date.now().toString(),
      type: 'system',
      content: `Error: ${payload.error}`,
      timestamp: new Date(),
    };
    
    setMessages(prev => [...prev, errorMessage]);
    setIsTyping(false);
    setProcessingState({ isProcessing: false });
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  // Handle message sending
  const handleSendMessage = useCallback(async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setShowSuggestions(false);
    setIsTyping(true);

    try {
      // Send message to agent API
      const response = await agentsApi.sendNaturalLanguage(inputMessage);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to send message');
      }
      
      // Note: Actual response will come via WebSocket
      // This is just for immediate feedback if WebSocket is down
      if (!wsConnected) {
        const agentMessage: Message = {
          id: Date.now().toString(),
          type: 'agent',
          content: response.message,
          timestamp: new Date(),
          data: response.data,
        };
        setMessages(prev => [...prev, agentMessage]);
        setIsTyping(false);
      }
      
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        id: Date.now().toString(),
        type: 'system',
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
      setIsTyping(false);
    }
  }, [inputMessage, wsConnected, onAgentResponse]);

  // Handle input change and suggestions
  const handleInputChange = useCallback(async (value: string) => {
    setInputMessage(value);
    
    if (value.length > 2) {
      try {
        const response = await agentsApi.getSuggestions(value);
        setSuggestions(response.suggestions);
        setShowSuggestions(response.suggestions.length > 0);
      } catch (error) {
        console.error('Error getting suggestions:', error);
      }
    } else {
      setShowSuggestions(false);
    }
  }, []);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((suggestion: string) => {
    setInputMessage(suggestion);
    setShowSuggestions(false);
    inputRef.current?.focus();
  }, []);

  // Handle key press
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  // Format timestamp
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className={`flex flex-col h-full bg-white border-l border-gray-200 ${className}`}>
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full flex items-center justify-center"
                 style={{ background: brandGradients.primary }}>
              <Bot className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">AI Assistant</h3>
              <p className="text-sm text-gray-500">
                {wsConnected ? 'Connected' : 'Offline'}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${wsConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <Activity className={`w-4 h-4 ${wsConnected ? 'text-green-500' : 'text-red-500'}`} />
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 && (
          <div className="text-center py-8">
            <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Start a conversation with your AI assistant</p>
            <p className="text-sm text-gray-400 mt-2">
              Try asking about transactions, reports, or use commands like /upload, /filter, /export
            </p>
          </div>
        )}

        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
              message.type === 'user'
                ? 'bg-blue-500 text-white'
                : message.type === 'agent'
                ? 'bg-gray-100 text-gray-900'
                : 'bg-yellow-50 text-yellow-800 border border-yellow-200'
            }`}>
              {message.type !== 'user' && (
                <div className="flex items-center space-x-2 mb-1">
                  {message.type === 'agent' ? (
                    <Bot className="w-4 h-4" />
                  ) : (
                    <AlertCircle className="w-4 h-4" />
                  )}
                  <span className="text-xs font-medium">
                    {message.type === 'agent' ? 'AI Assistant' : 'System'}
                  </span>
                </div>
              )}
              <p className="text-sm whitespace-pre-wrap">{message.content}</p>
              <p className="text-xs opacity-75 mt-1">{formatTime(message.timestamp)}</p>
            </div>
          </div>
        ))}

        {/* Typing Indicator */}
        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 px-4 py-2 rounded-lg">
              <div className="flex items-center space-x-2">
                <Bot className="w-4 h-4 text-gray-500" />
                <Loader2 className="w-4 h-4 animate-spin text-gray-500" />
                <span className="text-sm text-gray-500">AI is typing...</span>
              </div>
            </div>
          </div>
        )}

        {/* Processing Indicator */}
        {processingState.isProcessing && (
          <div className="flex justify-start">
            <div className="bg-blue-50 border border-blue-200 px-4 py-2 rounded-lg">
              <div className="flex items-center space-x-2">
                <Zap className="w-4 h-4 text-blue-500" />
                <span className="text-sm text-blue-700">
                  Processing {processingState.command || 'command'}...
                </span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="flex-shrink-0 p-4 border-t border-gray-200">
        {/* Suggestions */}
        {showSuggestions && suggestions.length > 0 && (
          <div className="mb-3 bg-gray-50 rounded-lg p-2 border border-gray-200">
            <div className="text-xs text-gray-500 mb-2">Suggestions:</div>
            <div className="space-y-1">
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionSelect(suggestion)}
                  className="w-full text-left px-2 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        )}

        <div className="flex space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={inputMessage}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type a message or command..."
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={processingState.isProcessing}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || processingState.isProcessing}
            className="px-4 py-2 text-white rounded-lg font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            style={{ 
              background: brandGradients.primary,
              boxShadow: brandGradients.boxShadow 
            }}
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default EnhancedAgentPanel;