/**
 * React hook for file processing with WebSocket integration
 * 
 * Provides real-time file processing updates and progress tracking
 * integrating with the WebSocket service for seamless user experience.
 */

import { useState, useEffect, useCallback } from 'react';
import { useWebSocketMessage, MessageType } from '@/shared/services/websocket/UnifiedWebSocketService.tsx';
import { ProcessingStage } from '../services/processingService';
import { getUploadStatus } from '../services/fileService';

export interface FileProcessingState {
  isProcessing: boolean;
  progress: number;
  currentStage: string;
  stages: ProcessingStage[];
  error: string | null;
  completed: boolean;
  results?: any;
}

export interface FileProcessingActions {
  startProcessing: (uploadId: string) => Promise<void>;
  resetProcessing: () => void;
  retryProcessing: () => void;
}

export interface UseFileProcessingReturn {
  state: FileProcessingState;
  actions: FileProcessingActions;
}

export const useFileProcessing = (uploadId?: string): UseFileProcessingReturn => {
  const [state, setState] = useState<FileProcessingState>({
    isProcessing: false,
    progress: 0,
    currentStage: '',
    stages: [],
    error: null,
    completed: false,
  });

  const [activeUploadId, setActiveUploadId] = useState<string | null>(uploadId || null);

  // WebSocket listeners for real-time updates
  useWebSocketMessage(MessageType.FILE_PROCESSING_STARTED, (payload: any) => {
    if (payload.upload_id === activeUploadId) {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        currentStage: 'File Processing Started',
        error: null,
        completed: false,
      }));
    }
  });

  useWebSocketMessage(MessageType.FILE_PROCESSING_PROGRESS, (payload: any) => {
    if (payload.upload_id === activeUploadId) {
      setState(prev => ({
        ...prev,
        progress: payload.progress * 100,
        currentStage: payload.stage || payload.message || 'Processing...',
      }));
    }
  });

  useWebSocketMessage(MessageType.FILE_PROCESSING_COMPLETED, (payload: any) => {
    if (payload.upload_id === activeUploadId) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        progress: 100,
        currentStage: 'Processing Complete',
        completed: true,
        results: payload.results,
      }));
    }
  });

  useWebSocketMessage(MessageType.FILE_PROCESSING_ERROR, (payload: any) => {
    if (payload.upload_id === activeUploadId) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: payload.error || 'Processing failed',
        currentStage: 'Error',
      }));
    }
  });

  useWebSocketMessage(MessageType.CATEGORIZATION_STARTED, (payload: any) => {
    if (payload.upload_id === activeUploadId) {
      setState(prev => ({
        ...prev,
        currentStage: 'AI Categorization Started',
      }));
    }
  });

  useWebSocketMessage(MessageType.CATEGORIZATION_PROGRESS, (payload: any) => {
    if (payload.upload_id === activeUploadId) {
      setState(prev => ({
        ...prev,
        progress: payload.progress * 100,
        currentStage: payload.message || 'Categorizing transactions...',
      }));
    }
  });

  useWebSocketMessage(MessageType.CATEGORIZATION_COMPLETED, (payload: any) => {
    if (payload.upload_id === activeUploadId) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        progress: 100,
        currentStage: 'Categorization Complete',
        completed: true,
        results: {
          ...prev.results,
          categorization: payload,
        },
      }));
    }
  });

  useWebSocketMessage(MessageType.CATEGORIZATION_ERROR, (payload: any) => {
    if (payload.upload_id === activeUploadId) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: payload.error || 'Categorization failed',
        currentStage: 'Categorization Error',
      }));
    }
  });

  // Start processing function
  const startProcessing = useCallback(async (uploadId: string) => {
    setActiveUploadId(uploadId);
    
    setState(prev => ({
      ...prev,
      isProcessing: true,
      progress: 0,
      currentStage: 'Initializing...',
      error: null,
      completed: false,
      results: undefined,
    }));

    try {
      // Check initial status
      const statusResult = await getUploadStatus(uploadId);
      
      if ('type' in statusResult) {
        // Handle API error
        setState(prev => ({
          ...prev,
          isProcessing: false,
          error: statusResult.message || 'Failed to get upload status',
          currentStage: 'Error',
        }));
        return;
      }

      // Update based on current status
      const { status, progress, message } = statusResult;
      
      setState(prev => ({
        ...prev,
        progress: progress || 0,
        currentStage: message || `Status: ${status}`,
        isProcessing: status === 'processing',
        completed: status === 'completed',
        error: status === 'failed' ? (statusResult.error || 'Processing failed') : null,
      }));

    } catch (error) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        currentStage: 'Error',
      }));
    }
  }, []);

  // Reset processing state
  const resetProcessing = useCallback(() => {
    setState({
      isProcessing: false,
      progress: 0,
      currentStage: '',
      stages: [],
      error: null,
      completed: false,
    });
    setActiveUploadId(null);
  }, []);

  // Retry processing
  const retryProcessing = useCallback(() => {
    if (activeUploadId) {
      startProcessing(activeUploadId);
    }
  }, [activeUploadId, startProcessing]);

  // Initialize processing if uploadId is provided
  useEffect(() => {
    if (uploadId && uploadId !== activeUploadId) {
      startProcessing(uploadId);
    }
  }, [uploadId, activeUploadId, startProcessing]);

  return {
    state,
    actions: {
      startProcessing,
      resetProcessing,
      retryProcessing,
    },
  };
};

export default useFileProcessing;