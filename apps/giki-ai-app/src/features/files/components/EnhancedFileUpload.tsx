/**
 * Enhanced File Upload Component with Drag & Drop and Progress Tracking
 * 
 * Features:
 * - Drag and drop file upload
 * - Real-time upload progress tracking
 * - WebSocket integration for processing updates
 * - Multiple file support
 * - File validation and error handling
 * - Retry functionality
 * - Visual feedback and animations
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
// Removed Lucide imports - replaced with geometric icons
// Upload → ↑, File → □, CheckCircle → ✓, AlertCircle → ⚠, Play → ▶
import { useFileUpload } from '../hooks/useFileUpload';
import { useWebSocketMessage, MessageType } from '@/shared/services/websocket/UnifiedWebSocketService.tsx';
import { brandGradients } from '@/shared/utils/brandGradients';
import { spacing } from '@/shared/utils/spacing';
import { FileItem } from './FileItem';

interface UploadFile {
  id: string;
  file: File;
  uploadId?: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  transactionCount?: number;
  categorizedCount?: number;
}

interface EnhancedFileUploadProps {
  onUploadComplete?: (uploadId: string, results: any) => void;
  onUploadError?: (error: string) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
  maxFileSize?: number; // in MB
  className?: string;
}

const EnhancedFileUploadComponent: React.FC<EnhancedFileUploadProps> = ({
  onUploadComplete,
  onUploadError,
  maxFiles = 5,
  acceptedTypes = ['.csv', '.xlsx', '.xls'],
  maxFileSize = 50,
  className = '',
}) => {
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [isDragActive, setIsDragActive] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { uploadFile, isUploading } = useFileUpload();

  // WebSocket listeners for real-time updates
  useWebSocketMessage(MessageType.FILE_UPLOADED, (payload: any) => {
    const fileId = payload.file_id;
    setFiles(prev => prev.map(f => 
      f.uploadId === fileId ? { ...f, status: 'processing' as const, progress: 100 } : f
    ));
  });

  useWebSocketMessage(MessageType.FILE_PROCESSING_PROGRESS, (payload: any) => {
    const uploadId = payload.upload_id;
    setFiles(prev => prev.map(f => 
      f.uploadId === uploadId ? { ...f, progress: payload.progress * 100 } : f
    ));
  });

  useWebSocketMessage(MessageType.FILE_PROCESSING_COMPLETED, (payload: any) => {
    const uploadId = payload.upload_id;
    setFiles(prev => prev.map(f => 
      f.uploadId === uploadId ? { 
        ...f, 
        status: 'completed' as const, 
        progress: 100,
        transactionCount: payload.results?.transaction_count,
        categorizedCount: payload.results?.categorized_count
      } : f
    ));

    // Notify parent component
    if (onUploadComplete) {
      onUploadComplete(uploadId, payload.results);
    }
  });

  useWebSocketMessage(MessageType.FILE_PROCESSING_ERROR, (payload: any) => {
    const uploadId = payload.upload_id;
    setFiles(prev => prev.map(f => 
      f.uploadId === uploadId ? { 
        ...f, 
        status: 'error' as const, 
        error: payload.error 
      } : f
    ));

    if (onUploadError) {
      onUploadError(payload.error);
    }
  });

  // File validation
  const validateFile = useCallback((file: File): string | null => {
    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!acceptedTypes.includes(fileExtension)) {
      return `File type ${fileExtension} is not supported. Accepted types: ${acceptedTypes.join(', ')}`;
    }

    // Check file size (convert MB to bytes)
    const maxSizeBytes = maxFileSize * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return `File size exceeds ${maxFileSize}MB limit`;
    }

    return null;
  }, [acceptedTypes, maxFileSize]);

  // Handle file selection
  const handleFileSelect = useCallback((selectedFiles: FileList) => {
    if (files.length + selectedFiles.length > maxFiles) {
      if (onUploadError) {
        onUploadError(`Maximum ${maxFiles} files allowed`);
      }
      return;
    }

    const newFiles: UploadFile[] = [];
    
    for (let i = 0; i < selectedFiles.length; i++) {
      const file = selectedFiles[i];
      const validationError = validateFile(file);
      
      if (validationError) {
        if (onUploadError) {
          onUploadError(validationError);
        }
        continue;
      }

      newFiles.push({
        id: `${Date.now()}-${i}`,
        file,
        status: 'pending',
        progress: 0,
      });
    }

    setFiles(prev => [...prev, ...newFiles]);
  }, [files.length, maxFiles, validateFile, onUploadError]);

  // Upload a single file
  const uploadSingleFile = useCallback(async (fileToUpload: UploadFile) => {
    setFiles(prev => prev.map(f => 
      f.id === fileToUpload.id ? { ...f, status: 'uploading' as const } : f
    ));

    try {
      const response = await uploadFile(fileToUpload.file);

      if (response.success && response.uploadId) {
        setFiles(prev => prev.map(f => 
          f.id === fileToUpload.id ? { 
            ...f, 
            uploadId: response.uploadId,
            status: 'processing' as const,
            progress: 100
          } : f
        ));
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setFiles(prev => prev.map(f => 
        f.id === fileToUpload.id ? { 
          ...f, 
          status: 'error' as const, 
          error: errorMessage 
        } : f
      ));

      if (onUploadError) {
        onUploadError(errorMessage);
      }
    }
  }, [uploadFile, onUploadError]);

  // Start processing all pending files
  const startProcessing = useCallback(async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    const pendingFiles = files.filter(f => f.status === 'pending');

    for (const file of pendingFiles) {
      await uploadSingleFile(file);
    }

    setIsProcessing(false);
  }, [files, isProcessing, uploadSingleFile]);

  // Remove a file
  const removeFile = useCallback((fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  }, []);

  // Retry failed upload
  const retryUpload = useCallback((fileId: string) => {
    const file = files.find(f => f.id === fileId);
    if (file) {
      uploadSingleFile(file);
    }
  }, [files, uploadSingleFile]);

  // Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
    
    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      handleFileSelect(droppedFiles);
    }
  }, [handleFileSelect]);

  // File input change handler
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      handleFileSelect(selectedFiles);
    }
  }, [handleFileSelect]);

  // Click to open file dialog
  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // Auto-start processing when files are added
  useEffect(() => {
    const pendingFiles = files.filter(f => f.status === 'pending');
    if (pendingFiles.length > 0 && !isProcessing) {
      startProcessing();
    }
  }, [files, isProcessing, startProcessing]);

  // Get status color - memoized to prevent re-creation
  const getStatusColor = React.useCallback((status: UploadFile['status']) => {
    switch (status) {
      case 'pending': return 'text-gray-500';
      case 'uploading': return 'text-blue-500';
      case 'processing': return 'text-orange-500';
      case 'completed': return 'text-green-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  }, []);

  // Get status icon - memoized to prevent re-creation
  const getStatusIcon = React.useCallback((file: UploadFile) => {
    switch (file.status) {
      case 'pending':
        return <span className="w-4 h-4 text-gray-500 text-base font-bold flex items-center justify-center">□</span>;
      case 'uploading':
      case 'processing':
        return (
          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        );
      case 'completed':
        return <span className="w-4 h-4 text-green-500 text-base font-bold flex items-center justify-center">✓</span>;
      case 'error':
        return <span className="w-4 h-4 text-red-500 text-base font-bold flex items-center justify-center">⚠</span>;
      default:
        return <span className="w-4 h-4 text-gray-500 text-base font-bold flex items-center justify-center">□</span>;
    }
  }, []);

  return (
    <div className={`w-full ${className}`}>
      {/* Upload Drop Zone */}
      <div
        className={`
          relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200
          ${isDragActive 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileInputChange}
          className="hidden"
        />

        <div className="flex flex-col items-center space-y-4">
          <div className={`
            w-16 h-16 rounded-full flex items-center justify-center transition-colors
            ${isDragActive ? 'bg-blue-100' : 'bg-gray-100'}
          `}>
            <span className={`w-8 h-8 text-3xl font-bold flex items-center justify-center ${isDragActive ? 'text-blue-500' : 'text-gray-400'}`}>↑</span>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {isDragActive ? 'Drop files here' : 'Upload your files'}
            </h3>
            <p className="text-gray-600 mb-4">
              Drag and drop your files here, or{' '}
              <button
                onClick={openFileDialog}
                className="text-blue-600 hover:text-blue-700 underline"
              >
                browse
              </button>
            </p>
            <p className="text-sm text-gray-500">
              Supported formats: {acceptedTypes.join(', ')} • Max size: {maxFileSize}MB • Max files: {maxFiles}
            </p>
          </div>

          {files.length === 0 && (
            <button
              onClick={openFileDialog}
              className="text-white rounded-lg font-semibold transition-all inline-flex items-center"
              style={{
                background: brandGradients.primary,
                boxShadow: brandGradients.boxShadow,
                padding: `${spacing[3]} ${spacing[6]}`,
              }}
            >
              <span className="w-4 h-4 mr-2 text-base font-bold flex items-center justify-center">↑</span>
              Choose Files
            </button>
          )}
        </div>
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="mt-6 space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">
              Files ({files.length}/{maxFiles})
            </h4>
            {files.some(f => f.status === 'pending') && (
              <button
                onClick={startProcessing}
                disabled={isProcessing}
                className="text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50 flex items-center gap-1"
              >
                <span className="w-3 h-3 text-sm font-bold flex items-center justify-center">▶</span>
                Process All
              </button>
            )}
          </div>

          {files.map((file) => (
            <FileItem
              key={file.id}
              file={file}
              onRemove={removeFile}
              onRetry={retryUpload}
              getStatusIcon={getStatusIcon}
              getStatusColor={getStatusColor}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
// Only re-render when props change (callbacks, maxFiles, acceptedTypes, etc.)
export const EnhancedFileUpload = React.memo(EnhancedFileUploadComponent);

export default React.memo(EnhancedFileUploadComponent);