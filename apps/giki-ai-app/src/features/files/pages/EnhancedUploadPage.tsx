/**
 * Enhanced Upload Page - Modern file upload interface with real-time progress tracking
 * Features: Drag & drop, WebSocket progress, multiple files, batch processing, notifications
 */
import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { apiClient } from '@/shared/services/api/apiClient';
import { 
  Upload, 
  X, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  FileText,
  Zap,
  Activity,
  Users,
  TrendingUp,
  FileCheck,
  AlertTriangle
} from 'lucide-react';

// Types
interface FileUploadProgress {
  id: string;
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
  uploadId?: string;
  results?: ProcessingResults;
}

interface ProcessingResults {
  uploadId: string;
  fileName: string;
  totalRows: number;
  successfulRows: number;
  failedRows: number;
  dataQualityScore: number;
  processingTime: number;
  downloadUrls: {
    xlsx: string;
    csv: string;
    pdf: string;
  };
}

interface WebSocketMessage {
  type: 'progress' | 'completed' | 'error' | 'status';
  uploadId: string;
  data: any;
}

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  duration?: number;
}

const EnhancedUploadPage: React.FC = () => {
  const navigate = useNavigate();
  const [files, setFiles] = useState<FileUploadProgress[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [wsStatus, setWsStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const wsRef = useRef<WebSocket | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dragCounterRef = useRef(0);

  // WebSocket connection management
  useEffect(() => {
    const connectWebSocket = () => {
      const token = localStorage.getItem('access_token');
      const baseUrl = process.env.NODE_ENV === 'development' 
        ? 'localhost:8000'
        : 'giki-ai-api-273348121056.us-central1.run.app';
      const protocol = process.env.NODE_ENV === 'development' ? 'ws' : 'wss';
      const wsUrl = `${protocol}://${baseUrl}/ws/files?token=${token}`;
      
      setWsStatus('connecting');
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        setWsStatus('connected');
        addNotification('success', 'Connected', 'Real-time progress tracking enabled');
      };
      
      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          // Handle WebSocket message format from backend
          if (message.event_type && message.data) {
            handleWebSocketMessage({
              type: message.event_type,
              uploadId: message.data.upload_id,
              data: message.data
            });
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      
      ws.onclose = () => {
        setWsStatus('disconnected');
        // Attempt to reconnect after 3 seconds
        setTimeout(connectWebSocket, 3000);
      };
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setWsStatus('disconnected');
        addNotification('error', 'Connection Error', 'Real-time progress tracking unavailable');
      };
      
      wsRef.current = ws;
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  // Handle WebSocket messages
  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    const { type, uploadId, data } = message;
    
    setFiles(prev => prev.map(file => {
      if (file.uploadId === uploadId) {
        switch (type) {
          case 'file.processing_progress':
            return { ...file, progress: data.progress || 0 };
          case 'file.processing_completed':
            return { 
              ...file, 
              status: 'completed',
              progress: 100,
              results: {
                uploadId: uploadId,
                fileName: file.file.name,
                totalRows: data.total_processed || 0,
                successfulRows: data.categorized || 0,
                failedRows: 0,
                dataQualityScore: data.accuracy || 87,
                processingTime: 0,
                downloadUrls: {
                  xlsx: `/api/v1/files/${uploadId}/export/xlsx`,
                  csv: `/api/v1/files/${uploadId}/export/csv`,
                  pdf: `/api/v1/files/${uploadId}/export/pdf`
                }
              }
            };
          case 'file.processing_error':
            return { 
              ...file, 
              status: 'error',
              error: data.error
            };
          default:
            return file;
        }
      }
      return file;
    }));
  }, []);

  // Notification management
  const addNotification = useCallback((type: Notification['type'], title: string, message: string, duration = 5000) => {
    const notification: Notification = {
      id: Date.now().toString(),
      type,
      title,
      message,
      timestamp: new Date(),
      duration
    };
    
    setNotifications(prev => [...prev, notification]);
    
    if (duration > 0) {
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, duration);
    }
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  // File handling
  const handleFileSelect = useCallback((selectedFiles: FileList | null) => {
    if (!selectedFiles) return;
    
    const newFiles: FileUploadProgress[] = Array.from(selectedFiles).map(file => ({
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      file,
      progress: 0,
      status: 'pending'
    }));
    
    setFiles(prev => [...prev, ...newFiles]);
    
    // Auto-start upload for new files
    newFiles.forEach(fileProgress => {
      startUpload(fileProgress);
    });
  }, []);

  const startUpload = useCallback(async (fileProgress: FileUploadProgress) => {
    try {
      setFiles(prev => prev.map(f => 
        f.id === fileProgress.id 
          ? { ...f, status: 'uploading', progress: 0 }
          : f
      ));

      const formData = new FormData();
      formData.append('file', fileProgress.file);
      formData.append('currency', 'USD');

      const response = await apiClient.postFormData('/files/upload', formData);

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      // Handle multiple upload response - get the first upload from the response
      const uploadId = result.uploads?.[0]?.upload_id || result.upload_id;
      
      setFiles(prev => prev.map(f => 
        f.id === fileProgress.id 
          ? { 
              ...f, 
              status: 'processing',
              progress: 25,
              uploadId: uploadId
            }
          : f
      ));

      addNotification('success', 'Upload Started', `${fileProgress.file.name} is being processed`);
      
    } catch (error) {
      setFiles(prev => prev.map(f => 
        f.id === fileProgress.id 
          ? { 
              ...f, 
              status: 'error',
              error: error instanceof Error ? error.message : 'Upload failed'
            }
          : f
      ));
      
      addNotification('error', 'Upload Failed', `${fileProgress.file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, []);

  const retryUpload = useCallback((fileProgress: FileUploadProgress) => {
    setFiles(prev => prev.map(f => 
      f.id === fileProgress.id 
        ? { ...f, status: 'pending', progress: 0, error: undefined }
        : f
    ));
    
    startUpload(fileProgress);
  }, [startUpload]);

  const removeFile = useCallback((fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  }, []);

  const clearAllFiles = useCallback(() => {
    setFiles([]);
  }, []);

  // Drag and drop handling
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current++;
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current--;
    if (dragCounterRef.current === 0) {
      setIsDragging(false);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current = 0;
    setIsDragging(false);
    
    const droppedFiles = e.dataTransfer.files;
    handleFileSelect(droppedFiles);
  }, [handleFileSelect]);

  // Navigation handlers
  const handleViewResults = useCallback((uploadId: string) => {
    navigate(`/results/${uploadId}`);
  }, [navigate]);

  const handleViewTransactions = useCallback((uploadId: string) => {
    navigate(`/transactions?uploadId=${uploadId}`);
  }, [navigate]);

  // Stats calculation
  const stats = {
    total: files.length,
    completed: files.filter(f => f.status === 'completed').length,
    processing: files.filter(f => f.status === 'processing' || f.status === 'uploading').length,
    errors: files.filter(f => f.status === 'error').length,
    avgQuality: files.filter(f => f.results?.dataQualityScore).reduce((acc, f) => acc + (f.results?.dataQualityScore || 0), 0) / files.filter(f => f.results?.dataQualityScore).length || 0
  };

  const getStatusColor = (status: FileUploadProgress['status']) => {
    switch (status) {
      case 'completed': return 'text-success';
      case 'processing': case 'uploading': return 'text-blue-600';
      case 'error': return 'text-error';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: FileUploadProgress['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-5 w-5" />;
      case 'processing': case 'uploading': return <RefreshCw className="h-5 w-5 animate-spin" />;
      case 'error': return <AlertCircle className="h-5 w-5" />;
      default: return <FileText className="h-5 w-5" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <img 
                src="/images/giki-logo.svg" 
                alt="Giki.ai" 
                className="h-8 w-auto"
              />
              <h1 className="text-2xl font-bold text-gray-900">Enhanced Upload</h1>
            </div>
            
            {/* WebSocket Status */}
            <div className="flex items-center space-x-2">
              <Activity className={`h-4 w-4 ${wsStatus === 'connected' ? 'text-green-500' : wsStatus === 'connecting' ? 'text-yellow-500' : 'text-red-500'}`} />
              <span className="text-sm text-gray-600">
                {wsStatus === 'connected' ? 'Live' : wsStatus === 'connecting' ? 'Connecting...' : 'Offline'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {notifications.map(notification => (
          <div
            key={notification.id}
            className={`max-w-sm rounded-lg shadow-lg p-4 ${
              notification.type === 'success' ? 'bg-green-50 border-green-200' :
              notification.type === 'error' ? 'bg-red-50 border-red-200' :
              notification.type === 'warning' ? 'bg-yellow-50 border-yellow-200' :
              'bg-blue-50 border-blue-200'
            } border`}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0">
                {notification.type === 'success' && <CheckCircle className="h-5 w-5 text-green-400" />}
                {notification.type === 'error' && <AlertCircle className="h-5 w-5 text-red-400" />}
                {notification.type === 'warning' && <AlertTriangle className="h-5 w-5 text-yellow-400" />}
                {notification.type === 'info' && <Activity className="h-5 w-5 text-blue-400" />}
              </div>
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-gray-900">{notification.title}</p>
                <p className="text-sm text-gray-500 mt-1">{notification.message}</p>
              </div>
              <button
                onClick={() => removeNotification(notification.id)}
                className="ml-4 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Upload Area */}
          <div className="lg:col-span-2 space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-4 gap-4">
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-gray-400" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">{stats.total}</p>
                    <p className="text-xs text-gray-500">Total Files</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">{stats.completed}</p>
                    <p className="text-xs text-gray-500">Completed</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center">
                  <RefreshCw className="h-5 w-5 text-blue-400" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">{stats.processing}</p>
                    <p className="text-xs text-gray-500">Processing</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center">
                  <TrendingUp className="h-5 w-5 text-indigo-400" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">{stats.avgQuality.toFixed(1)}%</p>
                    <p className="text-xs text-gray-500">Avg Quality</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Drop Zone */}
            <div
              className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                isDragging 
                  ? 'border-brand-primary bg-brand-primary/5' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              <Upload className="mx-auto h-12 w-12 text-gray-400" />
              <div className="mt-4">
                <p className="text-lg font-medium text-gray-900">
                  {isDragging ? 'Drop files here' : 'Drag and drop files here'}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  or{' '}
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="text-brand-primary hover:text-[#1D372E] font-medium"
                  >
                    browse to upload
                  </button>
                </p>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept=".csv,.xlsx,.xls"
                onChange={(e) => handleFileSelect(e.target.files)}
                className="hidden"
              />
            </div>

            {/* File List */}
            {files.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm">
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-900">Upload Progress</h3>
                    <button
                      onClick={clearAllFiles}
                      className="text-sm text-gray-500 hover:text-gray-700"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
                <div className="divide-y divide-gray-200">
                  {files.map(fileProgress => (
                    <div key={fileProgress.id} className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={getStatusColor(fileProgress.status)}>
                            {getStatusIcon(fileProgress.status)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {fileProgress.file.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {formatFileSize(fileProgress.file.size)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {fileProgress.status === 'error' && (
                            <button
                              onClick={() => retryUpload(fileProgress)}
                              className="text-sm text-brand-primary hover:text-[#1D372E] font-medium"
                            >
                              Retry
                            </button>
                          )}
                          {fileProgress.status === 'completed' && fileProgress.uploadId && (
                            <div className="flex space-x-2">
                              <button
                                onClick={() => handleViewResults(fileProgress.uploadId!)}
                                className="text-sm text-brand-primary hover:text-[#1D372E] font-medium"
                              >
                                View Results
                              </button>
                              <button
                                onClick={() => handleViewTransactions(fileProgress.uploadId!)}
                                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                              >
                                View Transactions
                              </button>
                            </div>
                          )}
                          <button
                            onClick={() => removeFile(fileProgress.id)}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      
                      {/* Progress Bar */}
                      <div className="mt-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">
                            {fileProgress.status === 'completed' ? 'Completed' :
                             fileProgress.status === 'error' ? 'Error' :
                             fileProgress.status === 'processing' ? 'Processing...' :
                             fileProgress.status === 'uploading' ? 'Uploading...' :
                             'Pending'}
                          </span>
                          <span className="text-gray-500">{fileProgress.progress}%</span>
                        </div>
                        <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              fileProgress.status === 'completed' ? 'bg-green-500' :
                              fileProgress.status === 'error' ? 'bg-red-500' :
                              'bg-brand-primary'
                            }`}
                            style={{ width: `${fileProgress.progress}%` }}
                          />
                        </div>
                      </div>

                      {/* Error Message */}
                      {fileProgress.error && (
                        <div className="mt-2 text-sm text-error">
                          {fileProgress.error}
                        </div>
                      )}

                      {/* Results Summary */}
                      {fileProgress.results && (
                        <div className="mt-4 bg-gray-50 rounded-lg p-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Processing Results</h4>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-gray-500">Total Rows:</span>
                              <span className="ml-2 font-medium">{fileProgress.results.totalRows}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Success Rate:</span>
                              <span className="ml-2 font-medium">
                                {((fileProgress.results.successfulRows / fileProgress.results.totalRows) * 100).toFixed(1)}%
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-500">Quality Score:</span>
                              <span className="ml-2 font-medium">{fileProgress.results.dataQualityScore}%</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Processing Time:</span>
                              <span className="ml-2 font-medium">{fileProgress.results.processingTime}s</span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* What happens next */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">What happens next?</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-brand-primary rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">1</span>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Upload & Analyze</h4>
                    <p className="text-sm text-gray-600">Your files are uploaded and analyzed for structure and content.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-brand-primary rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">2</span>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">AI Processing</h4>
                    <p className="text-sm text-gray-600">Our AI categorizes transactions and generates insights.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-brand-primary rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">3</span>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Review & Export</h4>
                    <p className="text-sm text-gray-600">Review categorized data and export in your preferred format.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Enhanced Features</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Zap className="h-5 w-5 text-brand-primary" />
                  <span className="text-sm text-gray-600">Real-time progress tracking</span>
                </div>
                <div className="flex items-center space-x-3">
                  <FileCheck className="h-5 w-5 text-brand-primary" />
                  <span className="text-sm text-gray-600">Batch file processing</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Activity className="h-5 w-5 text-brand-primary" />
                  <span className="text-sm text-gray-600">Live WebSocket updates</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Users className="h-5 w-5 text-brand-primary" />
                  <span className="text-sm text-gray-600">Multi-user support</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedUploadPage;