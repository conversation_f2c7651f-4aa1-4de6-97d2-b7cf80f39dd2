/**
 * Upload Page - Matching approved mockup design
 * Following: docs/design-system/page-mockups/100-simplified-onboarding.html
 */
import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/shared/hooks/useUnifiedServices';
import { EnhancedFileUpload } from '../components/EnhancedFileUpload';
import { confirmColumnMapping } from '../services/uploadService';
import { getSchemaInterpretation } from '../services/filesApi';
import { uploadFile } from '../services/fileService';
import { ApiError } from '@/shared/utils/errorHandling';
import { brandGradients } from '@/shared/utils/brandGradients';
import { spacing, layoutSpacing } from '@/shared/utils/spacing';
import { Loader2 } from 'lucide-react';
import '@/styles/layout-variables.css';

interface UploadedFile {
  id: string;
  uploadId?: string;
  name: string;
  size: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  transactionCount?: number;
  errorMessage?: string;
}

// Type guard for API error responses
function isApiError(response: unknown): response is ApiError {
  return response && typeof response === 'object' && 'error' in response;
}

export const UploadPage: React.FC = React.memo(() => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isDragActive, setIsDragActive] = useState(false);

  // Enhanced file upload callbacks
  const handleUploadComplete = useCallback((uploadId: string, results: any) => {
    console.log('Upload completed:', uploadId, results);
    // Navigate to processing page or update local state
    if (results?.transaction_count) {
      navigate(`/processing/${uploadId}`);
    }
  }, [navigate]);

  const handleUploadError = useCallback((error: string) => {
    console.error('Upload error:', error);
    // Handle error state - the EnhancedFileUpload component will show the error
  }, []);

  const handleFileUpload = useCallback(
    async (selectedFiles: FileList) => {
      if (selectedFiles.length === 0) return;

      const file = selectedFiles[0]; // Process first file
      const fileId = Math.random().toString(36).substr(2, 9);
      
      const newFile: UploadedFile = {
        id: fileId,
        name: file.name,
        size: file.size,
        status: 'uploading',
        progress: 0,
      };

      setFiles([newFile]);

      try {
        // Upload file
        const response = await uploadFile(file, 'USD');

        if (response.success && response.data && response.uploadId) {
          // Update with upload ID
          setFiles((prev) =>
            prev.map((f) =>
              f.id === fileId
                ? {
                    ...f,
                    uploadId: response.uploadId,
                    status: 'processing' as const,
                    progress: 25,
                  }
                : f,
            ),
          );

          // Get schema interpretation using the correct API
          const schemaResult = await getSchemaInterpretation(response.uploadId);

          if (isApiError(schemaResult)) {
            throw new Error(schemaResult.message || 'Schema detection failed');
          }

          setFiles((prev) =>
            prev.map((f) =>
              f.id === fileId
                ? {
                    ...f,
                    progress: 50,
                  }
                : f,
            ),
          );

          // Check overall confidence from the API response
          const avgConfidence = schemaResult.overall_confidence || 0;

          // Lower confidence threshold to allow testing
          if (avgConfidence > 0.5) {
            // Convert column_mappings array to Record format expected by confirmColumnMapping
            const mappingRecord: Record<string, string> = {};
            schemaResult.column_mappings.forEach(mapping => {
              mappingRecord[mapping.original_name] = mapping.mapped_field;
            });

            const mappingResult = await confirmColumnMapping(
              response.uploadId,
              mappingRecord,
            );

            if (isApiError(mappingResult)) {
              throw new Error(mappingResult.message || 'Mapping confirmation failed');
            }

            setFiles((prev) =>
              prev.map((f) =>
                f.id === fileId
                  ? { ...f, progress: 75 }
                  : f,
              ),
            );

            // Navigate to processing page immediately to show live updates
            navigate('/processing');
          } else {
            throw new Error('Low confidence schema detection - manual review required');
          }
        } else {
          throw new Error('Upload failed');
        }
      } catch (error) {
        setFiles((prev) =>
          prev.map((f) =>
            f.id === fileId
              ? {
                  ...f,
                  status: 'error' as const,
                  errorMessage: error instanceof Error ? error.message : 'Upload failed',
                }
              : f,
          ),
        );
      }
    },
    [uploadFile, navigate],
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragActive(false);

      const droppedFiles = e.dataTransfer.files;
      if (droppedFiles.length > 0) {
        handleFileUpload(droppedFiles).catch((error) => {
          console.error('File upload failed:', error);
        });
      }
    },
    [handleFileUpload],
  );

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const selectedFiles = e.target.files;
      if (selectedFiles && selectedFiles.length > 0) {
        handleFileUpload(selectedFiles).catch((error) => {
          console.error('File upload failed:', error);
        });
      }
    },
    [handleFileUpload],
  );

  return (
    <div className="bg-white">
      {/* Main Content - Works within EnhancedAppLayout 3-panel system */}
      <div className="max-w-[1200px] mx-auto p-page">
        {/* Upload Content - Professional 2-column layout within the main content area */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
            {/* Left Column - Process Info */}
            <section>
              <h1 className="text-6xl font-extrabold text-gray-900 leading-tight" style={{ marginBottom: spacing[6] }}>
                Your Statements<br/>
                Become <span className="text-giki-primary">Organized Books</span>
              </h1>

              <p className="text-2xl text-gray-600 leading-relaxed" style={{ marginBottom: spacing[10] }}>
                Upload your bank or credit card statements. We'll categorize every transaction.
              </p>

              {/* Process Preview - Matching mockup gradient style */}
              <div className="text-white rounded-2xl shadow-lg" style={{ 
                padding: spacing[8], 
                background: brandGradients.primary,
                marginBottom: spacing[10],
                boxShadow: '0 8px 32px rgba(41, 83, 67, 0.2)'
              }}>
                <div className="flex items-center justify-around">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl mx-auto border-2 border-white/30" style={{ marginBottom: spacing[3] }}>
                      1
                    </div>
                    <div className="font-semibold text-base">Upload</div>
                    <div className="text-sm opacity-80" style={{ marginTop: spacing[1] }}>Your statements</div>
                  </div>
                  <div className="text-2xl opacity-60">→</div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl mx-auto border-2 border-white/30" style={{ marginBottom: spacing[3] }}>
                      2
                    </div>
                    <div className="font-semibold text-base">AI Categorizes</div>
                    <div className="text-sm opacity-80" style={{ marginTop: spacing[1] }}>Every transaction</div>
                  </div>
                  <div className="text-2xl opacity-60">→</div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl mx-auto border-2 border-white/30" style={{ marginBottom: spacing[3] }}>
                      3
                    </div>
                    <div className="font-semibold text-base">Export</div>
                    <div className="text-sm opacity-80" style={{ marginTop: spacing[1] }}>To accounting software</div>
                  </div>
                </div>
              </div>

              {/* Data Security & Formats */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Security */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Your data is secure</h3>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <span style={{ color: 'var(--giki-primary)' }}>□</span>
                      <span className="text-gray-600">Bank-level AES-256 encryption</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span style={{ color: 'var(--giki-primary)' }}>□</span>
                      <span className="text-gray-600">Files deleted after processing</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span style={{ color: 'var(--giki-primary)' }}>□</span>
                      <span className="text-gray-600">Never shared or used for AI training</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span style={{ color: 'var(--giki-primary)' }}>□</span>
                      <span className="text-gray-600">Export or delete anytime</span>
                    </li>
                  </ul>
                </div>

                {/* Formats */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Supported formats</h3>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center gap-2 text-gray-600">
                      <span style={{ color: 'var(--giki-primary)' }}>⚬</span>
                      <span>Excel (.xlsx, .xls)</span>
                    </li>
                    <li className="flex items-center gap-2 text-gray-600">
                      <span style={{ color: 'var(--giki-primary)' }}>⚬</span>
                      <span>CSV files</span>
                    </li>
                    <li className="flex items-center gap-2 text-gray-600">
                      <span style={{ color: 'var(--giki-primary)' }}>⚬</span>
                      <span>PDF statements</span>
                    </li>
                    <li className="flex items-center gap-2 text-gray-600">
                      <span style={{ color: 'var(--giki-primary)' }}>⚬</span>
                      <span>OFX/QBO files</span>
                    </li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Right Column - Upload Panel */}
            <section className="bg-white border border-border rounded-[20px] shadow-[0_12px_40px_rgba(0,0,0,0.08)]" style={{ 
              padding: layoutSpacing.sectionGap
            }}>
              <div className="text-center" style={{ marginBottom: spacing[6] }}>
                <h2 className="text-2xl font-bold text-gray-900" style={{ marginBottom: spacing[1] }}>Start Your Upload</h2>
                <p className="text-gray-600 text-sm">Get categorized transactions in minutes</p>
              </div>

              {/* What Happens Next - Compact */}
              <div className="bg-background border border-border rounded-xl" style={{ 
                padding: spacing[4], 
                marginBottom: spacing[4] 
              }}>
                <h3 className="font-semibold text-gray-900 text-sm" style={{ marginBottom: spacing[2] }}>What happens next:</h3>
                <ul className="space-y-1">
                  <li className="flex items-center text-gray-600 text-xs" style={{ gap: spacing[2] }}>
                    <span style={{ color: 'var(--giki-primary)' }}>⚬</span>
                    <span>AI extracts all transactions</span>
                  </li>
                  <li className="flex items-center text-gray-600 text-xs" style={{ gap: spacing[2] }}>
                    <span style={{ color: 'var(--giki-primary)' }}>⚬</span>
                    <span>Intelligent categorization</span>
                  </li>
                  <li className="flex items-center text-gray-600 text-xs" style={{ gap: spacing[2] }}>
                    <span style={{ color: 'var(--giki-primary)' }}>⚬</span>
                    <span>Review & adjust as needed</span>
                  </li>
                  <li className="flex items-center text-gray-600 text-xs" style={{ gap: spacing[2] }}>
                    <span style={{ color: 'var(--giki-primary)' }}>⚬</span>
                    <span>Export to any software</span>
                  </li>
                </ul>
              </div>

              {/* Upload Status or Upload Zone */}
              {files.length > 0 && files[0].status !== 'error' ? (
                <div className="mb-6">
                  {files[0].status === 'uploading' && (
                    <div className="text-center py-16">
                      <div className="w-full max-w-[400px] mx-auto">
                        <div className="bg-border h-2 rounded-full overflow-hidden mb-4">
                          <div 
                            className="bg-gradient-to-r from-giki-primary to-giki-dark-blue h-full transition-all duration-300"
                            style={{ width: `${files[0].progress}%` }}
                          />
                        </div>
                        <p className="text-gray-900 font-semibold">Uploading {files[0].name}...</p>
                        <p className="text-gray-600 text-sm">{Math.round(files[0].progress)}% complete</p>
                      </div>
                    </div>
                  )}
                  
                  {files[0].status === 'processing' && (
                    <div className="text-center py-16">
                      <Loader2 className="w-16 h-16 mx-auto mb-4 text-giki-primary animate-spin" />
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">Processing Your File</h3>
                      <p className="text-gray-600">AI is categorizing {files[0].transactionCount || 'your'} transactions...</p>
                    </div>
                  )}

                  {files[0].status === 'completed' && (
                    <div className="text-center py-16">
                      <div className="w-20 h-20 bg-green-600 text-white rounded-full flex items-center justify-center text-4xl mx-auto mb-4">
                        □
                      </div>
                      <h3 className="text-xl font-semibold text-success mb-2">Upload Complete!</h3>
                      <p className="text-gray-600 mb-6">
                        {files[0].transactionCount} transactions found in {files[0].name}
                      </p>
                      <button
                        className="w-full bg-gradient-to-br from-giki-primary to-giki-dark-blue hover:from-giki-primary-hover hover:to-giki-dark-blue text-white py-4 text-lg font-semibold rounded-xl transition-all duration-200 shadow-[0_4px_16px_rgba(41,83,67,0.2)] hover:shadow-[0_6px_20px_rgba(41,83,67,0.3)]"
                        onClick={() => files[0].uploadId && navigate(`/processing/${files[0].uploadId}`)}
                      >
                        Start AI Categorization →
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <EnhancedFileUpload
                  onUploadComplete={handleUploadComplete}
                  onUploadError={handleUploadError}
                  maxFiles={5}
                  acceptedTypes={['.xlsx', '.xls', '.csv', '.pdf', '.qbo', '.ofx', '.txt']}
                  maxFileSize={50}
                  className="mb-6"
                />
              )}

              {/* Error State */}
              {files.length > 0 && files[0].status === 'error' && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
                  <p className="text-red-700 font-semibold mb-1">Upload Failed</p>
                  <p className="text-error text-sm">{files[0].errorMessage}</p>
                  <button
                    className="mt-3 text-red-700 text-sm font-medium hover:underline"
                    onClick={() => setFiles([])}
                  >
                    Try Again
                  </button>
                </div>
              )}

              {/* Security Note */}
              <div className="flex items-center gap-2 justify-center text-gray-500 text-xs mt-3">
                <span style={{ color: 'var(--giki-primary)' }}>□</span>
                <span>Bank-level encryption • Files deleted after processing</span>
              </div>
            </section>
        </div>
      </div>
    </div>
  );
});

UploadPage.displayName = 'UploadPage';

export default UploadPage;