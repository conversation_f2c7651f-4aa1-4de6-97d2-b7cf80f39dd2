/**
 * Processing Page - Working Version with Backend Integration
 * Connects to FastAPI backend processing endpoints
 * Real-time file processing status following giki.ai brand guidelines
 */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { apiClient } from '@/shared/services/api/apiClient';

interface ProcessingJob {
  id: string;
  filename: string;
  status: 'processing' | 'completed' | 'failed' | 'categorizing';
  progress: number;
  transactions_found: number;
  transactions_categorized: number;
  accuracy_score: number;
  error_message?: string;
  started_at: string;
  completed_at?: string;
}

const ProcessingPage: React.FC = () => {
  const navigate = useNavigate();
  const [jobs, setJobs] = useState<ProcessingJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [overallProgress, setOverallProgress] = useState(0);

  // Fetch processing status from backend
  useEffect(() => {
    const fetchProcessingStatus = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await apiClient.get('/files/processing-status');
        const data = response.data;
        setJobs(data.data || data.jobs || []);

      } catch (fetchError) {
        console.error('Processing status fetch error:', fetchError);
        setError(fetchError instanceof Error ? fetchError.message : 'Failed to load processing status');

        // Set mock data on error for development
        setJobs([
          {
            id: '1',
            filename: 'bank_statement_december.pdf',
            status: 'completed',
            progress: 100,
            transactions_found: 247,
            transactions_categorized: 215,
            accuracy_score: 87.2,
            started_at: '2025-01-13T10:30:00Z',
            completed_at: '2025-01-13T10:35:00Z',
          },
          {
            id: '2',
            filename: 'credit_card_statement.csv',
            status: 'categorizing',
            progress: 75,
            transactions_found: 156,
            transactions_categorized: 117,
            accuracy_score: 89.1,
            started_at: '2025-01-13T10:45:00Z',
          },
          {
            id: '3',
            filename: 'expense_report_Q4.xlsx',
            status: 'processing',
            progress: 35,
            transactions_found: 0,
            transactions_categorized: 0,
            accuracy_score: 0,
            started_at: '2025-01-13T10:50:00Z',
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchProcessingStatus();

    // Poll for updates every 2 seconds while processing
    const interval = setInterval(() => {
      if (jobs.some(job => job.status === 'processing' || job.status === 'categorizing')) {
        fetchProcessingStatus();
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [jobs.length]); // Re-run when jobs array length changes

  // Calculate overall progress
  useEffect(() => {
    if (jobs.length > 0) {
      const totalProgress = jobs.reduce((sum, job) => sum + job.progress, 0);
      setOverallProgress(Math.round(totalProgress / jobs.length));
    }
  }, [jobs]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processing':
        return '⚙️';
      case 'categorizing':
        return '🧠';
      case 'completed':
        return '✅';
      case 'failed':
        return '❌';
      default:
        return '📄';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'categorizing':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const diffMs = end.getTime() - start.getTime();
    const diffSecs = Math.round(diffMs / 1000);
    
    if (diffSecs < 60) return `${diffSecs}s`;
    const diffMins = Math.round(diffSecs / 60);
    if (diffMins < 60) return `${diffMins}m`;
    const diffHours = Math.round(diffMins / 60);
    return `${diffHours}h`;
  };

  const handleContinue = () => {
    const completedJobs = jobs.filter(job => job.status === 'completed');
    if (completedJobs.length > 0) {
      navigate('/transactions');
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Processing Files
        </h1>
        <p className="text-gray-600">
          AI is analyzing your files and categorizing transactions
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="text-yellow-700 font-medium">Backend Connection Issue</div>
          <div className="text-yellow-600 text-sm mt-1">
            {error} • Showing demo processing status for testing
          </div>
        </div>
      )}

      {/* Overall Progress */}
      <div className="bg-gradient-to-br from-[#295343] to-[#1A3F5F] rounded-xl p-6 text-white mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Overall Progress</h2>
          <span className="text-2xl font-bold">{overallProgress}%</span>
        </div>
        
        <div className="w-full bg-white/20 rounded-full h-3 mb-4">
          <div 
            className="bg-white h-3 rounded-full transition-all duration-500"
            style={{ width: `${overallProgress}%` }}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <div className="font-semibold">Files Processed</div>
            <div className="opacity-90">
              {jobs.filter(j => j.status === 'completed').length} of {jobs.length}
            </div>
          </div>
          <div>
            <div className="font-semibold">Transactions Found</div>
            <div className="opacity-90">
              {jobs.reduce((sum, job) => sum + job.transactions_found, 0)}
            </div>
          </div>
          <div>
            <div className="font-semibold">Average Accuracy</div>
            <div className="opacity-90">
              {jobs.length > 0 
                ? Math.round(jobs.reduce((sum, job) => sum + job.accuracy_score, 0) / jobs.length) 
                : 0}%
            </div>
          </div>
        </div>
      </div>

      {/* Processing Jobs */}
      <div className="space-y-4 mb-8">
        {jobs.map((job) => (
          <div key={job.id} className="bg-white rounded-xl shadow-lg border border-[#E1E5E9] p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="text-2xl">{getStatusIcon(job.status)}</div>
                <div>
                  <h3 className="font-semibold text-gray-900">{job.filename}</h3>
                  <div className="text-sm text-gray-600">
                    Started {formatDuration(job.started_at, job.completed_at)} ago
                    {job.completed_at && ` • Completed in ${formatDuration(job.started_at, job.completed_at)}`}
                  </div>
                </div>
              </div>
              
              <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(job.status)}`}>
                {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Progress</span>
                <span>{job.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    job.status === 'completed' ? 'bg-green-500' :
                    job.status === 'failed' ? 'bg-red-500' :
                    'bg-[#295343]'
                  }`}
                  style={{ width: `${job.progress}%` }}
                />
              </div>
            </div>

            {/* Job Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <div className="text-gray-600">Transactions Found</div>
                <div className="font-semibold text-gray-900">{job.transactions_found}</div>
              </div>
              <div>
                <div className="text-gray-600">Categorized</div>
                <div className="font-semibold text-gray-900">{job.transactions_categorized}</div>
              </div>
              <div>
                <div className="text-gray-600">Accuracy Score</div>
                <div className="font-semibold text-gray-900">
                  {job.accuracy_score > 0 ? `${job.accuracy_score}%` : 'Calculating...'}
                </div>
              </div>
            </div>

            {/* Error Message */}
            {job.status === 'failed' && job.error_message && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="text-red-700 text-sm">
                  <strong>Error:</strong> {job.error_message}
                </div>
              </div>
            )}

            {/* Processing Steps for Active Jobs */}
            {(job.status === 'processing' || job.status === 'categorizing') && (
              <div className="mt-4 space-y-2">
                <div className="text-sm font-medium text-gray-700">Processing Steps:</div>
                <div className="space-y-1 text-sm">
                  <div className={`flex items-center gap-2 ${job.progress > 0 ? 'text-green-600' : 'text-gray-400'}`}>
                    <span>{job.progress > 0 ? '✓' : '○'}</span>
                    File uploaded and validated
                  </div>
                  <div className={`flex items-center gap-2 ${job.progress > 20 ? 'text-green-600' : 'text-gray-400'}`}>
                    <span>{job.progress > 20 ? '✓' : '○'}</span>
                    Data extraction and parsing
                  </div>
                  <div className={`flex items-center gap-2 ${job.progress > 50 ? 'text-green-600' : 'text-gray-400'}`}>
                    <span>{job.progress > 50 ? '✓' : '○'}</span>
                    Transaction identification
                  </div>
                  <div className={`flex items-center gap-2 ${job.status === 'categorizing' ? 'text-blue-600' : job.progress > 80 ? 'text-green-600' : 'text-gray-400'}`}>
                    <span>{job.status === 'categorizing' ? '◐' : job.progress > 80 ? '✓' : '○'}</span>
                    AI categorization {job.status === 'categorizing' && '(in progress)'}
                  </div>
                  <div className={`flex items-center gap-2 ${job.progress === 100 ? 'text-green-600' : 'text-gray-400'}`}>
                    <span>{job.progress === 100 ? '✓' : '○'}</span>
                    Quality validation and completion
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Empty State */}
      {jobs.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-gray-400 text-2xl">📄</span>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No files processing</h3>
          <p className="text-gray-600 mb-4">Upload files to start processing</p>
          <button
            onClick={() => navigate('/upload')}
            className="inline-flex items-center px-4 py-2 bg-[#295343] text-white rounded-lg hover:bg-[#1D372E] transition-colors"
          >
            Upload Files
          </button>
        </div>
      )}

      {/* Action Buttons */}
      {jobs.length > 0 && (
        <div className="flex items-center justify-center gap-4">
          <button
            onClick={() => navigate('/upload')}
            className="px-6 py-3 border border-[#295343] text-[#295343] rounded-xl font-semibold hover:bg-[#295343] hover:text-white transition-all duration-200"
          >
            Upload More Files
          </button>
          
          {jobs.some(job => job.status === 'completed') && (
            <button
              onClick={handleContinue}
              className="px-6 py-3 bg-gradient-to-br from-[#295343] to-[#1A3F5F] hover:from-[#1D372E] hover:to-[#163651] text-white rounded-xl font-semibold transition-all duration-200 shadow-lg"
            >
              Review Transactions
            </button>
          )}
        </div>
      )}

      {/* Processing Tips */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">◎ Processing Tips</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
          <div>
            <strong>File Processing:</strong> We extract transactions from PDFs, CSVs, Excel files, and more.
          </div>
          <div>
            <strong>AI Categorization:</strong> Our AI categorizes transactions with 85%+ accuracy.
          </div>
          <div>
            <strong>Quality Check:</strong> Every transaction is validated for accuracy and completeness.
          </div>
          <div>
            <strong>Review Ready:</strong> You can review and improve categorizations before export.
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProcessingPage;