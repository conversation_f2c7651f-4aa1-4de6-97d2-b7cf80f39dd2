/**
 * Upload New Transactions Component - Daily Use
 * Matches wireframe: docs/wireframes/03-daily-use-journey/02-upload.md
 */
import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Card } from '@/shared/components/ui/card';
import { Progress } from '@/shared/components/ui/progress';
import { useToast } from '@/shared/components/ui/use-toast';
import {
  Upload,
  FileSpreadsheet,
  CheckCircle,
  ArrowRight,
  Lightbulb,
} from 'lucide-react';

interface ProcessingFile {
  name: string;
  progress: number;
  status: 'reading' | 'analyzing' | 'categorizing' | 'complete';
}

interface RecentFile {
  name: string;
  transactionCount: number;
  uploadDate: string;
}

interface UploadNewTransactionsProps {
  onFileUpload?: (files: File[]) => void;
  isProcessing?: boolean;
}

const UploadNewTransactions: React.FC<UploadNewTransactionsProps> = ({
  onFileUpload,
  isProcessing: _isProcessing = false,
}) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isDragging, setIsDragging] = useState(false);
  const [processingFiles, setProcessingFiles] = useState<ProcessingFile[]>([]);

  // Mock recent files data
  const [recentFiles] = useState<RecentFile[]>([
    {
      name: 'february_data.csv',
      transactionCount: 147,
      uploadDate: '2 days ago',
    },
    {
      name: 'january_2025.xlsx',
      transactionCount: 523,
      uploadDate: '1 week ago',
    },
    {
      name: 'q4_expenses.csv',
      transactionCount: 892,
      uploadDate: '2 weeks ago',
    },
  ]);

  const getStatusText = (status: ProcessingFile['status']): string => {
    switch (status) {
      case 'reading':
        return 'Reading file...';
      case 'analyzing':
        return 'Analyzing content...';
      case 'categorizing':
        return 'Categorizing...';
      case 'complete':
        return 'Complete!';
      default:
        return 'Processing...';
    }
  };

  const simulateFileProcessing = useCallback(
    (fileName: string, index: number) => {
      const stages = [
        'reading',
        'analyzing',
        'categorizing',
        'complete',
      ] as const;
      let currentStage = 0;
      let progress = 0;

      const interval = setInterval(() => {
        progress += Math.random() * 15 + 5; // Progress 5-20% each step

        if (
          progress >= 25 * (currentStage + 1) &&
          currentStage < stages.length - 1
        ) {
          currentStage++;
        }

        if (progress >= 100) {
          progress = 100;
          currentStage = stages.length - 1;
          clearInterval(interval);

          // Auto-redirect to review after completion
          setTimeout(() => {
            navigate('/transactions/review');
          }, 1500);
        }

        setProcessingFiles((prev) =>
          prev.map((f) =>
            f.name === fileName
              ? { ...f, progress, status: stages[currentStage] }
              : f,
          ),
        );
      }, 100);
    },
    [navigate],
  );

  const handleFileSelect = useCallback(
    (files: File[]) => {
      // Validate file types
      const validFiles = files.filter((file) => {
        const isValid =
          file.name.endsWith('.xlsx') ||
          file.name.endsWith('.xls') ||
          file.name.endsWith('.csv');

        if (!isValid) {
          toast({
            title: 'Invalid file format',
            description: `${file.name} is not supported. Please use Excel or CSV files.`,
            variant: 'destructive',
          });
        }
        return isValid;
      });

      if (validFiles.length === 0) return;

      // Start processing simulation
      const newProcessingFiles: ProcessingFile[] = validFiles.map((file) => ({
        name: file.name,
        progress: 0,
        status: 'reading',
      }));

      setProcessingFiles(newProcessingFiles);

      // Simulate processing
      validFiles.forEach((file, index) => {
        simulateFileProcessing(file.name, index);
      });

      // Call the parent handler
      onFileUpload?.(validFiles);
    },
    [toast, onFileUpload, simulateFileProcessing],
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragging(false);

      const files = Array.from(e.dataTransfer.files);
      handleFileSelect(files);
    },
    [handleFileSelect],
  );

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      handleFileSelect(files);
    }
  };

  const handleViewRecent = (fileName: string) => {
    navigate(`/transactions?file=${encodeURIComponent(fileName)}`);
  };

  return (
    <div className="p-8 bg-white min-h-screen">
      {/* Professional Header - Enhanced Design */}
      <div className="mb-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-semibold text-gray-700 mb-2">
            Upload New Transactions
          </h1>
          <p className="text-gray-500 text-base">
            AI-powered categorization in real-time
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto">
        {/* Main Upload Card */}
        <Card className="border border-gray-200 shadow-sm overflow-hidden mb-8">
          <div className="bg-brand-primary text-white px-6 py-4">
            <div className="flex items-center gap-2">
              <FileSpreadsheet className="h-5 w-5" />
              <h2 className="text-base font-semibold">
                Quick Upload & Process
              </h2>
            </div>
          </div>
          <div className="p-6">
            {/* Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-8 md:p-12 text-center transition-colors cursor-pointer ${
                isDragging
                  ? 'border-brand-primary bg-green-50'
                  : 'border-slate-300 hover:border-brand-primary hover:bg-slate-50'
              }`}
              onDrop={handleDrop}
              onDragOver={(e) => e.preventDefault()}
              onDragEnter={() => setIsDragging(true)}
              onDragLeave={() => setIsDragging(false)}
              onClick={() => document.getElementById('upload-input')?.click()}
            >
              <FileSpreadsheet className="h-16 w-16 mx-auto mb-4 text-slate-400" />
              <h3 className="text-xl font-semibold text-slate-900 mb-2">
                ⊞ Drop your files here
              </h3>
              <p className="text-slate-600 mb-4">
                We&apos;ll categorize them for you
                <br />
                Excel or CSV accepted
              </p>
              <Button
                variant="outline"
                className="border-brand-primary text-brand-primary hover:bg-green-50"
                onClick={(e) => {
                  e.stopPropagation();
                  document.getElementById('upload-input')?.click();
                }}
              >
                <Upload className="h-4 w-4 mr-2" />
                Select Files
              </Button>
              <input
                id="upload-input"
                type="file"
                multiple
                accept=".xlsx,.xls,.csv"
                onChange={handleFileInputChange}
                className="hidden"
              />
            </div>
          </div>
        </Card>

        {/* Processing Files */}
        {processingFiles.length > 0 && (
          <Card className="border border-gray-200 shadow-sm overflow-hidden mb-8">
            <div className="bg-brand-primary text-white px-6 py-4">
              <h3 className="text-base font-semibold">↑ Processing Files</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {processingFiles.map((file, index) => (
                  <div
                    key={index}
                    className="bg-slate-50 rounded-lg p-4 border"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-slate-900">
                        {file.name}
                      </span>
                      <span className="text-sm text-slate-500">
                        {file.progress}%
                      </span>
                    </div>
                    <Progress value={file.progress} className="h-2 mb-2" />
                    <div className="text-sm text-slate-600">
                      Status: {getStatusText(file.status)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        )}

        {/* Recently Processed */}
        <Card className="border border-gray-200 shadow-sm overflow-hidden mb-8">
          <div className="bg-brand-primary text-white px-6 py-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              <h3 className="text-base font-semibold">∷ Recently Processed</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              {recentFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border hover:bg-slate-100 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-brand-primary" />
                    <div>
                      <div className="font-medium text-slate-900">
                        {file.name}
                      </div>
                      <div className="text-sm text-slate-500">
                        {file.transactionCount} transactions
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleViewRecent(file.name)}
                    className="text-brand-primary hover:text-[#1D372E]"
                  >
                    View
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </Card>

        {/* Professional Tip */}
        <Card className="border border-gray-200 shadow-sm overflow-hidden">
          <div className="bg-blue-50 border-l-4 border-brand-primary p-6">
            <div className="flex items-start gap-3">
              <Lightbulb className="h-5 w-5 text-brand-primary mt-0.5 flex-shrink-0" />
              <div className="text-sm text-slate-700">
                <strong className="text-brand-primary">∷ Pro Tip:</strong> Upload
                daily, weekly, or monthly - we handle it all automatically. No
                need to categorize anything yourself!
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default UploadNewTransactions;
