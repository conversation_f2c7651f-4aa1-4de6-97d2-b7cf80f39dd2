/**
 * Professional Register Page - Working Version
 * Following same design patterns as LoginPageWorking
 * Turn Messy Statements Into Organized Books
 */
import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';

const RegisterPageWorking: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Basic validation
      if (formData.password !== formData.confirmPassword) {
        setError('Passwords do not match');
        return;
      }

      if (formData.password.length < 6) {
        setError('Password must be at least 6 characters');
        return;
      }

      // Mock registration - in real app would call API
      if (formData.name && formData.email && formData.password) {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        navigate('/login', {
          replace: true,
        });
      } else {
        setError('Please fill in all fields');
      }
    } catch {
      setError('Registration failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="min-h-screen bg-[#F8F9FA] font-inter flex flex-col">
      {/* Professional Header */}
      <header className="bg-white border-b border-[#E1E5E9] fixed top-0 left-0 right-0 z-50">
        <div className="max-w-[1400px] mx-auto px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <span className="text-2xl font-semibold text-[#295343]">giki.ai</span>
            </div>
            {/* Login Link */}
            <div>
              <Link to="/login" className="text-[#295343] hover:text-[#1D372E] font-medium">
                Already have an account? Sign in
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Registration Container */}
      <main className="flex-1 mt-16">
        <div className="max-w-[1400px] w-full mx-auto px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-[1fr_500px] gap-16 items-center min-h-[calc(100vh-4rem)]">
            {/* Hero Section - Left Column */}
            <div className="lg:pr-8">
              {/* Hero Title */}
              <h1 className="text-5xl lg:text-[3.5rem] font-extrabold text-gray-900 mb-6 leading-[1.1]">
                Join Thousands<br/>
                Managing Their <span className="text-[#295343]">Books</span>
              </h1>

              {/* Hero Subtitle */}
              <p className="text-2xl text-gray-600 mb-10 leading-relaxed">
                Start organizing your financial statements with AI-powered categorization.
              </p>

              {/* Benefits List */}
              <div className="space-y-4 mb-10">
                <div className="flex items-center gap-4">
                  <div className="w-6 h-6 bg-[#295343] rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-lg text-gray-700">Upload any bank statement format</span>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-6 h-6 bg-[#295343] rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-lg text-gray-700">87% AI categorization accuracy</span>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-6 h-6 bg-[#295343] rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-lg text-gray-700">Export to QuickBooks, Xero, or CSV</span>
                </div>
              </div>
            </div>

            {/* Professional Registration Panel - Right Column */}
            <div className="bg-white shadow-[0_12px_40px_rgba(0,0,0,0.08)] border border-[#E1E5E9] rounded-[20px]">
              <div className="p-10">
                {/* Registration Header */}
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-gray-900 mb-2">Create Account</h2>
                  <p className="text-gray-600">
                    Start managing your statements today
                  </p>
                </div>

                {/* Error Display */}
                {error && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                    {error}
                  </div>
                )}

                {/* Registration Form */}
                <form onSubmit={handleSubmit} className="space-y-5">
                  {/* Name Field */}
                  <div>
                    <label className="text-sm font-semibold text-gray-700 mb-2 block">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="John Doe"
                      className="w-full px-5 py-4 border border-[#E1E5E9] rounded-[10px] text-[1.0625rem] focus:ring-2 focus:ring-[#295343] focus:ring-opacity-20 focus:border-[#295343] transition-all duration-200"
                      required
                    />
                  </div>

                  {/* Email Field */}
                  <div>
                    <label className="text-sm font-semibold text-gray-700 mb-2 block">
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      className="w-full px-5 py-4 border border-[#E1E5E9] rounded-[10px] text-[1.0625rem] focus:ring-2 focus:ring-[#295343] focus:ring-opacity-20 focus:border-[#295343] transition-all duration-200"
                      required
                    />
                  </div>

                  {/* Password Field */}
                  <div>
                    <label className="text-sm font-semibold text-gray-700 mb-2 block">
                      Password
                    </label>
                    <input
                      type="password"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      placeholder="••••••••"
                      className="w-full px-5 py-4 border border-[#E1E5E9] rounded-[10px] text-[1.0625rem] focus:ring-2 focus:ring-[#295343] focus:ring-opacity-20 focus:border-[#295343] transition-all duration-200"
                      required
                    />
                  </div>

                  {/* Confirm Password Field */}
                  <div>
                    <label className="text-sm font-semibold text-gray-700 mb-2 block">
                      Confirm Password
                    </label>
                    <input
                      type="password"
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      placeholder="••••••••"
                      className="w-full px-5 py-4 border border-[#E1E5E9] rounded-[10px] text-[1.0625rem] focus:ring-2 focus:ring-[#295343] focus:ring-opacity-20 focus:border-[#295343] transition-all duration-200"
                      required
                    />
                  </div>

                  {/* Create Account Button */}
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-gradient-to-br from-[#295343] to-[#1A3F5F] hover:from-[#1D372E] hover:to-[#163651] text-white py-4 text-lg font-semibold rounded-xl transition-all duration-200 shadow-[0_4px_16px_rgba(41,83,67,0.2)] hover:shadow-[0_6px_20px_rgba(41,83,67,0.3)] disabled:opacity-50"
                  >
                    {isLoading ? 'Creating Account...' : 'Create Account'}
                  </button>
                </form>

                {/* Terms */}
                <div className="mt-5 text-center">
                  <p className="text-xs text-gray-500">
                    By creating an account, you agree to our{' '}
                    <Link to="/terms" className="text-[#295343] hover:text-[#1D372E]">Terms of Service</Link>
                    {' '}and{' '}
                    <Link to="/privacy" className="text-[#295343] hover:text-[#1D372E]">Privacy Policy</Link>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default RegisterPageWorking;