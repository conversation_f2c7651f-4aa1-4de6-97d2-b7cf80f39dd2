import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Separator } from '@/shared/components/ui/separator';
import { gradientButtonStyle, applyGradientHover } from '@/shared/utils/brandGradients';
import {
  Target,
  BarChart3,
  Clock,
  CheckCircle,
  MessageCircle,
} from 'lucide-react';
import { useAuth } from '@/shared/hooks/useUnifiedServices';

export const FirstLoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // Get user's first name for personalization
  const firstName =
    user?.full_name?.split(' ')[0] || user?.email?.split('@')[0] || 'there';

  useEffect(() => {
    // Track first login analytics
  }, [user]);

  const handleStartSetup = () => {
    // Track onboarding start
    // Navigate to onboarding welcome screen
    navigate('/onboarding');
  };

  const handleOpenAgent = () => {
    // Open agent panel for help
  };

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Clean header */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="text-xl font-semibold text-slate-900">giki.ai</div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleOpenAgent}
              className="flex items-center gap-2"
            >
              <MessageCircle className="h-4 w-4" />
              Help
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="max-w-4xl mx-auto px-6 py-12">
        {/* Welcome Hero Card */}
        <Card className="max-w-2xl mx-auto mb-8">
          <CardContent className="p-8 text-center">
            <h1 className="text-2xl font-semibold mb-2 text-slate-900">
              Welcome to giki.ai, {firstName}! 👋
            </h1>

            <div className="mt-8">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Target className="h-6 w-6 text-brand-primary" />
                <h2 className="text-xl font-semibold text-slate-900">
                  Let&apos;s Get You Started
                </h2>
              </div>

              <p className="text-slate-600 mb-6">
                We&apos;ll have you up and running in just 10 minutes.
                Here&apos;s what we&apos;ll do together:
              </p>

              {/* Setup steps */}
              <div className="space-y-3 text-left max-w-md mx-auto mb-8">
                <div className="flex gap-3 items-start">
                  <span className="text-lg">①</span>
                  <span className="text-sm text-slate-700">
                    Upload your historical data (with categories)
                  </span>
                </div>
                <div className="flex gap-3 items-start">
                  <span className="text-lg">②</span>
                  <span className="text-sm text-slate-700">
                    We&apos;ll learn your categorization patterns
                  </span>
                </div>
                <div className="flex gap-3 items-start">
                  <span className="text-lg">③</span>
                  <span className="text-sm text-slate-700">
                    Test our accuracy together
                  </span>
                </div>
                <div className="flex gap-3 items-start">
                  <span className="text-lg">④</span>
                  <span className="text-sm text-slate-700">
                    Go live with automatic categorization!
                  </span>
                </div>
              </div>

              <Button
                size="lg"
                className="min-w-[200px] text-white font-semibold"
                style={gradientButtonStyle}
                onMouseEnter={(e) => applyGradientHover(e.currentTarget, true)}
                onMouseLeave={(e) => applyGradientHover(e.currentTarget, false)}
                onClick={handleStartSetup}
              >
                Start Setup →
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Benefits & Time Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <BarChart3 className="h-5 w-5 text-brand-primary" />
                <h3 className="font-semibold text-slate-900">
                  What You&apos;ll Get
                </h3>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-brand-primary" />
                  <span className="text-sm text-slate-700">
                    95%+ accuracy on categorization
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-brand-primary" />
                  <span className="text-sm text-slate-700">
                    Automatic processing going forward
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-brand-primary" />
                  <span className="text-sm text-slate-700">
                    Export-ready reports
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-brand-primary" />
                  <span className="text-sm text-slate-700">
                    Real-time insights
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Clock className="h-5 w-5 text-brand-primary" />
                <h3 className="font-semibold text-slate-900">Time Estimate</h3>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-700">• Upload files:</span>
                  <span className="text-slate-500">2 min</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-slate-700">• AI training:</span>
                  <span className="text-slate-500">5 min</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-slate-700">• Validation:</span>
                  <span className="text-slate-500">3 min</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-semibold">
                  <span className="text-slate-900">Total:</span>
                  <span className="text-slate-900">~10 minutes</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Help footer */}
        <div className="text-center mt-12 text-sm text-slate-600">
          Need help? The giki assistant is here →
          <Button
            variant="ghost"
            size="sm"
            className="ml-2"
            onClick={handleOpenAgent}
          >
            <MessageCircle className="h-4 w-4" />
          </Button>
          <NAME_EMAIL>
        </div>
      </div>
    </div>
  );
};

export default FirstLoginPage;
