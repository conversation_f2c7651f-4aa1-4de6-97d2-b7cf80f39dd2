/**
 * Professional Landing + Login Page
 * Following: docs/design-system/page-mockups/000-landing-login.html
 * Turn Messy Statements Into Organized Books
 */
import React, { useEffect, useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Card, CardContent } from '@/shared/components/ui/card';
import { useAuth } from '../hooks/useAuth';
import { useToast } from '@/shared/components/ui/use-toast';
import { Loader2, ArrowRight } from 'lucide-react';

const LoginPage: React.FC = () => {
  const { isAuthenticated, login } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // If user is already authenticated, redirect to dashboard
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/upload', {
        replace: true,
      });
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await login(email, password);
      if (result.success) {
        toast({
          title: 'Welcome back!',
          description: "You've been signed in successfully.",
        });
        navigate('/upload', {
          replace: true,
        });
      } else {
        toast({
          title: 'Sign in failed',
          description:
            result.error || 'Please check your credentials and try again.',
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        title: 'Sign in failed',
        description: 'Please check your credentials and try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };


  return (
    <div className="min-h-screen bg-[#F8F9FA] font-inter flex flex-col">
      {/* Professional Header */}
      <header className="bg-white border-b border-[#E1E5E9] fixed top-0 left-0 right-0 z-50">
        <div className="max-w-[1400px] mx-auto px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <span className="text-2xl font-semibold text-[#295343]">giki.ai</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Landing Container - Two Column Layout */}
      <main className="flex-1 mt-16">
        <div className="max-w-[1400px] w-full mx-auto px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-[1fr_460px] gap-16 items-center min-h-[calc(100vh-4rem)]">
          {/* Hero Section - Left Column */}
          <div className="lg:pr-8">
            {/* Hero Title */}
            <h1 className="text-5xl lg:text-[3.5rem] font-extrabold text-gray-900 mb-6 leading-[1.1]">
              Turn Messy Statements<br/>
              Into <span className="text-[#295343]">Organized Books</span>
            </h1>

            {/* Hero Subtitle */}
            <p className="text-2xl text-gray-600 mb-10 leading-relaxed">
              AI categorizes your transactions. Export to any accounting software.
            </p>

            {/* Process Preview Banner */}
            <div className="mb-10 p-8 bg-gradient-to-br from-[#295343] to-[#1A3F5F] rounded-2xl text-white shadow-lg">
              <div className="flex items-center justify-around">
                <div className="text-center">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl mb-3 mx-auto border-2 border-white/30">
                    ↑
                  </div>
                  <div className="font-semibold">Bank Statements</div>
                  <div className="text-sm opacity-80 mt-1">Upload any format</div>
                </div>
                <div className="text-2xl opacity-60">→</div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl mb-3 mx-auto border-2 border-white/30">
                    ⚬
                  </div>
                  <div className="font-semibold">AI Categorized</div>
                  <div className="text-sm opacity-80 mt-1">87% accuracy</div>
                </div>
                <div className="text-2xl opacity-60">→</div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl mb-3 mx-auto border-2 border-white/30">
                    ✓
                  </div>
                  <div className="font-semibold">Export Ready</div>
                  <div className="text-sm opacity-80 mt-1">QuickBooks, Xero, CSV</div>
                </div>
              </div>
            </div>


          </div>

          {/* Professional Login Panel - Right Column */}
          <Card className="bg-white shadow-[0_12px_40px_rgba(0,0,0,0.08)] border border-[#E1E5E9] rounded-[20px]">
            <CardContent className="p-10">
              {/* Login Header */}
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-2">Welcome Back</h2>
                <p className="text-gray-600">
                  Access your MIS dashboard
                </p>
              </div>

              {/* Login Form */}
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSubmit(e).catch(console.error);
                }}
                className="space-y-5"
              >
                {/* Email Field */}
                <div>
                  <Label
                    htmlFor="email"
                    className="text-sm font-semibold text-gray-700 mb-2 block"
                  >
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-5 py-4 border border-[#E1E5E9] rounded-[10px] text-[1.0625rem] focus:ring-2 focus:ring-[#295343] focus:ring-opacity-20 focus:border-[#295343] transition-all duration-200"
                    required
                  />
                </div>

                {/* Password Field */}
                <div>
                  <Label
                    htmlFor="password"
                    className="text-sm font-semibold text-gray-700 mb-2 block"
                  >
                    Password
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="••••••••"
                    className="w-full px-5 py-4 border border-[#E1E5E9] rounded-[10px] text-[1.0625rem] focus:ring-2 focus:ring-[#295343] focus:ring-opacity-20 focus:border-[#295343] transition-all duration-200"
                    required
                  />
                </div>

                {/* Remember Me & Forgot Password */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-2">
                    <input type="checkbox" id="remember" className="rounded border-gray-300" />
                    <label htmlFor="remember" className="text-sm text-gray-600">Remember me</label>
                  </div>
                  <Link to="/forgot-password" className="text-sm text-[#295343] hover:text-[#1D372E] font-medium">
                    Forgot password?
                  </Link>
                </div>

                {/* Sign In Button */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-gradient-to-br from-[#295343] to-[#1A3F5F] hover:from-[#1D372E] hover:to-[#163651] text-white py-4 text-lg font-semibold rounded-xl transition-all duration-200 shadow-[0_4px_16px_rgba(41,83,67,0.2)] hover:shadow-[0_6px_20px_rgba(41,83,67,0.3)]"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Signing in...
                    </>
                  ) : (
                    'Sign In'
                  )}
                </Button>
              </form>

              {/* Divider */}
              <div className="relative my-5">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-4 bg-white text-gray-500">or</span>
                </div>
              </div>

              {/* Try Now Section */}
              <div className="text-center">
                <p className="text-sm text-gray-600">
                  New to giki.ai? <Link to="/signup" className="text-[#295343] font-medium hover:text-[#1D372E]">Create account</Link>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

      </div>
    </main>
    </div>
  );
};

export default LoginPage;
