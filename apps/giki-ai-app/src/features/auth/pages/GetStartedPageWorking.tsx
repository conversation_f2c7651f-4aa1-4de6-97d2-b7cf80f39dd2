/**
 * Professional Get Started Page - Working Version
 * Following same design patterns as LoginPageWorking
 * Turn Messy Statements Into Organized Books
 */
import React from 'react';
import { useNavigate, Link } from 'react-router-dom';

const GetStartedPageWorking: React.FC = () => {
  const navigate = useNavigate();

  const handleGetStarted = () => {
    navigate('/register');
  };

  const handleSignIn = () => {
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-[#F8F9FA] font-inter flex flex-col">
      {/* Professional Header */}
      <header className="bg-white border-b border-[#E1E5E9] fixed top-0 left-0 right-0 z-50">
        <div className="max-w-[1400px] mx-auto px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <span className="text-2xl font-semibold text-[#295343]">giki.ai</span>
            </div>
            {/* Navigation */}
            <div className="flex items-center gap-6">
              <Link to="/login" className="text-gray-600 hover:text-gray-900 font-medium">
                Sign In
              </Link>
              <button
                onClick={handleGetStarted}
                className="bg-gradient-to-br from-[#295343] to-[#1A3F5F] hover:from-[#1D372E] hover:to-[#163651] text-white px-6 py-2 rounded-lg font-medium transition-all duration-200"
              >
                Get Started
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Get Started Container */}
      <main className="flex-1 mt-16">
        <div className="max-w-[1400px] w-full mx-auto px-6 lg:px-8 py-16">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-6xl lg:text-7xl font-extrabold text-gray-900 mb-8 leading-[1.1]">
              Turn Messy Statements<br/>
              Into <span className="text-[#295343]">Organized Books</span>
            </h1>
            
            <p className="text-2xl text-gray-600 mb-12 leading-relaxed max-w-3xl mx-auto">
              AI-powered financial statement categorization. Upload any format, 
              get organized books, export to any accounting software.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={handleGetStarted}
                className="bg-gradient-to-br from-[#295343] to-[#1A3F5F] hover:from-[#1D372E] hover:to-[#163651] text-white px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-200 shadow-[0_4px_16px_rgba(41,83,67,0.2)] hover:shadow-[0_6px_20px_rgba(41,83,67,0.3)]"
              >
                Start Free Trial
              </button>
              <button
                onClick={handleSignIn}
                className="border border-[#295343] text-[#295343] hover:bg-[#295343] hover:text-white px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-200"
              >
                Sign In
              </button>
            </div>
          </div>

          {/* Process Flow */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              How It Works
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Step 1 */}
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-[#295343] to-[#1A3F5F] rounded-full flex items-center justify-center text-3xl text-white mb-6 mx-auto">
                  ↑
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Upload Statements</h3>
                <p className="text-gray-600">
                  Upload bank statements in any format - PDF, CSV, Excel. 
                  We support all major banks and formats.
                </p>
              </div>

              {/* Step 2 */}
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-[#295343] to-[#1A3F5F] rounded-full flex items-center justify-center text-3xl text-white mb-6 mx-auto">
                  ⚬
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">AI Categorization</h3>
                <p className="text-gray-600">
                  Our AI automatically categorizes transactions with 87% accuracy. 
                  Review and improve as needed.
                </p>
              </div>

              {/* Step 3 */}
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-[#295343] to-[#1A3F5F] rounded-full flex items-center justify-center text-3xl text-white mb-6 mx-auto">
                  ✓
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Export Ready</h3>
                <p className="text-gray-600">
                  Export organized data to QuickBooks, Xero, Sage, 
                  or any accounting software via CSV.
                </p>
              </div>
            </div>
          </div>

          {/* Features Grid */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Why Choose giki.ai?
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Feature 1 */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-[#E1E5E9]">
                <div className="w-12 h-12 bg-[#295343] rounded-lg flex items-center justify-center text-white text-xl mb-4">
                  🤖
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">AI-Powered</h3>
                <p className="text-gray-600">
                  Advanced machine learning models trained specifically for 
                  financial transaction categorization.
                </p>
              </div>

              {/* Feature 2 */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-[#E1E5E9]">
                <div className="w-12 h-12 bg-[#295343] rounded-lg flex items-center justify-center text-white text-xl mb-4">
                  ⚡
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Fast Processing</h3>
                <p className="text-gray-600">
                  Process thousands of transactions in minutes. 
                  Get your organized books ready for export quickly.
                </p>
              </div>

              {/* Feature 3 */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-[#E1E5E9]">
                <div className="w-12 h-12 bg-[#295343] rounded-lg flex items-center justify-center text-white text-xl mb-4">
                  🔒
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Bank-Level Security</h3>
                <p className="text-gray-600">
                  Your financial data is encrypted and secure. 
                  We never store sensitive banking credentials.
                </p>
              </div>

              {/* Feature 4 */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-[#E1E5E9]">
                <div className="w-12 h-12 bg-[#295343] rounded-lg flex items-center justify-center text-white text-xl mb-4">
                  📊
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Any Format</h3>
                <p className="text-gray-600">
                  Support for all major bank statement formats - 
                  PDF, CSV, Excel, QBO, OFX, and more.
                </p>
              </div>

              {/* Feature 5 */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-[#E1E5E9]">
                <div className="w-12 h-12 bg-[#295343] rounded-lg flex items-center justify-center text-white text-xl mb-4">
                  🎯
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">High Accuracy</h3>
                <p className="text-gray-600">
                  87% categorization accuracy out of the box, 
                  improving with your feedback and usage.
                </p>
              </div>

              {/* Feature 6 */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-[#E1E5E9]">
                <div className="w-12 h-12 bg-[#295343] rounded-lg flex items-center justify-center text-white text-xl mb-4">
                  🔄
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Easy Export</h3>
                <p className="text-gray-600">
                  One-click export to QuickBooks, Xero, Sage, 
                  or download as CSV for any accounting software.
                </p>
              </div>
            </div>
          </div>

          {/* Final CTA */}
          <div className="text-center bg-white p-12 rounded-xl shadow-lg border border-[#E1E5E9]">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to Organize Your Books?
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Join thousands of businesses already using giki.ai to streamline their accounting.
            </p>
            <button
              onClick={handleGetStarted}
              className="bg-gradient-to-br from-[#295343] to-[#1A3F5F] hover:from-[#1D372E] hover:to-[#163651] text-white px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-200 shadow-[0_4px_16px_rgba(41,83,67,0.2)] hover:shadow-[0_6px_20px_rgba(41,83,67,0.3)]"
            >
              Start Your Free Trial
            </button>
          </div>
        </div>
      </main>
    </div>
  );
};

export default GetStartedPageWorking;