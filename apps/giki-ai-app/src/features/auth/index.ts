/**
 * Auth Feature Barrel Exports
 *
 * This file provides clean imports for all auth-related
 * components, hooks, and services.
 */

// Components
export { default as LoginForm } from './components/LoginForm';
export { default as RegistrationForm } from './components/RegistrationForm';

// Pages
export { default as LoginPage } from './pages/LoginPage';
export { default as RegisterPage } from './pages/RegisterPage';
export { default as GetStartedPage } from './pages/GetStartedPage';
export { FirstLoginPage } from './pages/FirstLoginPage';

// Services
export * from './services/auth';
export * from './services/emailVerificationService';

// Types
export type {
  User,
  AuthState,
  RegisterCredentials,
  AuthResponse,
  PasswordResetRequest,
  PasswordResetConfirm,
} from './types/auth';
export type { LoginCredentials } from './types/auth';

// Hooks - Now provided by unified services
// export * from './hooks/useAuth';
