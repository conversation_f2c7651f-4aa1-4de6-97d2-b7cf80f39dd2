/**
 * Auth Hook
 *
 * Custom hook for managing authentication state and operations.
 */

import { useCallback, useState, useEffect } from 'react';
import useAuthStore from '@/shared/services/auth/authStore';
import { User, RegisterCredentials } from '../types/auth';
import {
  login as authServiceLogin,
  register as authServiceRegister,
} from '../services/authService';

export interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  token: string | null;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (data: RegisterCredentials) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  checkAuth: () => Promise<void>;
  clearError: () => void;
}

export function useAuth(): UseAuthReturn {
  const {
    user,
    token,
    isAuthenticated,
    setUser,
    setToken,
    clearAuth,
  } = useAuthStore();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const login = useCallback(async (email: string, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await authServiceLogin(email, password);
      
      if (result.success && result.data) {
        setUser(result.data.user);
        setToken(result.data.token);
        return { success: true };
      } else {
        const errorMessage = result.error || 'Login failed';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [setUser, setToken]);

  const register = useCallback(async (data: RegisterCredentials) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await authServiceRegister(data);
      
      if (result.success && result.data) {
        setUser(result.data.user);
        setToken(result.data.token);
        return { success: true };
      } else {
        const errorMessage = result.error || 'Registration failed';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [setUser, setToken]);

  const logout = useCallback(() => {
    clearAuth();
    setError(null);
  }, [clearAuth]);

  const checkAuth = useCallback(async () => {
    // For now, just check if we have a token
    // In a real app, you might want to validate the token with the server
    setIsLoading(false);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Check auth on mount
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    token,
    login,
    register,
    logout,
    checkAuth,
    clearError,
  };
}