/**
 * Simple Auth Hook - No complex dependencies
 * For systematic restoration of the app
 */

import { useState, useCallback } from 'react';

export interface User {
  id: string;
  email: string;
  name?: string;
}

export interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  checkAuth: () => Promise<void>;
  clearError: () => void;
}

export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const login = useCallback(async (email: string, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Simple mock login for testing
      if (email && password) {
        const mockUser: User = {
          id: '1',
          email,
          name: email.split('@')[0],
        };
        setUser(mockUser);
        setIsLoading(false);
        return { success: true };
      } else {
        setError('Invalid credentials');
        setIsLoading(false);
        return { success: false, error: 'Invalid credentials' };
      }
    } catch (err) {
      setError('Login failed');
      setIsLoading(false);
      return { success: false, error: 'Login failed' };
    }
  }, []);

  const logout = useCallback(() => {
    setUser(null);
    setError(null);
  }, []);

  const checkAuth = useCallback(async () => {
    // Simple mock auth check
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 100);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    user,
    isAuthenticated: !!user,
    isLoading,
    error,
    login,
    logout,
    checkAuth,
    clearError,
  };
}