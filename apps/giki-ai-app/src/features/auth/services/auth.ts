/**
 * Authentication Token Management
 *
 * Manages JWT tokens in localStorage for authentication
 */

const ACCESS_TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';

/**
 * Get the access token from localStorage
 */
export function getAccessToken(): string | null {
  try {
    const token = localStorage.getItem(ACCESS_TOKEN_KEY);
    console.log('[Auth] getAccessToken called', {
      hasToken: !!token,
      tokenLength: token?.length || 0,
      tokenStart: token ? token.substring(0, 20) + '...' : 'null',
    });
    
    // Validate token format before returning (JWT should have 3 parts separated by dots)
    if (token && typeof token === 'string') {
      const tokenParts = token.split('.');
      if (tokenParts.length !== 3) {
        console.log('[Auth] getAccessToken: Invalid JWT format detected, clearing token');
        localStorage.removeItem(ACCESS_TOKEN_KEY);
        return null;
      }
    }
    
    return token;
  } catch (error) {
    console.log('[Auth] getAccessToken error:', error);
    // Gracefully handle localStorage errors in private browsing mode
    return null;
  }
}

/**
 * Set the access token in localStorage
 */
export function setAccessToken(token: string): void {
  try {
    // Validate token format before storing (JWT should have 3 parts separated by dots)
    if (!token || typeof token !== 'string') {
      console.log('[Auth] setAccessToken error: Invalid token type');
      return;
    }
    
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3) {
      console.log('[Auth] setAccessToken error: Invalid JWT format - expected 3 segments, got', tokenParts.length);
      return;
    }
    
    console.log('[Auth] setAccessToken called', {
      tokenLength: token.length,
      tokenStart: token.substring(0, 20) + '...',
    });
    localStorage.setItem(ACCESS_TOKEN_KEY, token);
    console.log('[Auth] setAccessToken success - token stored');
  } catch (error) {
    console.log('[Auth] setAccessToken error:', error);
    // Gracefully handle localStorage errors in private browsing mode
  }
}

/**
 * Get the refresh token from localStorage
 */
export function getRefreshToken(): string | null {
  try {
    return localStorage.getItem(REFRESH_TOKEN_KEY);
  } catch {
    // Gracefully handle localStorage errors in private browsing mode
    return null;
  }
}

/**
 * Set the refresh token in localStorage
 */
export function setRefreshToken(token: string): void {
  try {
    localStorage.setItem(REFRESH_TOKEN_KEY, token);
  } catch {
    // Gracefully handle localStorage errors in private browsing mode
  }
}

/**
 * Clear all tokens from localStorage
 */
export function clearTokens(): void {
  try {
    localStorage.removeItem(ACCESS_TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
  } catch {
    // Gracefully handle localStorage errors in private browsing mode
  }
}

/**
 * Check if user is authenticated (has valid access token)
 */
export function isAuthenticated(): boolean {
  const token = getAccessToken();
  if (!token) return false;

  try {
    // Parse JWT to check expiration
    // Properly type the JWT payload
    interface JWTPayload {
      exp: number;
      [key: string]: unknown;
    }
    const payload = JSON.parse(atob(token.split('.')[1])) as JWTPayload;
    const now = Math.floor(Date.now() / 1000);
    return payload.exp > now;
  } catch {
    // Token is invalid or malformed, consider user unauthenticated
    return false;
  }
}
