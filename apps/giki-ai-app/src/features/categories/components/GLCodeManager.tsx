import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Badge } from '@/shared/components/ui/badge';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Loader2,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Lightbulb,
  Clock,
  TrendingUp,
  Building2,
  Zap,
  Target,
} from 'lucide-react';
import type { Category } from '@/shared/types/categorization';
import type {
  GLCodeSuggestion,
  GLCodeValidation,
} from '@/shared/types/category';
import { GLCodeSuggestionItem } from './GLCodeSuggestionItem';

interface GLCodeManagerProps {
  category: Category;
  onUpdate: (updatedCategory: Category) => void;
  industry?: string; // M3 Giki: Industry context for enhanced suggestions
  tenantId?: number; // For accessing tenant-specific GL templates
}

const GLCodeManagerComponent: React.FC<GLCodeManagerProps> = ({
  category,
  onUpdate,
  industry = 'general_business',
  tenantId,
}) => {
  const [glCode, setGlCode] = useState(category.gl_code || '');
  const [glAccountName, setGlAccountName] = useState(
    category.gl_account_name || '',
  );
  const [glAccountType, setGlAccountType] = useState(
    category.gl_account_type || 'Expense',
  );
  const [validation, setValidation] = useState<GLCodeValidation | null>(null);
  const [suggestions, setSuggestions] = useState<GLCodeSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [_isValidating, setIsValidating] = useState(false);
  const [isGeneratingSuggestions, setIsGeneratingSuggestions] = useState(false);
  const [validationStatus, setValidationStatus] = useState<
    'idle' | 'validating' | 'valid' | 'invalid' | 'warning'
  >('idle');
  const [realTimeFeedback, setRealTimeFeedback] = useState<string>('');
  const [lastValidationTime, setLastValidationTime] = useState<number>(0);

  // M3 Giki Enhancement: Industry-specific state
  interface IndustryTemplate {
    name: string;
    gl_range: string;
  }
  const [industryTemplates, setIndustryTemplates] = useState<
    IndustryTemplate[]
  >([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');

  // Debounced real-time GL code validation with caching
  const validateGLCode = useCallback(async (code: string) => {
    if (!code || code.length < 2) {
      setValidationStatus('idle');
      setRealTimeFeedback('');
      return;
    }

    setIsValidating(true);
    setValidationStatus('validating');
    setRealTimeFeedback('Validating GL code...');

    const startTime = Date.now();

    try {
      // TODO: Implement GL code validation in glCodeService
      // Temporarily stubbed until API is implemented
      const result = {
        is_valid: code.length >= 4 && /^\d+$/.test(code),
        error_message: code.length < 4 ? 'GL code must be at least 4 digits' : '',
        warnings: [],
        suggested_code: null
      };
      const validationTime = Date.now() - startTime;

      if ('is_valid' in result) {
        setValidation(result);
        setLastValidationTime(validationTime);

        if (result.is_valid) {
          setValidationStatus('valid');
          setRealTimeFeedback(
            `□ Valid GL code (${validationTime}ms${validationTime < 50 ? ' - cached' : ''})`,
          );
        } else if (result.warnings && result.warnings.length > 0) {
          setValidationStatus('warning');
          setRealTimeFeedback(`⚠ ${result.warnings[0]} (${validationTime}ms)`);
        } else {
          setValidationStatus('invalid');
          setRealTimeFeedback(
            `✗ ${result.error_message || 'Invalid GL code'} (${validationTime}ms)`,
          );
        }

        // Auto-suggest improvements if validation fails
        if (!result.is_valid && result.suggested_code) {
          setRealTimeFeedback(
            (prev) => `${prev} - Try: ${result.suggested_code}`,
          );
        }
      }
    } catch {
      setValidationStatus('invalid');
      setRealTimeFeedback(
        '⚠ Validation failed - please check network connection',
      );
    } finally {
      setIsValidating(false);
    }
  }, []);

  // Debounced validation for real-time feedback
  const [validationTimeout, setValidationTimeout] = useState<number | null>(
    null,
  );
  const debouncedValidate = useCallback(
    (code: string) => {
      if (validationTimeout) {
        clearTimeout(validationTimeout);
      }

      const newTimeout = window.setTimeout(() => {
        void validateGLCode(code);
      }, 300); // 300ms debounce for responsive feedback

      setValidationTimeout(newTimeout);
    },
    [validateGLCode, validationTimeout],
  );

  // M3 Giki Enhancement: Load industry templates
  const loadIndustryTemplates = useCallback(async () => {
    if (!industry) return;

    setIsLoadingTemplates(true);
    setRealTimeFeedback('Loading industry-specific GL templates...');

    try {
      // TODO: Implement industry templates in glCodeService
      // Temporarily stubbed until API is implemented
      const result = { templates: [] as IndustryTemplate[] };
      setIndustryTemplates(result.templates || []);
      setRealTimeFeedback(
        `□ Loaded ${result.templates?.length || 0} ${industry.replace('_', ' ')} industry templates`,
      );
    } catch {
      setRealTimeFeedback('⚠ Failed to load industry templates');
    } finally {
      setIsLoadingTemplates(false);
    }
  }, [industry]);

  // Enhanced suggestions with M3 Giki industry context
  const getSuggestions = useCallback(async () => {
    setIsGeneratingSuggestions(true);
    setRealTimeFeedback(
      'Generating M3 Giki enhanced suggestions with industry intelligence...',
    );

    const startTime = Date.now();

    try {
      // TODO: Implement GL code suggestions in glCodeService
      // Temporarily stubbed until API is implemented
      const result = {
        suggestions: [] as GLCodeSuggestion[]
      };

      const suggestionTime = Date.now() - startTime;

      if (Array.isArray(result)) {
        setSuggestions(result);
        setRealTimeFeedback(
          `⚬ Generated ${result.length} industry-smart suggestions (${suggestionTime}ms${suggestionTime < 100 ? ' - cached' : ''})`,
        );
      } else {
        setRealTimeFeedback('⚠ No suggestions available for this category');
      }
    } catch {
      setRealTimeFeedback(
        '⚠ Failed to generate suggestions - please try again',
      );
    } finally {
      setIsGeneratingSuggestions(false);
    }
  }, [category.name, category.path, glAccountType, industry, tenantId]);

  const handleSave = useCallback(async () => {
    setIsLoading(true);
    // TODO: Implement GL code mapping update in glCodeService
    // Temporarily stubbed until API is implemented
    const result = {
      id: category.id,
      gl_code: glCode,
      gl_account_name: glAccountName,
      gl_account_type: glAccountType
    };

    if ('id' in result) {
      onUpdate(result);
    }
    setIsLoading(false);
  }, [category.id, glCode, glAccountName, glAccountType, onUpdate]);

  // Handle GL code input changes with real-time validation
  const handleGLCodeChange = useCallback((value: string) => {
    setGlCode(value);
    debouncedValidate(value);
  }, [debouncedValidate]);

  // M3 Giki: Load industry templates on mount
  useEffect(() => {
    void loadIndustryTemplates();
  }, [loadIndustryTemplates]);

  // Auto-suggest when category, account type, or industry changes
  useEffect(() => {
    if (category.name && glAccountType) {
      void getSuggestions();
    }
  }, [category.name, glAccountType, industry, getSuggestions]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (validationTimeout) {
        clearTimeout(validationTimeout);
      }
    };
  }, [validationTimeout]);

  return (
    <Card className="w-full border border-gray-200 shadow-sm overflow-hidden">
      <div className="bg-brand-primary text-white px-6 py-4">
        <div className="flex items-center justify-between">
          <h3 className="text-base font-semibold flex items-center gap-2">
            <Target className="w-4 h-4" />
            M3 Giki GL Code Management
          </h3>
          <Badge
            variant="outline"
            className="bg-white/10 text-white border-white/20"
          >
            {industry.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>
      </div>
      <div className="p-6 space-y-4">
        {/* M3 Giki Industry Template Selection */}
        {industryTemplates.length > 0 && (
          <div className="bg-gradient-to-r from-blue-50 to-green-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 mb-3">
              <Building2 className="w-4 h-4 text-blue-600" />
              <Label className="font-medium text-blue-900">
                Industry-Specific GL Templates
              </Label>
              <Badge
                variant="outline"
                className="bg-blue-100 text-blue-800 border-blue-300"
              >
                M3 Giki Enhanced
              </Badge>
            </div>
            <Select
              value={selectedTemplate}
              onValueChange={setSelectedTemplate}
            >
              <SelectTrigger className="bg-white border-blue-300 focus:ring-blue-500">
                <SelectValue placeholder="Choose an industry template for instant setup" />
              </SelectTrigger>
              <SelectContent>
                {industryTemplates.map((template, index) => (
                  <SelectItem key={index} value={template.name}>
                    <div className="flex items-center gap-2">
                      <Zap className="w-3 h-3 text-yellow-500" />
                      {template.name} - {template.gl_range}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedTemplate && (
              <div className="mt-2 p-2 bg-white rounded border border-blue-200">
                <p className="text-xs text-blue-700">
                  Template will auto-populate GL code and account details for
                  &ldquo;
                  {selectedTemplate}&rdquo;
                </p>
              </div>
            )}
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="gl-code" className="flex items-center gap-2">
              GL Code
              {validationStatus === 'validating' && (
                <Loader2 className="w-3 h-3 animate-spin text-gray-500" />
              )}
              {validationStatus === 'valid' && (
                <CheckCircle2 className="w-3 h-3 text-success" />
              )}
              {validationStatus === 'invalid' && (
                <XCircle className="w-3 h-3 text-error" />
              )}
              {validationStatus === 'warning' && (
                <AlertTriangle className="w-3 h-3 text-yellow-600" />
              )}
            </Label>
            <div className="relative">
              <Input
                id="gl-code"
                value={glCode}
                onChange={(e) => handleGLCodeChange(e?.target?.value || '')}
                placeholder="e.g., 6001 - type to validate instantly"
                className={`pr-8 ${
                  validationStatus === 'valid'
                    ? 'border-green-500 focus:ring-green-500'
                    : validationStatus === 'invalid'
                      ? 'border-red-500 focus:ring-red-500'
                      : validationStatus === 'warning'
                        ? 'border-yellow-500 focus:ring-yellow-500'
                        : ''
                }`}
              />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                {validationStatus === 'validating' && (
                  <Loader2 className="w-4 h-4 animate-spin text-gray-400" />
                )}
                {validationStatus === 'valid' && (
                  <CheckCircle2 className="w-4 h-4 text-success" />
                )}
                {validationStatus === 'invalid' && (
                  <XCircle className="w-4 h-4 text-error" />
                )}
                {validationStatus === 'warning' && (
                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                )}
              </div>
            </div>
            {validation && !validation.is_valid && (
              <Alert className="mt-2">
                <AlertDescription>
                  {validation.errors?.join(', ') || 'Invalid GL code'}
                </AlertDescription>
              </Alert>
            )}
          </div>

          <div>
            <Label htmlFor="account-name">Account Name</Label>
            <Input
              id="account-name"
              value={glAccountName}
              onChange={(e) => setGlAccountName(e?.target?.value)}
              placeholder="e.g., Office Supplies"
            />
          </div>

          <div>
            <Label htmlFor="account-type">Account Type</Label>
            <Select
              value={glAccountType}
              onValueChange={(value) => setGlAccountType(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select account type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Asset">Asset</SelectItem>
                <SelectItem value="Liability">Liability</SelectItem>
                <SelectItem value="Equity">Equity</SelectItem>
                <SelectItem value="Revenue">Revenue</SelectItem>
                <SelectItem value="Expense">Expense</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Real-time feedback panel */}
        {realTimeFeedback && (
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border">
            <div className="flex-shrink-0">
              {validationStatus === 'validating' || isGeneratingSuggestions ? (
                <Clock className="w-4 h-4 text-blue-500" />
              ) : validationStatus === 'valid' ? (
                <TrendingUp className="w-4 h-4 text-success" />
              ) : validationStatus === 'invalid' ? (
                <AlertTriangle className="w-4 h-4 text-error" />
              ) : (
                <Lightbulb className="w-4 h-4 text-yellow-600" />
              )}
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-700">
                {realTimeFeedback}
              </p>
              {lastValidationTime > 0 && validationStatus !== 'validating' && (
                <p className="text-xs text-gray-500 mt-1">
                  Performance:{' '}
                  {lastValidationTime < 50
                    ? 'Excellent (cached)'
                    : lastValidationTime < 200
                      ? 'Fast'
                      : 'Standard'}{' '}
                  response time
                </p>
              )}
            </div>
          </div>
        )}

        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={() => void loadIndustryTemplates()}
            disabled={isLoadingTemplates}
            className="border-blue-500 text-blue-600 hover:bg-blue-50 flex items-center gap-2"
          >
            {isLoadingTemplates ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Loading Templates...
              </>
            ) : (
              <>
                <Building2 className="w-4 h-4" />
                Refresh Industry Templates
              </>
            )}
          </Button>
          <Button
            variant="outline"
            onClick={() => void getSuggestions()}
            disabled={isGeneratingSuggestions}
            className="border-brand-primary text-brand-primary hover:bg-green-50 flex items-center gap-2"
          >
            {isGeneratingSuggestions ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Target className="w-4 h-4" />
                Get M3 Enhanced Suggestions
              </>
            )}
          </Button>
          <Button
            onClick={() => void handleSave()}
            disabled={isLoading}
            className="bg-brand-primary hover:bg-brand-primary-hover text-white"
          >
            Save GL Mapping
          </Button>
        </div>

        {suggestions.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-success" />
              <Label>M3 Giki Industry-Enhanced GL Suggestions</Label>
              <Badge
                variant="outline"
                className="text-xs bg-green-100 text-green-800 border-green-300"
              >
                {suggestions.length} intelligent options
              </Badge>
              <Badge
                variant="outline"
                className="text-xs bg-blue-100 text-blue-800 border-blue-300"
              >
                {industry.replace('_', ' ')} optimized
              </Badge>
            </div>
            <div className="space-y-2">
              {suggestions.map((suggestion, index) => (
                <GLCodeSuggestionItem
                  key={`${suggestion.gl_code}-${index}`}
                  suggestion={suggestion}
                  onApply={(appliedSuggestion) => {
                    setGlCode(appliedSuggestion.gl_code);
                    setGlAccountName(appliedSuggestion.gl_account_name);
                    setGlAccountType(appliedSuggestion.gl_account_type);
                    // Trigger validation for the new code
                    debouncedValidate(appliedSuggestion.gl_code);
                    setRealTimeFeedback(
                      `Applied suggestion: ${appliedSuggestion.gl_code} - ${appliedSuggestion.gl_account_name}`,
                    );
                  }}
                />
              ))}
            </div>
            <div className="text-xs text-gray-500 flex items-center gap-1 bg-gray-50 p-2 rounded">
              <TrendingUp className="w-3 h-3" />
              M3 Giki powered by Redis caching and industry templates for
              &lt;50ms response times
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

// Memoize the component to prevent unnecessary re-renders
// Only re-render when category, onUpdate callback, industry, or tenantId change
export const GLCodeManager = React.memo(GLCodeManagerComponent, (prevProps, nextProps) => {
  // Custom comparison function for deep equality check on category object
  return (
    prevProps.category.id === nextProps.category.id &&
    prevProps.category.gl_code === nextProps.category.gl_code &&
    prevProps.category.gl_account_name === nextProps.category.gl_account_name &&
    prevProps.category.gl_account_type === nextProps.category.gl_account_type &&
    prevProps.category.name === nextProps.category.name &&
    prevProps.category.path === nextProps.category.path &&
    prevProps.onUpdate === nextProps.onUpdate &&
    prevProps.industry === nextProps.industry &&
    prevProps.tenantId === nextProps.tenantId
  );
});

export default GLCodeManager;
