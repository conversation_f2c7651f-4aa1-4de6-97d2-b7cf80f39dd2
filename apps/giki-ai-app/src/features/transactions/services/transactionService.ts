// apps/giki-ai-app/src/services/transactionService.ts
import { apiClient } from '@/shared/services/api/apiClient';
import {
  CategorizationStatus,
  type Transaction,
} from '@/shared/types/categorization';
import { logger, handleApiError, ApiError } from '@/shared/utils/errorHandling';

// These types are moved from lib/api.ts. Consider centralizing them in @/shared/types/api.ts later.

export interface ConfidenceDistribution {
  high: number;
  medium: number;
  low: number;
  manual: number;
}

export interface TransactionStats {
  total_count: number;
  categorized_count: number;
  uncategorized_count: number;
  total_amount: number;
  confidence_distribution: ConfidenceDistribution;
  category_breakdown?: Array<{
    category_id: number;
    category_name: string;
    count: number;
    amount: number;
  }>;
}

export interface FetchTransactionsParams {
  uploadId?: string;
  withAiSuggestions?: boolean;
  startDate?: string;
  endDate?: string;
  status?: string; // e.g., 'needs_review', 'categorized', 'all'
  categoryId?: string;
  searchTerm?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  minAmount?: number;
  maxAmount?: number;
  useFastPagination?: boolean; // Flag to use cursor-based pagination
  cursor?: string; // Cursor for fast pagination
}

export interface PaginatedResponse<T> {
  items: T[];
  total_count: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface FastPaginatedResponse<T> {
  items: T[];
  total_count: number; // Will be -1 for fast pagination
  page: number;
  page_size: number;
  total_pages: number; // Will be -1 for fast pagination
  next_cursor?: string; // Cursor for next page
  has_more?: boolean; // Whether there are more pages
}

export type FetchTransactionsResponse = PaginatedResponse<Transaction>;

export type FastFetchTransactionsResponse = FastPaginatedResponse<Transaction>;

export const fetchTransactions = async (
  params: FetchTransactionsParams = {},
): Promise<
  Transaction[] | FetchTransactionsResponse | FastFetchTransactionsResponse
> => {
 (
    '[transactionService] fetchTransactions called with params:',
    params,
  );
 ('[transactionService] Current localStorage tokens:', {
    accessToken: localStorage.getItem('access_token') ? 'EXISTS' : 'MISSING',
    refreshToken: localStorage.getItem('refresh_token') ? 'EXISTS' : 'MISSING',
  });
  try {
    // Build query parameters
    const queryParams: Record<string, string> = {};

    // Determine which endpoint to use
    const useFastEndpoint =
      params.useFastPagination ||
      (!params.startDate &&
        !params.endDate &&
        !params.status &&
        !params.categoryId &&
        !params.searchTerm &&
        !params.minAmount &&
        !params.maxAmount &&
        !params.sortBy &&
        !params.sortDirection);

    if (useFastEndpoint) {
      // Use fast endpoint with cursor-based pagination (no filters supported)
      if (params.pageSize) {
        queryParams.limit = params.pageSize.toString();
      }
      if (params.cursor) {
        queryParams.cursor = params.cursor;
      }
    } else {
      // Use regular endpoint with full filtering but slower performance
      if (params.uploadId) queryParams.upload_id = params.uploadId;
      if (params.withAiSuggestions) queryParams.with_ai_suggestions = 'true';
      if (params.startDate) queryParams.start_date = params.startDate;
      if (params.endDate) queryParams.end_date = params.endDate;
      if (params.status && params.status !== 'all')
        queryParams.status = params.status;
      if (params.categoryId && params.categoryId !== 'all_categories')
        queryParams.category_id = params.categoryId;
      if (params.searchTerm) queryParams.search_term = params.searchTerm;
      if (params.page) queryParams.page = params.page.toString();
      if (params.pageSize) {
        // Backend expects 'limit' parameter, not 'page_size'
        // Use the requested page size without arbitrary limits
        queryParams.limit = params.pageSize.toString();
      }
      if (params.sortBy) queryParams.sort_by = params.sortBy;
      if (params.sortDirection)
        queryParams.sort_direction = params.sortDirection;
      if (params.minAmount !== undefined)
        queryParams.min_amount = params.minAmount.toString();
      if (params.maxAmount !== undefined)
        queryParams.max_amount = params.maxAmount.toString();

      // Skip count for faster response when possible
      queryParams.skip_count = 'true';
    }

    // Choose endpoint based on performance needs
    const endpoint = useFastEndpoint ? '/transactions/fast' : '/transactions/';

    // Make the API request
    const response = await apiClient.get<
      Transaction[] | FetchTransactionsResponse | FastFetchTransactionsResponse
    >(endpoint, {
      params: queryParams,
    });

    const data = response.data;
   ('[transactionService] API response:', data);
    logger.debug('Transaction API response', 'transactionService', data);

    // Process the response
    if (data && 'items' in data && Array.isArray(data.items)) {
      const processedItems = data.items.map((item) => {
        if (!item.status) {
          if (item.is_user_modified) {
            item.status = CategorizationStatus.USER_MODIFIED;
          } else if (
            item.ai_suggested_category_path ||
            item.ai_suggested_category_id
          ) {
            item.status = CategorizationStatus.AI_SUGGESTED;
          } else {
            item.status = CategorizationStatus.UNCATEGORIZED;
          }
        }
        return item;
      });
      return { ...data, items: processedItems } as FetchTransactionsResponse;
    }

    if (Array.isArray(data)) {
      const processedItems = data.map((item) => {
        if (!item.status) {
          if (item.is_user_modified) {
            item.status = CategorizationStatus.USER_MODIFIED;
          } else if (
            item.ai_suggested_category_path ||
            item.ai_suggested_category_id
          ) {
            item.status = CategorizationStatus.AI_SUGGESTED;
          } else {
            item.status = CategorizationStatus.UNCATEGORIZED;
          }
        }
        return item;
      });
      return {
        items: processedItems,
        total_count: processedItems.length,
        page: params.page || 1,
        page_size: params.pageSize || processedItems.length,
        total_pages: 1,
      } as FetchTransactionsResponse;
    }

    logger.error(
      'Unexpected API response format',
      'transactionService',
      new Error('Unexpected response format'),
    );
    throw new Error('Unexpected response format from transactions API');
  } catch (error) {
    logger.error(
      'Error in fetchTransactions',
      'transactionService',
      error as Error,
    );
    // apiClient handles error creation and throws AppError instances.
    // Re-throwing allows the caller to handle it.
    throw error;
  }
};

export const fetchTransaction = async (
  transactionId: string,
): Promise<Transaction> => {
  try {
    const response = await apiClient.get<Transaction>(
      `/transactions/${transactionId}`,
    );

    const data = response.data;
    logger.debug(
      'Transaction details API response',
      'transactionService',
      data,
    );

    // Ensure the response has the required status field with enhanced logic
    if (!data.status) {
      if (data.is_user_modified) {
        data.status = CategorizationStatus.USER_MODIFIED;
      } else if (
        data.ai_suggested_category_path ||
        data.ai_suggested_category_id
      ) {
        data.status = CategorizationStatus.AI_SUGGESTED;
      } else {
        data.status = CategorizationStatus.UNCATEGORIZED;
      }
    }

    // Add confidence level indicator
    if (data.ai_category_confidence !== undefined) {
      data.confidence_level = getConfidenceLevel(data.ai_category_confidence);
    }

    return data;
  } catch (error) {
    logger.error(
      'Error in fetchTransaction',
      'transactionService',
      error as Error,
    );
    // apiClient handles error creation and throws AppError instances.
    // Re-throwing allows the caller to handle it.
    throw error;
  }
};

export const updateTransactionCategory = async (
  transactionId: string,
  categoryId: number,
): Promise<Transaction> => {
  try {
    logger.info('Updating transaction with category', 'transactionService', {
      transactionId,
      categoryId,
    });

    const response = await apiClient.put<Transaction>(
      `/transactions/${transactionId}/category`,
      { category_id: categoryId },
    );

    const data = response.data;
    logger.debug(
      'Update transaction category response',
      'transactionService',
      data,
    );

    // Ensure the response has the required status field
    if (!data.status) {
      data.status = CategorizationStatus.USER_MODIFIED;
    }

    return data;
  } catch (error) {
    logger.error(
      'Error in updateTransactionCategory',
      'transactionService',
      error as Error,
    );
    // apiClient handles error creation and throws AppError instances.
    // Re-throwing allows the caller to handle it.
    throw error;
  }
};

export interface BatchCategoryUpdateParams {
  transaction_ids: string[];
  category_id: number | null;
  use_ai?: boolean;
  rag_corpus_id?: string | null;
  confidence_threshold?: number;
}

// Enhanced types for review and approval workflow
export interface ReviewQueueParams {
  confidence_threshold?: number;
  limit?: number;
  category_id?: string;
  amount_range?: { min?: number; max?: number };
  date_range?: { start?: string; end?: string };
}

export interface ReviewQueueResponse {
  review_queue: Array<{
    id: string;
    description: string;
    amount: number;
    date: string;
    suggested_categories: Array<{
      name: string;
      confidence: number;
      category_id: string;
    }>;
    flags: string[];
    ai_reasoning?: string;
  }>;
  queue_size: number;
  average_confidence: number;
  recommendations?: {
    high_priority: string[];
    bulk_actions: string[];
  };
}

export interface BulkApprovalParams {
  transaction_ids: string[];
  approval_notes?: string;
  auto_apply_suggestions?: boolean;
}

export interface BulkApprovalResponse {
  approved_count: number;
  failed_count: number;
  errors?: Array<{
    transaction_id: string;
    error: string;
  }>;
  summary: {
    total_amount: number;
    categories_affected: string[];
    processing_time_ms: number;
  };
}

export interface TransactionUpdateParams {
  category_id?: number;
  confidence?: number;
  notes?: string;
  manual_override?: boolean;
}

export interface TransactionConfidenceDistribution {
  high_confidence: number; // >= 0.9
  medium_confidence: number; // 0.7 - 0.89
  low_confidence: number; // < 0.7
  total: number;
}

interface TransactionStatsExtended {
  total_transactions: number;
  needs_review: number;
  approved: number;
  confidence_distribution: {
    high_confidence: number;
    medium_confidence: number;
    low_confidence: number;
    total: number;
  };
  processing_summary: {
    avg_confidence: number;
    top_categories: Array<{
      name: string;
      count: number;
      avg_confidence: number;
    }>;
  };
}

export const updateBatchCategories = async (
  params: BatchCategoryUpdateParams,
): Promise<{ success: boolean; message: string }> => {
  try {
    logger.info('Batch updating transactions', 'transactionService', {
      count: params.transaction_ids.length,
      use_ai: params.use_ai,
      category_id: params.category_id,
    });

    const response = await apiClient.put<{ success: boolean; message: string }>(
      '/transactions/batch/category',
      params,
    );

    logger.debug('Batch update response', 'transactionService', response.data);
    return response.data;
  } catch (error) {
    logger.error(
      'Error in updateBatchCategories',
      'transactionService',
      error as Error,
    );
    // apiClient handles error creation and throws AppError instances.
    // Re-throwing allows the caller to handle it.
    throw error;
  }
};

/**
 * Get transactions needing review based on confidence thresholds
 */
export const getReviewQueue = async (
  params: ReviewQueueParams = {},
): Promise<ReviewQueueResponse | ApiError> => {
  try {
    const queryParams: Record<string, string> = {};

    if (params.confidence_threshold !== undefined) {
      queryParams.confidence_threshold = params.confidence_threshold.toString();
    }
    if (params.limit) {
      queryParams.limit = params.limit.toString();
    }
    if (params.category_id) {
      queryParams.category_id = params.category_id;
    }
    if (params.amount_range?.min !== undefined) {
      queryParams.min_amount = params.amount_range.min.toString();
    }
    if (params.amount_range?.max !== undefined) {
      queryParams.max_amount = params.amount_range.max.toString();
    }
    if (params.date_range?.start) {
      queryParams.start_date = params.date_range.start;
    }
    if (params.date_range?.end) {
      queryParams.end_date = params.date_range.end;
    }

    const response = await apiClient.get<ReviewQueueResponse>(
      '/transactions/review-queue',
      { params: queryParams },
    );

    logger.debug('Review queue response', 'transactionService', response.data);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getReviewQueue',
      defaultMessage: 'Failed to get review queue.',
    });
  }
};

/**
 * Bulk approve transactions
 */
export const bulkApproveTransactions = async (
  params: BulkApprovalParams,
): Promise<BulkApprovalResponse | ApiError> => {
  try {
    logger.info('Bulk approving transactions', 'transactionService', {
      count: params.transaction_ids.length,
      notes: params.approval_notes,
    });

    const response = await apiClient.post<BulkApprovalResponse>(
      '/transactions/bulk-approve',
      params,
    );

    logger.debug('Bulk approval response', 'transactionService', response.data);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'bulkApproveTransactions',
      defaultMessage: 'Failed to approve transactions.',
    });
  }
};

/**
 * Update single transaction category with enhanced parameters
 */
export const updateTransactionCategoryEnhanced = async (
  transactionId: string,
  params: TransactionUpdateParams,
): Promise<Transaction | ApiError> => {
  try {
    logger.info(
      'Updating transaction category (enhanced)',
      'transactionService',
      {
        transactionId,
        params,
      },
    );

    const response = await apiClient.put<Transaction>(
      `/transactions/${transactionId}/category`,
      params,
    );

    const data = response.data;

    // Ensure the response has the required status field
    if (!data.status) {
      data.status = CategorizationStatus.USER_MODIFIED;
    }

    logger.debug('Enhanced update response', 'transactionService', data);
    return data;
  } catch (error) {
    return handleApiError(error, {
      context: 'updateTransactionCategoryEnhanced',
      defaultMessage: 'Failed to update transaction category.',
    });
  }
};

/**
 * Get transaction statistics for dashboard
 */
export const getTransactionStats = async (dateRange?: {
  start: string;
  end: string;
}): Promise<TransactionStats | ApiError> => {
  try {
    const queryParams: Record<string, string> = {};

    if (dateRange?.start) {
      queryParams.start_date = dateRange.start;
    }
    if (dateRange?.end) {
      queryParams.end_date = dateRange.end;
    }

    const response = await apiClient.get<TransactionStats>(
      '/transactions/stats',
      { params: queryParams },
    );

    logger.debug(
      'Transaction stats response',
      'transactionService',
      response.data,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getTransactionStats',
      defaultMessage: 'Failed to get transaction statistics.',
    });
  }
};

/**
 * Get confidence distribution for transactions
 */
export const getConfidenceDistribution = async (filters?: {
  category_id?: string;
  date_range?: { start: string; end: string };
}): Promise<ConfidenceDistribution | ApiError> => {
  try {
    const response = await getTransactionStats(filters?.date_range);

    if ('error' in response) {
      return response as ApiError;
    }

    // Check if confidence_distribution exists in the response
    if (
      'confidence_distribution' in response &&
      response.confidence_distribution
    ) {
      return response.confidence_distribution;
    }

    // Return a default confidence distribution if not found
    return {
      high: 0,
      medium: 0,
      low: 0,
      manual: 0,
    };
  } catch (error) {
    return handleApiError(error, {
      context: 'getConfidenceDistribution',
      defaultMessage: 'Failed to get confidence distribution.',
    });
  }
};

/**
 * Real-time transaction processing status
 */
export const getTransactionProcessingStatus = async (
  uploadId?: string,
): Promise<
  | {
      status: 'processing' | 'completed' | 'failed';
      progress: number;
      processed: number;
      total: number;
      current_operation: string;
      estimated_completion?: string;
    }
  | ApiError
> => {
  try {
    const endpoint = uploadId
      ? `/transactions/processing-status/${uploadId}`
      : '/transactions/processing-status';

    const response = await apiClient.get<{
      status: 'processing' | 'completed' | 'failed';
      progress: number;
      processed: number;
      total: number;
      current_operation: string;
      estimated_completion?: string;
    }>(endpoint);

    logger.debug(
      'Processing status response',
      'transactionService',
      response.data,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getTransactionProcessingStatus',
      defaultMessage: 'Failed to get processing status.',
    });
  }
};

/**
 * Export transactions to various formats
 */
export const exportTransactions = async (
  format: 'csv' | 'excel' | 'pdf',
  filters?: {
    start_date?: string;
    end_date?: string;
    category_ids?: string[];
    status?: string;
    confidence_threshold?: number;
  },
): Promise<{ download_url: string; expires_at: string } | ApiError> => {
  try {
    const params = {
      format,
      ...filters,
      category_ids: filters?.category_ids?.join(','),
    };

    const response = await apiClient.post<{
      download_url: string;
      expires_at: string;
    }>('/transactions/export', params);

    logger.debug('Export response', 'transactionService', response.data);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'exportTransactions',
      defaultMessage: 'Failed to export transactions.',
    });
  }
};

/**
 * Utility function to format confidence as percentage
 */
export const formatConfidence = (confidence?: number): string => {
  if (confidence === undefined || confidence === null) return 'N/A';
  return `${Math.round(confidence * 100)}%`;
};

/**
 * Utility function to get confidence level
 */
export const getConfidenceLevel = (
  confidence?: number,
): 'high' | 'medium' | 'low' => {
  if (!confidence) return 'low';
  if (confidence >= 0.9) return 'high';
  if (confidence >= 0.7) return 'medium';
  return 'low';
};

/**
 * Utility function to determine if transaction needs review
 */
export const needsReview = (
  transaction: Transaction,
  threshold: number = 0.85,
): boolean => {
  const confidence = transaction.ai_category_confidence || 0;
  return confidence < threshold || !transaction.category_path;
};
