/**
 * Transaction Export Service
 *
 * Provides comprehensive export functionality for transactions in multiple formats:
 * - CSV: Simple tabular format
 * - Excel: With formatting, multiple sheets, and summaries
 * - PDF: Professional reports with charts and summaries
 */

import ExcelJS from 'exceljs';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import { format } from 'date-fns';
import { Transaction } from '@/shared/types/categorization';
import { Category } from '@/shared/services/base/ServiceFactory';

// Export format types
export type ExportFormat = 'csv' | 'excel' | 'pdf';

// Export options
export interface ExportOptions {
  format: ExportFormat;
  fileName?: string;
  includeMetadata?: boolean;
  includeSummary?: boolean;
  includeCharts?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  categories?: Category[];
  customColumns?: string[];
}

// Transaction summary for reports
interface TransactionSummary {
  totalTransactions: number;
  totalIncome: number;
  totalExpenses: number;
  netAmount: number;
  categorizedCount: number;
  uncategorizedCount: number;
  categorizationRate: number;
  dateRange: {
    start: string;
    end: string;
  };
  categoryBreakdown: {
    category: string;
    amount: number;
    count: number;
    percentage: number;
  }[];
}

/**
 * Calculate transaction summary statistics
 */
function calculateSummary(transactions: Transaction[]): TransactionSummary {
  const totalTransactions = transactions.length;

  let totalIncome = 0;
  let totalExpenses = 0;
  let categorizedCount = 0;

  const categoryMap = new Map<string, { amount: number; count: number }>();

  transactions.forEach((t) => {
    const amount = t.amount;
    if (t.transaction_type === 'credit') {
      totalIncome += amount;
    } else {
      totalExpenses += amount;
    }

    if (t.category_path && t.category_path !== 'Uncategorized') {
      categorizedCount++;
      const existing = categoryMap.get(t.category_path) || {
        amount: 0,
        count: 0,
      };
      categoryMap.set(t.category_path, {
        amount: existing.amount + amount,
        count: existing.count + 1,
      });
    }
  });

  const netAmount = totalIncome - totalExpenses;
  const uncategorizedCount = totalTransactions - categorizedCount;
  const categorizationRate =
    totalTransactions > 0 ? (categorizedCount / totalTransactions) * 100 : 0;

  // Sort categories by amount
  const categoryBreakdown = Array.from(categoryMap.entries())
    .map(([category, data]) => ({
      category,
      amount: data.amount,
      count: data.count,
      percentage: (data.amount / totalExpenses) * 100,
    }))
    .sort((a, b) => b.amount - a.amount);

  // Get date range
  const dates = transactions
    .map((t) => new Date(t.date))
    .sort((a, b) => a.getTime() - b.getTime());
  const dateRange = {
    start: dates.length > 0 ? format(dates[0], 'yyyy-MM-dd') : '',
    end: dates.length > 0 ? format(dates[dates.length - 1], 'yyyy-MM-dd') : '',
  };

  return {
    totalTransactions,
    totalIncome,
    totalExpenses,
    netAmount,
    categorizedCount,
    uncategorizedCount,
    categorizationRate,
    dateRange,
    categoryBreakdown,
  };
}

/**
 * Export transactions as CSV
 */
function exportCSV(transactions: Transaction[], options: ExportOptions): void {
  const headers = [
    'Date',
    'Description',
    'Amount',
    'Type',
    'Category',
    'Account',
    'Status',
    'AI Confidence',
    'Entity',
  ];

  const csvContent = [
    headers.join(','),
    ...transactions.map((t) =>
      [
        t.date,
        `"${t?.description?.replace(/"/g, '""')}"`, // Escape quotes
        t?.amount?.toFixed(2),
        t.transaction_type || '',
        t.category_path || 'Uncategorized',
        t.account || '',
        t.status || '',
        t.ai_category_confidence ? t?.ai_category_confidence?.toFixed(2) : '',
        t.merchant || '',
      ].join(','),
    ),
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const fileName =
    options.fileName || `transactions-${format(new Date(), 'yyyy-MM-dd')}.csv`;

  link.href = URL.createObjectURL(blob);
  link.download = fileName;
  link.click();
  URL.revokeObjectURL(link.href);
}

/**
 * Export transactions as Excel with multiple sheets
 */
async function exportExcel(
  transactions: Transaction[],
  options: ExportOptions,
): Promise<void> {
  const workbook = new ExcelJS.Workbook();

  // Main transactions sheet
  const worksheet = workbook.addWorksheet('Transactions');

  // Define columns with headers and formatting
  worksheet.columns = [
    { header: 'Date', key: 'date', width: 12 },
    { header: 'Description', key: 'description', width: 40 },
    {
      header: 'Amount',
      key: 'amount',
      width: 12,
      style: { numFmt: '#,##0.00' },
    },
    { header: 'Type', key: 'type', width: 10 },
    { header: 'Category', key: 'category', width: 25 },
    { header: 'Account', key: 'account', width: 20 },
    { header: 'Status', key: 'status', width: 15 },
    { header: 'AI Confidence', key: 'aiConfidence', width: 15 },
    { header: 'Entity', key: 'entity', width: 20 },
    { header: 'Is Categorized', key: 'isCategorized', width: 15 },
    { header: 'User Modified', key: 'userModified', width: 15 },
  ];

  // Add transaction data
  const transactionData = transactions.map((t) => ({
    date: t.date,
    description: t.description,
    amount: t.amount,
    type: t.transaction_type || '',
    category: t.category_path || 'Uncategorized',
    account: t.account || '',
    status: t.status || '',
    aiConfidence: t.ai_category_confidence || '',
    entity: t.merchant || '',
    isCategorized: t.is_categorized ? 'Yes' : 'No',
    userModified: t.is_user_modified ? 'Yes' : 'No',
  }));

  worksheet.addRows(transactionData);

  // Style the header row
  worksheet.getRow(1).font = { bold: true };
  worksheet.getRow(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFD0D0D0' },
  };

  // Add summary sheet if requested
  if (options.includeSummary) {
    const summary = calculateSummary(transactions);
    const summarySheet = workbook.addWorksheet('Summary');

    // Set column widths
    summarySheet.columns = [
      { width: 30 },
      { width: 15 },
      { width: 10 },
      { width: 12 },
    ];

    // Add summary data
    summarySheet.addRow(['Transaction Summary Report']);
    summarySheet.addRow([]);
    summarySheet.addRow([
      'Generated:',
      format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
    ]);
    summarySheet.addRow([]);
    summarySheet.addRow(['Overview']);
    summarySheet.addRow(['Total Transactions:', summary.totalTransactions]);
    summarySheet.addRow(['Total Income:', summary?.totalIncome?.toFixed(2)]);
    summarySheet.addRow([
      'Total Expenses:',
      summary?.totalExpenses?.toFixed(2),
    ]);
    summarySheet.addRow(['Net Amount:', summary?.netAmount?.toFixed(2)]);
    summarySheet.addRow([]);
    summarySheet.addRow(['Categorization']);
    summarySheet.addRow(['Categorized:', summary.categorizedCount]);
    summarySheet.addRow(['Uncategorized:', summary.uncategorizedCount]);
    summarySheet.addRow([
      'Categorization Rate:',
      `${summary?.categorizationRate?.toFixed(1)}%`,
    ]);
    summarySheet.addRow([]);
    summarySheet.addRow(['Date Range']);
    summarySheet.addRow(['Start Date:', summary?.dateRange?.start]);
    summarySheet.addRow(['End Date:', summary?.dateRange?.end]);
    summarySheet.addRow([]);
    summarySheet.addRow(['Category Breakdown']);
    summarySheet.addRow(['Category', 'Amount', 'Count', 'Percentage']);

    // Add category breakdown
    summary?.categoryBreakdown?.forEach((cat) => {
      summarySheet.addRow([
        cat.category,
        cat?.amount?.toFixed(2),
        cat?.count?.toString(),
        `${cat?.percentage?.toFixed(1)}%`,
      ]);
    });

    // Style the title row
    summarySheet.getRow(1).font = { bold: true, size: 14 };
    summarySheet.mergeCells('A1:D1');
    summarySheet.getRow(1).alignment = { horizontal: 'center' };

    // Style section headers
    [5, 11, 16, 20].forEach((rowNum) => {
      summarySheet.getRow(rowNum).font = { bold: true };
    });

    // Style category breakdown header
    summarySheet.getRow(20).font = { bold: true };
    summarySheet.getRow(21).font = { bold: true };
  }

  // Export the workbook
  const fileName =
    options.fileName || `transactions-${format(new Date(), 'yyyy-MM-dd')}.xlsx`;

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = fileName;
  link.click();
  URL.revokeObjectURL(link.href);
}

/**
 * Export transactions as PDF report
 */
function exportPDF(transactions: Transaction[], options: ExportOptions): void {
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
  });

  const summary = calculateSummary(transactions);
  const pageWidth = doc?.internal?.pageSize.getWidth();
  const pageHeight = doc?.internal?.pageSize.getHeight();
  const margin = 15;
  let yPosition = margin;

  // Title
  doc.setFontSize(20);
  doc.setTextColor(40, 40, 40);
  doc.text('Transaction Report', pageWidth / 2, yPosition, { align: 'center' });
  yPosition += 15;

  // Date and metadata
  doc.setFontSize(10);
  doc.setTextColor(100, 100, 100);
  doc.text(
    `Generated: ${format(new Date(), 'yyyy-MM-dd HH:mm:ss')}`,
    pageWidth / 2,
    yPosition,
    { align: 'center' },
  );
  yPosition += 10;

  if (options.includeSummary) {
    // Summary section
    doc.setFontSize(14);
    doc.setTextColor(40, 40, 40);
    doc.text('Summary', margin, yPosition);
    yPosition += 8;

    doc.setFontSize(10);
    doc.setTextColor(60, 60, 60);

    // Overview metrics
    const metrics = [
      ['Total Transactions:', summary?.totalTransactions?.toString()],
      ['Total Income:', `$${summary?.totalIncome?.toFixed(2)}`],
      ['Total Expenses:', `$${summary?.totalExpenses?.toFixed(2)}`],
      ['Net Amount:', `$${summary?.netAmount?.toFixed(2)}`],
      ['Categorization Rate:', `${summary?.categorizationRate?.toFixed(1)}%`],
      [
        'Date Range:',
        `${summary?.dateRange?.start} to ${summary?.dateRange?.end}`,
      ],
    ];

    metrics.forEach(([label, value]) => {
      doc.text(label, margin, yPosition);
      doc.text(value, margin + 50, yPosition);
      yPosition += 6;
    });

    yPosition += 10;

    // Category breakdown table
    if (summary?.categoryBreakdown?.length > 0) {
      doc.setFontSize(12);
      doc.setTextColor(40, 40, 40);
      doc.text('Top Categories by Spending', margin, yPosition);
      yPosition += 8;

      const categoryTableData = summary.categoryBreakdown
        .slice(0, 10)
        .map((cat) => [
          cat.category,
          `$${cat?.amount?.toFixed(2)}`,
          cat?.count?.toString(),
          `${cat?.percentage?.toFixed(1)}%`,
        ]);

      autoTable(doc, {
        startY: yPosition,
        head: [['Category', 'Amount', 'Count', '% of Total']],
        body: categoryTableData,
        theme: 'striped',
        headStyles: { fillColor: [66, 66, 66] },
        margin: { left: margin, right: margin },
        columnStyles: {
          0: { cellWidth: 60 },
          1: { cellWidth: 30, halign: 'right' },
          2: { cellWidth: 20, halign: 'center' },
          3: { cellWidth: 25, halign: 'right' },
        },
      });

      yPosition =
        (doc as unknown as { lastAutoTable: { finalY: number } }).lastAutoTable
          .finalY + 15;
    }
  }

  // Transaction details table
  doc.addPage();
  yPosition = margin;

  doc.setFontSize(14);
  doc.setTextColor(40, 40, 40);
  doc.text('Transaction Details', margin, yPosition);
  yPosition += 10;

  // Prepare transaction data for table
  const tableData = transactions.map((t) => [
    format(new Date(t.date), 'yyyy-MM-dd'),
    t?.description?.length > 40
      ? t?.description?.substring(0, 40) + '...'
      : t.description,
    `$${t?.amount?.toFixed(2)}`,
    t.transaction_type || '',
    (t.category_path || 'Uncategorized').length > 25
      ? (t.category_path || 'Uncategorized').substring(0, 25) + '...'
      : t.category_path || 'Uncategorized',
  ]);

  autoTable(doc, {
    startY: yPosition,
    head: [['Date', 'Description', 'Amount', 'Type', 'Category']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: [66, 66, 66],
      fontSize: 10,
    },
    bodyStyles: {
      fontSize: 9,
    },
    columnStyles: {
      0: { cellWidth: 25 },
      1: { cellWidth: 70 },
      2: { cellWidth: 25, halign: 'right' },
      3: { cellWidth: 20 },
      4: { cellWidth: 40 },
    },
    margin: { left: margin, right: margin },
    // Handle page breaks
    didDrawPage: (data) => {
      // Footer
      doc.setFontSize(8);
      doc.setTextColor(150, 150, 150);
      doc.text(
        `Page ${data.pageNumber} of ${doc.getNumberOfPages()}`,
        pageWidth / 2,
        pageHeight - 10,
        { align: 'center' },
      );
    },
  });

  // Save the PDF
  const fileName =
    options.fileName || `transactions-${format(new Date(), 'yyyy-MM-dd')}.pdf`;
  doc.save(fileName);
}

/**
 * Main export function
 */
export async function exportTransactions(
  transactions: Transaction[],
  options: ExportOptions,
): Promise<void> {
  if (!transactions || transactions.length === 0) {
    throw new Error('No transactions to export');
  }

  switch (options.format) {
    case 'csv':
      exportCSV(transactions, options);
      break;
    case 'excel':
      await exportExcel(transactions, options);
      break;
    case 'pdf':
      exportPDF(transactions, options);
      break;
    default:
      throw new Error(`Unsupported export format: ${String(options.format)}`);
  }
}

/**
 * Export transaction report with enhanced formatting
 */
export async function exportTransactionReport(
  transactions: Transaction[],
  options: Omit<ExportOptions, 'format'> & {
    format: 'excel' | 'pdf';
    reportTitle?: string;
    companyName?: string;
    includeExecutiveSummary?: boolean;
  },
): Promise<void> {
  const enhancedOptions = {
    ...options,
    includeSummary: true,
    includeMetadata: true,
    includeCharts: options.format === 'pdf',
  };

  if (options.format === 'excel') {
    // Create enhanced Excel report with charts
    const workbook = new ExcelJS.Workbook();

    // Executive summary sheet
    if (options.includeExecutiveSummary) {
      const summary = calculateSummary(transactions);
      const execSheet = workbook.addWorksheet('Executive Summary');

      // Set column width
      execSheet.columns = [{ width: 80 }];

      // Add executive summary content
      execSheet.addRow([options.companyName || 'Transaction Report']);
      execSheet.addRow([options.reportTitle || 'Financial Analysis']);
      execSheet.addRow([]);
      execSheet.addRow(['Executive Summary']);
      execSheet.addRow([]);
      execSheet.addRow([
        `This report contains an analysis of ${summary.totalTransactions} transactions`,
      ]);
      execSheet.addRow([
        `from ${summary?.dateRange?.start} to ${summary?.dateRange?.end}.`,
      ]);
      execSheet.addRow([]);
      execSheet.addRow(['Key Findings:']);
      execSheet.addRow([
        `• Total Income: $${summary?.totalIncome?.toFixed(2)}`,
      ]);
      execSheet.addRow([
        `• Total Expenses: $${summary?.totalExpenses?.toFixed(2)}`,
      ]);
      execSheet.addRow([
        `• Net Result: $${summary?.netAmount?.toFixed(2)} (${summary.netAmount >= 0 ? 'Profit' : 'Loss'})`,
      ]);
      execSheet.addRow([
        `• ${summary?.categorizationRate?.toFixed(1)}% of transactions are properly categorized`,
      ]);
      execSheet.addRow([]);
      execSheet.addRow(['Top Expense Categories:']);

      summary?.categoryBreakdown?.slice(0, 5).forEach((cat, index) => {
        execSheet.addRow([
          `${index + 1}. ${cat.category}: $${cat?.amount?.toFixed(2)} (${cat?.percentage?.toFixed(1)}%)`,
        ]);
      });

      // Style the executive summary
      execSheet.getRow(1).font = { bold: true, size: 16 };
      execSheet.getRow(2).font = { bold: true, size: 14 };
      execSheet.getRow(4).font = { bold: true, size: 12 };
      execSheet.getRow(9).font = { bold: true };
      execSheet.getRow(15).font = { bold: true };
    }

    // Continue with regular Excel export
    await exportExcel(transactions, enhancedOptions);
  } else {
    // Enhanced PDF report
    exportPDF(transactions, enhancedOptions);
  }
}

/**
 * Get available export formats
 */
export function getAvailableFormats(): {
  value: ExportFormat;
  label: string;
  description: string;
}[] {
  return [
    {
      value: 'csv',
      label: 'CSV',
      description:
        'Simple comma-separated values file for spreadsheet applications',
    },
    {
      value: 'excel',
      label: 'Excel',
      description:
        'Formatted Excel workbook with multiple sheets and summaries',
    },
    {
      value: 'pdf',
      label: 'PDF',
      description: 'Professional PDF report with charts and formatted tables',
    },
  ];
}
