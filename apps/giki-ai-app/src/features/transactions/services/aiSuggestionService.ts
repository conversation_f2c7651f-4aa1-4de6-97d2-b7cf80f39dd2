/**
 * AI Suggestion Service for Transaction Categorization
 * 
 * Provides enhanced AI suggestions with confidence scores,
 * similar transaction detection, and rule creation capabilities
 */

import { apiClient } from '@/shared/services/api/apiClient';
import { type Transaction } from '@/shared/types/categorization';
import { type Category } from '@/shared/services/base/ServiceFactory';

export interface AISuggestion {
  categoryId: number;
  categoryPath: string;
  confidence: number;
  reasoning?: string;
  similarTransactionCount?: number;
  suggestedRule?: {
    pattern: string;
    description: string;
  };
}

export interface SimilarTransaction {
  id: string;
  description: string;
  amount: number;
  date: string;
  currentCategory?: string;
  matchScore: number;
}

export interface BatchCategorizationResult {
  processedCount: number;
  successCount: number;
  failedCount: number;
  transactions: Array<{
    id: string;
    categoryId: number;
    confidence: number;
  }>;
}

/**
 * Get AI suggestion for a single transaction
 */
export async function getAISuggestion(
  transaction: Transaction,
  categories: Category[]
): Promise<AISuggestion | null> {
  try {
    const response = await apiClient.post('/api/v1/ai/suggest-category', {
      transaction_id: transaction.id,
      description: transaction.description,
      amount: transaction.amount,
      date: transaction.date,
      available_categories: categories.map(c => ({
        id: c.id,
        name: c.name,
        path: c.path,
      })),
    });

    if ('error' in response) {
      console.error('Failed to get AI suggestion:', response.error);
      return null;
    }

    const data = response.data;
    
    return {
      categoryId: data.category_id,
      categoryPath: data.category_path,
      confidence: data.confidence,
      reasoning: data.reasoning,
      similarTransactionCount: data.similar_count,
      suggestedRule: data.suggested_rule ? {
        pattern: data.suggested_rule.pattern,
        description: data.suggested_rule.description,
      } : undefined,
    };
  } catch (error) {
    console.error('AI suggestion error:', error);
    return null;
  }
}

/**
 * Find similar transactions based on pattern matching
 */
export async function findSimilarTransactions(
  transaction: Transaction,
  allTransactions: Transaction[]
): Promise<SimilarTransaction[]> {
  try {
    // Extract merchant pattern
    const merchantPattern = extractMerchantPattern(transaction.description || '');
    
    // Local similarity matching (can be enhanced with API call)
    const similar = allTransactions
      .filter(t => t.id !== transaction.id)
      .map(t => {
        const pattern = extractMerchantPattern(t.description || '');
        const matchScore = calculateSimilarity(merchantPattern, pattern);
        
        return {
          id: t.id,
          description: t.description || '',
          amount: t.amount || 0,
          date: t.date,
          currentCategory: t.category_name,
          matchScore,
        };
      })
      .filter(t => t.matchScore > 0.7) // 70% similarity threshold
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, 10); // Return top 10 similar transactions
    
    return similar;
  } catch (error) {
    console.error('Similar transaction search error:', error);
    return [];
  }
}

/**
 * Apply AI suggestion to multiple transactions
 */
export async function applyBatchCategorization(
  transactionIds: string[],
  categoryId: number
): Promise<BatchCategorizationResult> {
  try {
    const response = await apiClient.post('/api/v1/transactions/batch-categorize', {
      transaction_ids: transactionIds,
      category_id: categoryId,
    });

    if ('error' in response) {
      throw new Error(response.error);
    }

    return response.data;
  } catch (error) {
    console.error('Batch categorization error:', error);
    return {
      processedCount: transactionIds.length,
      successCount: 0,
      failedCount: transactionIds.length,
      transactions: [],
    };
  }
}

/**
 * Apply category to single transaction
 */
export async function applySingleCategorization(
  transactionId: string,
  categoryId: number
): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await apiClient.put(`/api/v1/transactions/${transactionId}`, {
      category_id: categoryId,
    });

    if ('error' in response) {
      return { success: false, error: response.error };
    }

    return { success: true };
  } catch (error) {
    console.error('Single categorization error:', error);
    return { success: false, error: 'Failed to categorize transaction' };
  }
}

/**
 * Create a categorization rule based on pattern
 */
export async function createCategorizationRule(
  pattern: string,
  categoryId: number,
  description?: string
): Promise<{ id: number; pattern: string; category_id: number } | null> {
  try {
    const response = await apiClient.post('/api/v1/categorization-rules', {
      pattern,
      category_id: categoryId,
      description: description || `Auto-created rule for pattern: ${pattern}`,
      enabled: true,
      auto_created: true,
    });

    if ('error' in response) {
      console.error('Failed to create rule:', response.error);
      return null;
    }

    return response.data;
  } catch (error) {
    console.error('Rule creation error:', error);
    return null;
  }
}

/**
 * Get AI confidence explanation
 */
export function getConfidenceExplanation(confidence: number): {
  level: 'high' | 'medium' | 'low';
  label: string;
  description: string;
  color: string;
} {
  if (confidence >= 0.8) {
    return {
      level: 'high',
      label: 'High Confidence',
      description: 'AI is very confident about this categorization',
      color: '#059669', // Green
    };
  } else if (confidence >= 0.6) {
    return {
      level: 'medium',
      label: 'Medium Confidence',
      description: 'AI suggests this category but review recommended',
      color: '#D97706', // Orange
    };
  } else {
    return {
      level: 'low',
      label: 'Low Confidence',
      description: 'AI is uncertain - manual review required',
      color: '#DC2626', // Red
    };
  }
}

// Helper functions
function extractMerchantPattern(description: string): string {
  return description
    .toLowerCase()
    .replace(/\d+/g, '') // Remove numbers
    .replace(/[^a-z\s]/g, '') // Keep only letters and spaces
    .trim()
    .split(' ')
    .filter(word => word.length > 2) // Filter out short words
    .slice(0, 3) // Take first 3 significant words
    .join(' ');
}

function calculateSimilarity(pattern1: string, pattern2: string): number {
  if (pattern1 === pattern2) return 1;
  
  const words1 = pattern1.split(' ');
  const words2 = pattern2.split(' ');
  
  let matches = 0;
  for (const word of words1) {
    if (words2.includes(word)) {
      matches++;
    }
  }
  
  return matches / Math.max(words1.length, words2.length);
}

export default {
  getAISuggestion,
  findSimilarTransactions,
  applyBatchCategorization,
  applySingleCategorization,
  createCategorizationRule,
  getConfidenceExplanation,
};