/**
 * Test Utilities for giki.ai Frontend Tests
 */

import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';

// Import types for mocking
import type { User } from '@/features/auth/types/user';

// Auth Store Mock with proper typing
interface AuthState {
  token: string | null;
  refreshToken: string | null;
  userId: string | null;
  tenantId: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<void>;
  refreshTokens: () => Promise<void>;
  startAutoRefresh: () => void;
  stopAutoRefresh: () => void;
  setUser: (user: User | null) => void;
}

// Mock Auth Store State
export const mockAuthStore: AuthState = {
  token: null,
  refreshToken: null,
  userId: null,
  tenantId: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  user: null,
  login: vi.fn().mockResolvedValue(undefined),
  logout: vi.fn(),
  checkAuth: vi.fn().mockResolvedValue(undefined),
  refreshTokens: vi.fn().mockResolvedValue(undefined),
  startAutoRefresh: vi.fn(),
  stopAutoRefresh: vi.fn(),
  setUser: vi.fn(),
};

// Create a proper zustand store mock
const createAuthStoreMock = () => {
  let state = { ...mockAuthStore };
  
  const listeners = new Set<() => void>();
  
  const api = {
    getState: () => state,
    setState: (partial: any) => {
      const nextState = typeof partial === 'function' ? partial(state) : partial;
      state = { ...state, ...nextState };
      listeners.forEach(listener => listener());
    },
    subscribe: (listener: () => void) => {
      listeners.add(listener);
      return () => listeners.delete(listener);
    },
    destroy: () => {
      listeners.clear();
    },
  };
  
  // The hook function that components call
  const useStore = () => state;
  
  // Add the API methods to the hook
  Object.assign(useStore, api);
  
  return useStore;
};

// Export the mock for direct manipulation in tests
export const authStoreMock = createAuthStoreMock();

// Mock zustand auth store
vi.mock('@/shared/services/auth/authStore', () => ({
  default: authStoreMock,
  useAuthStore: authStoreMock,
}));

// Mock API Client
export const mockApiClient = {
  get: vi.fn().mockResolvedValue({ data: {}, status: 200, headers: new Headers() }),
  post: vi.fn().mockResolvedValue({ data: {}, status: 200, headers: new Headers() }),
  put: vi.fn().mockResolvedValue({ data: {}, status: 200, headers: new Headers() }),
  patch: vi.fn().mockResolvedValue({ data: {}, status: 200, headers: new Headers() }),
  delete: vi.fn().mockResolvedValue({ data: {}, status: 200, headers: new Headers() }),
  postFormData: vi.fn().mockResolvedValue({ data: {}, status: 200, headers: new Headers() }),
  postFormUrlEncoded: vi.fn().mockResolvedValue({ data: {}, status: 200, headers: new Headers() }),
  setBaseUrl: vi.fn(),
  getBaseUrl: vi.fn().mockReturnValue('http://localhost:8000/api/v1'),
};

vi.mock('@/shared/services/api/apiClient', () => ({
  apiClient: mockApiClient,
  default: vi.fn(() => mockApiClient),
}));

// Mock Unified WebSocket Service
class MockUnifiedWebSocketService {
  private listeners: Map<string, Set<Function>> = new Map();
  
  connect = vi.fn();
  disconnect = vi.fn();
  isConnected = vi.fn().mockReturnValue(true);
  getConnectionState = vi.fn().mockReturnValue('connected');
  getSessionId = vi.fn().mockReturnValue('mock-session-id');
  
  sendMessage = vi.fn((messageType: string, payload: any, options?: any) => {
    // Simulate immediate event emission for testing
    setTimeout(() => {
      const message = {
        type: messageType,
        payload,
        timestamp: Date.now(),
        message_id: `msg_${Date.now()}_test`,
      };
      this.emit(messageType, message);
    }, 0);
  });
  
  subscribe = vi.fn((messageType: string, handler: Function) => {
    if (!this.listeners.has(messageType)) {
      this.listeners.set(messageType, new Set());
    }
    this.listeners.get(messageType)!.add(handler);
    return () => this.unsubscribe(messageType, handler);
  });
  
  unsubscribe = vi.fn((messageType: string, handler: Function) => {
    this.listeners.get(messageType)?.delete(handler);
  });
  
  onConnectionChange = vi.fn((handler: Function) => {
    return () => {};
  });
  
  onError = vi.fn((handler: Function) => {
    return () => {};
  });
  
  emit = (messageType: string, message: any) => {
    this.listeners.get(messageType)?.forEach(handler => handler(message));
  };
  
  getStats = vi.fn().mockReturnValue({
    connections_total: 1,
    messages_sent: 0,
    messages_received: 0,
    errors: 0,
    reconnections: 0,
    connection_state: 'connected',
    queued_messages: 0,
    active_subscriptions: 0,
    reconnect_attempts: 0,
  });
  
  clearQueue = vi.fn();
}

export const mockUnifiedWebSocketService = new MockUnifiedWebSocketService();

// Mock WebSocket hooks for unified service
export const mockUseWebSocketMessage = vi.fn((messageType: string, handler: Function, deps?: any[]) => {
  // Return cleanup function
  return () => {};
});

export const mockUseWebSocketSender = vi.fn(() => {
  return mockUnifiedWebSocketService.sendMessage;
});

export const mockUseUnifiedWebSocket = vi.fn(() => ({
  service: mockUnifiedWebSocketService,
  connectionState: 'connected',
  isConnected: true,
  stats: mockUnifiedWebSocketService.getStats(),
}));

export const mockGetUnifiedWebSocketService = vi.fn(() => mockUnifiedWebSocketService);

export const mockEmit = vi.fn((eventType: string, payload: any, source?: 'ui' | 'agent') => {
  // Mock implementation
});

// Mock old WebSocket service for backward compatibility
export const mockWebSocketService = {
  send: vi.fn(),
  subscribe: vi.fn(() => () => {}),
  isConnected: vi.fn().mockReturnValue(true),
  getConnectionStatus: vi.fn().mockReturnValue({ connected: true, reconnectAttempts: 0 }),
};

export const mockUseWebSocket = vi.fn((eventType: string, handler: Function) => {
  return () => {};
});

export const mockUseWebSocketStatus = vi.fn(() => ({
  connected: true,
  reconnectAttempts: 0,
  lastError: undefined,
  reconnect: vi.fn(),
}));

// Mock both old and new WebSocket services
vi.mock('@/shared/services/websocket/WebSocketService', () => ({
  webSocketService: mockWebSocketService,
  default: mockWebSocketService,
  useWebSocket: mockUseWebSocket,
  useWebSocketStatus: mockUseWebSocketStatus,
  emit: mockEmit,
}));

vi.mock('@/shared/services/websocket/UnifiedWebSocketService', () => ({
  getUnifiedWebSocketService: mockGetUnifiedWebSocketService,
  unifiedWebSocketService: mockUnifiedWebSocketService,
  useWebSocketMessage: mockUseWebSocketMessage,
  useWebSocketSender: mockUseWebSocketSender,
  useUnifiedWebSocket: mockUseUnifiedWebSocket,
  MessageType: {
    CONNECTION_ESTABLISHED: 'connection.established',
    CONNECTION_LOST: 'connection.lost',
    HEARTBEAT: 'heartbeat',
    AGENT_MESSAGE: 'agent.message',
    AGENT_TYPING: 'agent.typing',
    AGENT_STATUS: 'agent.status',
    AGENT_TRANSFER: 'agent.transfer',
    FILE_UPLOADED: 'file.uploaded',
    FILE_PROCESSING_STARTED: 'file.processing_started',
    FILE_PROCESSING_PROGRESS: 'file.processing_progress',
    FILE_PROCESSING_COMPLETED: 'file.processing_completed',
    FILE_PROCESSING_ERROR: 'file.processing_error',
    CATEGORIZATION_STARTED: 'categorization.started',
    CATEGORIZATION_PROGRESS: 'categorization.progress',
    CATEGORIZATION_COMPLETED: 'categorization.completed',
    CATEGORIZATION_ERROR: 'categorization.error',
    TRANSACTION_UPDATED: 'transaction.updated',
    TRANSACTION_CATEGORIZED: 'transaction.categorized',
    TRANSACTION_SELECTED: 'transaction.selected',
    CATEGORY_CREATED: 'category.created',
    CATEGORY_UPDATED: 'category.updated',
    REPORT_GENERATE: 'report.generate',
    REPORT_READY: 'report.ready',
    REPORT_ERROR: 'report.error',
    ACCURACY_UPDATED: 'accuracy.updated',
    ACCURACY_TEST_STARTED: 'accuracy.test_started',
    ACCURACY_TEST_COMPLETED: 'accuracy.test_completed',
    SYSTEM_NOTIFICATION: 'system.notification',
    SYSTEM_ERROR: 'system.error',
    DATA_REFRESH_REQUIRED: 'data.refresh_required',
    UI_STATE_UPDATE: 'ui.state_update',
  },
  Priority: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical',
  },
}));

// Mock Local Storage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Mock Session Storage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'sessionStorage', { value: sessionStorageMock });

// Test wrapper component with all providers
interface AllTheProvidersProps {
  children: React.ReactNode;
  initialEntries?: string[];
  queryClientOptions?: any;
}

const AllTheProviders: React.FC<AllTheProvidersProps> = ({ 
  children,
  initialEntries = ['/'],
  queryClientOptions = {},
}) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
        staleTime: 0,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        ...queryClientOptions?.queries,
      },
      mutations: {
        retry: false,
        ...queryClientOptions?.mutations,
      },
    },
    logger: {
      log: () => {},
      warn: () => {},
      error: () => {},
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

// Custom render function
const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Mock API responses
export const mockApiResponse = function<T>(data: T) {
  return {
    data,
    status: 200,
    headers: new Headers(),
  };
};

// Mock API error
export const mockApiError = (status: number, message: string) => ({
  status,
  data: { detail: message },
});

// Mock transaction data
export const mockTransaction = {
  id: '1',
  description: 'Test Transaction',
  amount: -100.00,
  date: '2025-01-01',
  vendor: 'Test Vendor',
  category: 'Test Category',
  ai_confidence: 0.95,
  tenant_id: 3,
};

// Mock dashboard data
export const mockDashboardData = {
  totalTransactions: 15,
  totalIncome: 645000,
  totalExpenses: 144800,
  netProfit: 500200,
  categorizationRate: 100.0,
  timeSaved: 0,
  categoriesActive: 0,
  exportReadiness: 10,
};

// Mock user data
export const mockUser = {
  id: 16,
  email: '<EMAIL>',
  tenant_id: 3,
  is_active: true,
  is_admin: false,
  full_name: 'Test Owner',
};

// Helper to setup authenticated state
export const setupAuthenticatedState = (overrides: Partial<AuthState> = {}) => {
  const authenticatedState = {
    isAuthenticated: true,
    token: 'mock-jwt-token',
    refreshToken: 'mock-refresh-token',
    userId: '16',
    tenantId: '3',
    error: null,
    isLoading: false,
    user: mockUser,
    ...overrides,
  };
  
  // Update the auth store mock
  authStoreMock.setState(authenticatedState);
  
  // Setup localStorage mocks
  localStorageMock.getItem.mockImplementation((key: string) => {
    if (key === 'access_token') return authenticatedState.token;
    if (key === 'refresh_token') return authenticatedState.refreshToken;
    if (key === 'user_id') return authenticatedState.userId;
    if (key === 'tenant_id') return authenticatedState.tenantId;
    return null;
  });
};

// Helper to setup unauthenticated state
export const setupUnauthenticatedState = () => {
  const unauthenticatedState = {
    isAuthenticated: false,
    token: null,
    refreshToken: null,
    userId: null,
    tenantId: null,
    error: null,
    isLoading: false,
    user: null,
  };
  
  // Update the auth store mock
  authStoreMock.setState(unauthenticatedState);
  
  // Clear localStorage mocks
  localStorageMock.getItem.mockReturnValue(null);
};

// Mock fetch implementation with better error handling
export const mockFetch = (response: any, ok = true, status = 200) => {
  global.fetch = vi.fn().mockImplementation((url: string, options: RequestInit = {}) => {
    // Simulate network delay
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          ok,
          status,
          statusText: ok ? 'OK' : 'Error',
          json: vi.fn().mockResolvedValue(response),
          text: vi.fn().mockResolvedValue(JSON.stringify(response)),
          blob: vi.fn().mockResolvedValue(new Blob([JSON.stringify(response)])),
          headers: new Headers({
            'content-type': 'application/json',
          }),
          clone: vi.fn().mockReturnThis(),
        });
      }, 0);
    });
  });
};

// Async testing utilities
export const waitForLoadingToFinish = async (screen: any) => {
  const loadingElements = screen.queryAllByTestId(/loading|spinner/i);
  if (loadingElements.length > 0) {
    await waitFor(() => {
      expect(screen.queryAllByTestId(/loading|spinner/i)).toHaveLength(0);
    });
  }
};

// Mock service responses
export const mockServiceResponses = {
  // Auth service mocks
  login: (email: string, password: string) => {
    mockApiClient.postFormUrlEncoded.mockResolvedValueOnce({
      data: {
        access_token: 'mock-jwt-token',
        refresh_token: 'mock-refresh-token',
        token_type: 'bearer',
      },
      status: 200,
      headers: new Headers(),
    });
    
    mockApiClient.get.mockResolvedValueOnce({
      data: { ...mockUser, email },
      status: 200,
      headers: new Headers(),
    });
  },
  
  refreshToken: () => {
    mockApiClient.postFormUrlEncoded.mockResolvedValueOnce({
      data: {
        access_token: 'new-mock-jwt-token',
        refresh_token: 'new-mock-refresh-token',
        token_type: 'bearer',
      },
      status: 200,
      headers: new Headers(),
    });
  },
  
  // Dashboard service mocks
  dashboardMetrics: (data = mockDashboardData) => {
    mockApiClient.get.mockResolvedValueOnce({
      data,
      status: 200,
      headers: new Headers(),
    });
  },
  
  // Transaction service mocks
  transactions: (data = [mockTransaction]) => {
    mockApiClient.get.mockResolvedValueOnce({
      data: { transactions: data, total: data.length },
      status: 200,
      headers: new Headers(),
    });
  },
  
  // File upload mocks
  fileUpload: (fileId = 'test-file-id') => {
    mockApiClient.postFormData.mockResolvedValueOnce({
      data: { file_id: fileId, status: 'processing' },
      status: 200,
      headers: new Headers(),
    });
  },
};

// Reset all mocks utility
export const resetAllMocks = () => {
  vi.clearAllMocks();
  setupUnauthenticatedState();
  mockWebSocketService.listeners?.clear();
  localStorageMock.getItem.mockReturnValue(null);
  sessionStorageMock.getItem.mockReturnValue(null);
};

// Export everything including the custom render
export * from '@testing-library/react';
export { customRender as render };
export { waitFor } from '@testing-library/react';