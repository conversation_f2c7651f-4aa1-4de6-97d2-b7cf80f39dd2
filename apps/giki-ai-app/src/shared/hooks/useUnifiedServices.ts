/**
 * Unified Service Hooks
 * =====================
 * 
 * Specialized hooks that use the base service hook for domain-specific operations.
 * These replace the individual hooks in each feature domain.
 * 
 * Consolidates:
 * - features/auth/hooks/useAuth.ts
 * - features/categories/hooks/useCategories.ts  
 * - features/transactions/hooks/useTransactions.ts
 * - features/dashboard/hooks/useDashboard.ts
 */

import { useCallback } from 'react';
import { useBaseService, UseBaseServiceOptions } from './useBaseService';
import {
  ServiceFactory,
  User,
  Category,
  Transaction,
  DashboardMetrics,
} from '@/shared/services/base';
import { useToast } from '@/shared/components/ui/use-toast';
import { logger } from '@/shared/lib/logger';

// Auth Hook
export interface UseAuthReturn {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string, rememberMe?: boolean) => Promise<{ success: boolean; error?: string }>;
  register: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
  updateProfile: (data: Partial<User>) => Promise<boolean>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<boolean>;
  checkAuth: () => Promise<void>;
}

export function useAuth(): UseAuthReturn {
  const { toast } = useToast();
  const authService = ServiceFactory.getAuthService();
  
  const [state, actions] = useBaseService(authService, {
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Authentication Error',
        description: error.message,
      });
    },
    onSuccess: (operation, data) => {
      if (operation === 'login') {
        toast({
          title: 'Welcome back!',
          description: 'You have been successfully logged in.',
        });
      }
    },
  });

  const login = useCallback(async (
    email: string, 
    password: string, 
    rememberMe?: boolean
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const result = await authService.login(email, password);
      if (result.success && result.data) {
        // Store tokens
        localStorage.setItem('access_token', result.data.access_token);
        localStorage.setItem('refresh_token', result.data.refresh_token);
        
        // Store remember me preference
        if (rememberMe) {
          localStorage.setItem('remember_me', 'true');
        } else {
          localStorage.removeItem('remember_me');
        }
        
        // Update state with user
        actions.optimisticUpdate('current', result.data.user);
        
        return { success: true };
      }
      return { success: false, error: result.error || 'Login failed' };
    } catch (error) {
      logger.error('Login failed', 'useAuth', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      };
    }
  }, [authService, actions]);

  const register = useCallback(async (email: string, password: string): Promise<boolean> => {
    try {
      const result = await authService.register(email, password);
      return result.success;
    } catch (error) {
      logger.error('Registration failed', 'useAuth', error);
      return false;
    }
  }, [authService]);

  const logout = useCallback(async (): Promise<void> => {
    try {
      await authService.logout();
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      actions.reset();
    } catch (error) {
      logger.error('Logout failed', 'useAuth', error);
    }
  }, [authService, actions]);

  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (!refreshToken) return false;
      
      const result = await authService.refreshToken(refreshToken);
      if (result.success && result.data) {
        localStorage.setItem('access_token', result.data.access_token);
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Token refresh failed', 'useAuth', error);
      return false;
    }
  }, [authService]);

  const updateProfile = useCallback(async (data: Partial<User>): Promise<boolean> => {
    const result = await actions.partialUpdate('current', data);
    return result !== null;
  }, [actions]);

  const changePassword = useCallback(async (
    currentPassword: string, 
    newPassword: string
  ): Promise<boolean> => {
    try {
      const result = await authService.changePassword(currentPassword, newPassword);
      return result.success;
    } catch (error) {
      logger.error('Password change failed', 'useAuth', error);
      return false;
    }
  }, [authService]);

  const checkAuth = useCallback(async (): Promise<void> => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        // No token, ensure user state is cleared
        actions.optimisticUpdate('current', null);
        return;
      }
      
      // For now, just check if token exists - avoid API calls that might block rendering
      // TODO: Implement proper token validation later
      logger.info('Token found, skipping validation for now', 'useAuth');
    } catch (error) {
      logger.error('Auth check failed', 'useAuth', error);
      // Clear invalid tokens
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      actions.optimisticUpdate('current', null);
    }
  }, [actions]);

  return {
    user: state.item,
    isAuthenticated: !!state.item && !!localStorage.getItem('access_token'),
    isLoading: state.isLoading,
    error: state.error?.message || null,
    login,
    register,
    logout,
    refreshToken,
    updateProfile,
    changePassword,
    checkAuth,
  };
}

// Categories Hook
export interface UseCategoriesReturn {
  // State
  categories: Category[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchCategories: () => Promise<void>;
  createCategory: (category: Omit<Category, 'id'>) => Promise<Category | null>;
  updateCategory: (id: number, updates: Partial<Category>) => Promise<Category | null>;
  deleteCategory: (id: number) => Promise<boolean>;
  searchCategories: (query: string) => Promise<Category[]>;
  getHierarchy: () => Promise<Category[]>;
  bulkUpdate: (updates: { id: number; data: Partial<Category> }[]) => Promise<Category[]>;
}

export function useCategories(options?: UseBaseServiceOptions): UseCategoriesReturn {
  const { toast } = useToast();
  const categoryService = ServiceFactory.getCategoryService();
  
  const [state, actions] = useBaseService(categoryService, {
    autoFetch: true,
    cacheTimeout: 5 * 60 * 1000, // 5 minutes
    enableOptimisticUpdates: true,
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Category Error',
        description: error.message,
      });
    },
    ...options,
  });

  const searchCategories = useCallback(async (query: string): Promise<Category[]> => {
    try {
      const result = await categoryService.searchCategories(query);
      return result.success && result.data ? result.data : [];
    } catch (error) {
      logger.error('Category search failed', 'useCategories', error);
      return [];
    }
  }, [categoryService]);

  const getHierarchy = useCallback(async (): Promise<Category[]> => {
    try {
      const result = await categoryService.getHierarchy();
      return result.success && result.data ? result.data : [];
    } catch (error) {
      logger.error('Category hierarchy fetch failed', 'useCategories', error);
      return [];
    }
  }, [categoryService]);

  const bulkUpdate = useCallback(async (
    updates: { id: number; data: Partial<Category> }[]
  ): Promise<Category[]> => {
    return actions.updateBatch(updates);
  }, [actions]);

  return {
    categories: state.data,
    isLoading: state.isLoading,
    error: state.error?.message || null,
    fetchCategories: actions.fetchAll,
    createCategory: actions.create,
    updateCategory: actions.update,
    deleteCategory: actions.deleteById,
    searchCategories,
    getHierarchy,
    bulkUpdate,
  };
}

// Transactions Hook
export interface UseTransactionsReturn {
  // State
  transactions: Transaction[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchTransactions: (filters?: any) => Promise<void>;
  searchTransactions: (query: string, filters?: any) => Promise<Transaction[]>;
  updateTransaction: (id: string, updates: Partial<Transaction>) => Promise<Transaction | null>;
  deleteTransaction: (id: string) => Promise<boolean>;
  categorizeTransaction: (id: string, categoryId: number) => Promise<Transaction | null>;
  bulkCategorize: (updates: { transaction_id: string; category_id: number }[]) => Promise<Transaction[]>;
  exportTransactions: (format: 'csv' | 'excel' | 'pdf', filters?: any) => Promise<string | null>;
}

export function useTransactions(options?: UseBaseServiceOptions): UseTransactionsReturn {
  const { toast } = useToast();
  const transactionService = ServiceFactory.getTransactionService();
  
  const [state, actions] = useBaseService(transactionService, {
    cacheTimeout: 2 * 60 * 1000, // 2 minutes
    enableOptimisticUpdates: true,
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Transaction Error',
        description: error.message,
      });
    },
    ...options,
  });

  const searchTransactions = useCallback(async (
    query: string, 
    filters?: any
  ): Promise<Transaction[]> => {
    try {
      const result = await transactionService.searchTransactions(query, filters);
      return result.success && result.data ? result.data : [];
    } catch (error) {
      logger.error('Transaction search failed', 'useTransactions', error);
      return [];
    }
  }, [transactionService]);

  const categorizeTransaction = useCallback(async (
    id: string, 
    categoryId: number
  ): Promise<Transaction | null> => {
    try {
      const result = await transactionService.categorizeTransaction(id, categoryId);
      if (result.success && result.data) {
        // Update local state optimistically
        actions.optimisticUpdate(id, { category_id: categoryId });
        return result.data;
      }
      return null;
    } catch (error) {
      logger.error('Transaction categorization failed', 'useTransactions', error);
      return null;
    }
  }, [transactionService, actions]);

  const bulkCategorize = useCallback(async (
    updates: { transaction_id: string; category_id: number }[]
  ): Promise<Transaction[]> => {
    try {
      const result = await transactionService.bulkCategorize(updates);
      if (result.success && result.data) {
        // Update local state
        updates.forEach(update => {
          actions.optimisticUpdate(update.transaction_id, { 
            category_id: update.category_id 
          });
        });
        return result.data;
      }
      return [];
    } catch (error) {
      logger.error('Bulk categorization failed', 'useTransactions', error);
      return [];
    }
  }, [transactionService, actions]);

  const exportTransactions = useCallback(async (
    format: 'csv' | 'excel' | 'pdf',
    filters?: any
  ): Promise<string | null> => {
    try {
      const result = await transactionService.exportTransactions(format, filters);
      if (result.success && result.data) {
        return result.data.download_url;
      }
      return null;
    } catch (error) {
      logger.error('Transaction export failed', 'useTransactions', error);
      return null;
    }
  }, [transactionService]);

  return {
    transactions: state.data,
    isLoading: state.isLoading,
    error: state.error?.message || null,
    fetchTransactions: actions.fetchAll,
    searchTransactions,
    updateTransaction: actions.update,
    deleteTransaction: actions.deleteById,
    categorizeTransaction,
    bulkCategorize,
    exportTransactions,
  };
}

// Dashboard Hook
export interface UseDashboardReturn {
  // State
  metrics: DashboardMetrics | null;
  recentTransactions: Transaction[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchMetrics: () => Promise<void>;
  fetchRecentTransactions: (limit?: number) => Promise<void>;
  refreshAll: () => Promise<void>;
}

export function useDashboard(): UseDashboardReturn {
  const { toast } = useToast();
  const dashboardService = ServiceFactory.getDashboardService();
  
  const [state, actions] = useBaseService(dashboardService, {
    autoFetch: true,
    cacheTimeout: 1 * 60 * 1000, // 1 minute
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Dashboard Error',
        description: error.message,
      });
    },
  });

  const fetchMetrics = useCallback(async () => {
    try {
      const result = await dashboardService.getMetrics();
      if (result.success && result.data) {
        // Update metrics in state
        actions.optimisticUpdate('metrics', result.data);
      }
    } catch (error) {
      logger.error('Dashboard metrics fetch failed', 'useDashboard', error);
    }
  }, [dashboardService, actions]);

  const fetchRecentTransactions = useCallback(async (limit: number = 10) => {
    try {
      const result = await dashboardService.getRecentTransactions(limit);
      if (result.success && result.data) {
        // Update recent transactions in state
        actions.optimisticUpdate('recent', result.data);
      }
    } catch (error) {
      logger.error('Recent transactions fetch failed', 'useDashboard', error);
    }
  }, [dashboardService, actions]);

  const refreshAll = useCallback(async () => {
    actions.invalidateCache();
    await Promise.all([
      fetchMetrics(),
      fetchRecentTransactions(),
    ]);
  }, [actions, fetchMetrics, fetchRecentTransactions]);

  return {
    metrics: state.item as DashboardMetrics | null,
    recentTransactions: state.data as Transaction[],
    isLoading: state.isLoading,
    error: state.error?.message || null,
    fetchMetrics,
    fetchRecentTransactions,
    refreshAll,
  };
}
