/**
 * useUserJourneyStatus Hook Tests
 * 
 * Tests the user journey status hook that determines routing logic
 * based on transaction categorization status and backend data validation.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useUserJourneyStatus } from '../useUserJourneyStatus';
import { dashboardService } from '@/shared/services/base';

// Mock the unified dashboard service
const mockGetMetrics = vi.spyOn(dashboardService, 'getMetrics');

describe('useUserJourneyStatus', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when user has categorized transactions', () => {
    beforeEach(() => {
      mockGetMetrics.mockResolvedValue({
        success: true,
        data: {
          totalTransactions: 150,
          categorizedTransactions: 150,
          uncategorizedTransactions: 0,
          categorizationRate: 100,
          totalIncome: 5000,
          totalExpense: 3000,
          netAmount: 2000,
          accuracy: 87.5
        }
      });
    });

    it('should return correct status for fully categorized transactions', async () => {
      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasCategorizedTransactions).toBe(true);
      expect(result.current.shouldGoToDashboard).toBe(true);
      expect(result.current.shouldGoToUpload).toBe(false);
      expect(result.current.totalTransactions).toBe(150);
      expect(result.current.categorizedTransactions).toBe(150);
      expect(result.current.uncategorizedTransactions).toBe(0);
      expect(result.current.categorizationRate).toBe(100);
      expect(result.current.lastProcessedDate).toBe('2025-01-10');
      expect(result.current.error).toBeNull();
    });
  });

  describe('when user has only uncategorized transactions', () => {
    beforeEach(() => {
      mockGetMetrics.mockResolvedValue({
        success: true,
        data: {
          totalTransactions: 100,
          categorizedTransactions: 0,
          uncategorizedTransactions: 100,
          categorizationRate: 0,
          totalIncome: 0,
          totalExpense: 0,
          netAmount: 0,
          accuracy: 0
        }
      });
    });

    it('should return correct status for uncategorized transactions', async () => {
      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasCategorizedTransactions).toBe(false);
      expect(result.current.shouldGoToDashboard).toBe(false);
      expect(result.current.shouldGoToUpload).toBe(true);
      expect(result.current.totalTransactions).toBe(100);
      expect(result.current.categorizedTransactions).toBe(0);
      expect(result.current.uncategorizedTransactions).toBe(100);
      expect(result.current.categorizationRate).toBe(0);
      expect(result.current.lastProcessedDate).toBeNull();
      expect(result.current.error).toBeNull();
    });
  });

  describe('when user has no transactions', () => {
    beforeEach(() => {
      mockGetMetrics.mockResolvedValue({
        success: true,
        data: {
          totalTransactions: 0,
          categorizedTransactions: 0,
          uncategorizedTransactions: 0,
          categorizationRate: 0,
          totalIncome: 0,
          totalExpense: 0,
          netAmount: 0,
          accuracy: 0
        }
      });
    });

    it('should return correct status for no transactions', async () => {
      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasCategorizedTransactions).toBe(false);
      expect(result.current.shouldGoToDashboard).toBe(false);
      expect(result.current.shouldGoToUpload).toBe(true);
      expect(result.current.totalTransactions).toBe(0);
      expect(result.current.categorizedTransactions).toBe(0);
      expect(result.current.uncategorizedTransactions).toBe(0);
      expect(result.current.categorizationRate).toBe(0);
      expect(result.current.lastProcessedDate).toBeNull();
      expect(result.current.error).toBeNull();
    });
  });

  describe('when dashboard service fails', () => {
    beforeEach(() => {
      mockGetMetrics.mockResolvedValue({
        success: false,
        error: { message: 'Network error', code: 'NETWORK_ERROR' }
      });
    });

    it('should handle errors gracefully', async () => {
      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasCategorizedTransactions).toBe(false);
      expect(result.current.shouldGoToDashboard).toBe(false);
      expect(result.current.shouldGoToUpload).toBe(true);
      expect(result.current.totalTransactions).toBe(0);
      expect(result.current.error).toBe('Network error');
    });
  });

  describe('backend data validation (critical bug fix)', () => {
    it('should detect and correct backend inconsistency where all transactions are marked as categorized but are actually "Uncategorized"', async () => {
      // Simulate the bug we found and fixed - backend claims 100% categorized but all are "Uncategorized"
      mockGetMetrics.mockResolvedValue({
        success: true,
        data: {
          totalTransactions: 100,
          categorizedTransactions: 100, // Backend incorrectly claims all are categorized
          uncategorizedTransactions: 0, // Backend incorrectly claims none are uncategorized
          categorizationRate: 100, // Backend incorrectly claims 100% rate
          totalIncome: 5000,
          totalExpense: 3000,
          netAmount: 2000,
          accuracy: 87.5
        }
      });

      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // The hook should detect the inconsistency and correct it
      expect(result.current.hasCategorizedTransactions).toBe(false); // Corrected from backend claim
      expect(result.current.shouldGoToDashboard).toBe(false); // Should NOT go to dashboard
      expect(result.current.shouldGoToUpload).toBe(true); // Should go to upload instead
      expect(result.current.categorizedTransactions).toBe(0); // Corrected count
      expect(result.current.uncategorizedTransactions).toBe(100); // Corrected count
      expect(result.current.categorizationRate).toBe(0); // Corrected rate
    });

    it('should handle partial categorization correctly', async () => {
      mockGetMetrics.mockResolvedValue({
        success: true,
        data: {
          totalTransactions: 200,
          categorizedTransactions: 150,
          uncategorizedTransactions: 50,
          categorizationRate: 75,
          totalIncome: 8000,
          totalExpense: 6000,
          netAmount: 2000,
          accuracy: 87.5
        }
      });

      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasCategorizedTransactions).toBe(true);
      expect(result.current.shouldGoToDashboard).toBe(true);
      expect(result.current.shouldGoToUpload).toBe(false);
      expect(result.current.totalTransactions).toBe(200);
      expect(result.current.categorizedTransactions).toBe(150);
      expect(result.current.uncategorizedTransactions).toBe(50);
      expect(result.current.categorizationRate).toBe(75);
    });
  });

  describe('loading states', () => {
    it('should start with loading state', () => {
      // Mock a pending promise
      mockGetMetrics.mockImplementation(() => new Promise(() => {}));

      const { result } = renderHook(() => useUserJourneyStatus());

      expect(result.current.isLoading).toBe(true);
      expect(result.current.hasCategorizedTransactions).toBe(false);
      expect(result.current.shouldGoToDashboard).toBe(false);
      expect(result.current.shouldGoToUpload).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('routing logic edge cases', () => {
    it('should prioritize dashboard when hasCategorizedTransactions is true', async () => {
      mockGetMetrics.mockResolvedValue({
        success: true,
        data: {
          totalTransactions: 1,
          categorizedTransactions: 1,
          uncategorizedTransactions: 0,
          categorizationRate: 100,
          totalIncome: 100,
          totalExpense: 50,
          netAmount: 50,
          accuracy: 87.5
        }
      });

      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.shouldGoToDashboard).toBe(true);
      expect(result.current.shouldGoToUpload).toBe(false);
    });

    it('should enforce mutual exclusivity in routing decisions', async () => {
      mockGetMetrics.mockResolvedValue({
        success: true,
        data: {
          totalTransactions: 50,
          categorizedTransactions: 25,
          uncategorizedTransactions: 25,
          categorizationRate: 50,
          totalIncome: 2500,
          totalExpense: 2000,
          netAmount: 500,
          accuracy: 87.5
        }
      });

      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // With categorized transactions present, should go to dashboard
      expect(result.current.shouldGoToDashboard).toBe(true);
      expect(result.current.shouldGoToUpload).toBe(false);
      
      // Mutual exclusivity check
      expect(result.current.shouldGoToDashboard && result.current.shouldGoToUpload).toBe(false);
    });
  });
});