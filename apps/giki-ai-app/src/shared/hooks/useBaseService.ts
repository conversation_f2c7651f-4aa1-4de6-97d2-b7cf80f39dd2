/**
 * Base Service Hook
 * =================
 * 
 * Unified base hook that consolidates common patterns from:
 * - features/auth/hooks/useAuth.ts
 * - features/categories/hooks/useCategories.ts
 * - features/transactions/hooks/useTransactions.ts
 * - shared/hooks/useApi.ts
 * 
 * This base hook provides:
 * - Standardized loading and error states
 * - Common CRUD operations
 * - Caching and invalidation
 * - Optimistic updates
 * - Pagination support
 * - Real-time updates via WebSocket
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import { BaseApiService, ServiceResult, BaseEntity, PaginatedResponse } from '@/shared/services/base';
import { AppError } from '@/shared/types/errors';

export interface UseBaseServiceState<T> {
  data: T[];
  item: T | null;
  paginatedData: PaginatedResponse<T> | null;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: AppError | null;
  lastFetch: number | null;
}

export interface UseBaseServiceActions<T extends BaseEntity> {
  // Data fetching
  fetchAll: (params?: Record<string, any>) => Promise<void>;
  fetchById: (id: string | number) => Promise<void>;
  fetchPaginated: (page?: number, perPage?: number, params?: Record<string, any>) => Promise<void>;
  
  // CRUD operations
  create: (data: Omit<T, 'id' | 'created_at' | 'updated_at'>) => Promise<T | null>;
  update: (id: string | number, data: Partial<T>) => Promise<T | null>;
  partialUpdate: (id: string | number, data: Partial<T>) => Promise<T | null>;
  deleteById: (id: string | number) => Promise<boolean>;
  
  // Batch operations
  createBatch: (items: Omit<T, 'id' | 'created_at' | 'updated_at'>[]) => Promise<T[]>;
  updateBatch: (updates: { id: string | number; data: Partial<T> }[]) => Promise<T[]>;
  deleteBatch: (ids: (string | number)[]) => Promise<boolean>;
  
  // State management
  reset: () => void;
  clearError: () => void;
  invalidateCache: () => void;
  
  // Optimistic updates
  optimisticUpdate: (id: string | number, data: Partial<T>) => void;
  optimisticCreate: (data: Omit<T, 'id' | 'created_at' | 'updated_at'>) => string;
  optimisticDelete: (id: string | number) => void;
}

export interface UseBaseServiceOptions {
  autoFetch?: boolean;
  cacheTimeout?: number;
  enableOptimisticUpdates?: boolean;
  enableRealTimeUpdates?: boolean;
  onError?: (error: AppError) => void;
  onSuccess?: (operation: string, data: any) => void;
}

export type UseBaseServiceReturn<T extends BaseEntity> = [
  UseBaseServiceState<T>,
  UseBaseServiceActions<T>
];

export function useBaseService<T extends BaseEntity>(
  service: BaseApiService<T>,
  options: UseBaseServiceOptions = {}
): UseBaseServiceReturn<T> {
  const {
    autoFetch = false,
    cacheTimeout = 5 * 60 * 1000, // 5 minutes
    enableOptimisticUpdates = true,
    enableRealTimeUpdates = false,
    onError,
    onSuccess,
  } = options;

  // State
  const [state, setState] = useState<UseBaseServiceState<T>>({
    data: [],
    item: null,
    paginatedData: null,
    isLoading: false,
    isCreating: false,
    isUpdating: false,
    isDeleting: false,
    error: null,
    lastFetch: null,
  });

  // Refs for cleanup and optimization
  const abortControllerRef = useRef<AbortController | null>(null);
  const optimisticUpdatesRef = useRef<Map<string, T>>(new Map());

  // Helper function to handle service results
  const handleServiceResult = useCallback(
    <U>(result: ServiceResult<U>, operation: string): U | null => {
      if (result.success && result.data !== undefined) {
        onSuccess?.(operation, result.data);
        return result.data;
      } else {
        const error = result.error || new Error(`${operation} failed`);
        setState(prev => ({ ...prev, error: error as AppError }));
        onError?.(error as AppError);
        return null;
      }
    },
    [onError, onSuccess]
  );

  // Data fetching actions
  const fetchAll = useCallback(async (params?: Record<string, any>) => {
    // Check cache
    if (state.lastFetch && Date.now() - state.lastFetch < cacheTimeout) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const result = await service.getAll(params);
      const data = handleServiceResult(result, 'fetchAll');
      
      if (data) {
        setState(prev => ({
          ...prev,
          data,
          isLoading: false,
          lastFetch: Date.now(),
        }));
      } else {
        setState(prev => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error as AppError,
      }));
    }
  }, [service, handleServiceResult, state.lastFetch, cacheTimeout]);

  const fetchById = useCallback(async (id: string | number) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const result = await service.getById(id);
      const item = handleServiceResult(result, 'fetchById');
      
      setState(prev => ({
        ...prev,
        item,
        isLoading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error as AppError,
      }));
    }
  }, [service, handleServiceResult]);

  const fetchPaginated = useCallback(async (
    page: number = 1,
    perPage: number = 20,
    params?: Record<string, any>
  ) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const result = await service.getPaginated(page, perPage, params);
      const paginatedData = handleServiceResult(result, 'fetchPaginated');
      
      setState(prev => ({
        ...prev,
        paginatedData,
        isLoading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error as AppError,
      }));
    }
  }, [service, handleServiceResult]);

  // CRUD operations
  const create = useCallback(async (
    data: Omit<T, 'id' | 'created_at' | 'updated_at'>
  ): Promise<T | null> => {
    setState(prev => ({ ...prev, isCreating: true, error: null }));

    try {
      const result = await service.create(data);
      const created = handleServiceResult(result, 'create');
      
      if (created) {
        setState(prev => ({
          ...prev,
          data: [...prev.data, created],
          isCreating: false,
        }));
      } else {
        setState(prev => ({ ...prev, isCreating: false }));
      }
      
      return created;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isCreating: false,
        error: error as AppError,
      }));
      return null;
    }
  }, [service, handleServiceResult]);

  const update = useCallback(async (
    id: string | number,
    data: Partial<T>
  ): Promise<T | null> => {
    setState(prev => ({ ...prev, isUpdating: true, error: null }));

    // Optimistic update
    if (enableOptimisticUpdates) {
      setState(prev => ({
        ...prev,
        data: prev.data.map(item => 
          item.id === id ? { ...item, ...data } : item
        ),
      }));
    }

    try {
      const result = await service.update(id, data);
      const updated = handleServiceResult(result, 'update');
      
      if (updated) {
        setState(prev => ({
          ...prev,
          data: prev.data.map(item => item.id === id ? updated : item),
          item: prev.item?.id === id ? updated : prev.item,
          isUpdating: false,
        }));
      } else {
        // Revert optimistic update on failure
        if (enableOptimisticUpdates) {
          setState(prev => ({ ...prev, isUpdating: false }));
          // Would need to revert the optimistic update here
        }
      }
      
      return updated;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isUpdating: false,
        error: error as AppError,
      }));
      return null;
    }
  }, [service, handleServiceResult, enableOptimisticUpdates]);

  const partialUpdate = useCallback(async (
    id: string | number,
    data: Partial<T>
  ): Promise<T | null> => {
    setState(prev => ({ ...prev, isUpdating: true, error: null }));

    try {
      const result = await service.partialUpdate(id, data);
      const updated = handleServiceResult(result, 'partialUpdate');
      
      if (updated) {
        setState(prev => ({
          ...prev,
          data: prev.data.map(item => item.id === id ? updated : item),
          item: prev.item?.id === id ? updated : prev.item,
          isUpdating: false,
        }));
      } else {
        setState(prev => ({ ...prev, isUpdating: false }));
      }
      
      return updated;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isUpdating: false,
        error: error as AppError,
      }));
      return null;
    }
  }, [service, handleServiceResult]);

  const deleteById = useCallback(async (id: string | number): Promise<boolean> => {
    setState(prev => ({ ...prev, isDeleting: true, error: null }));

    // Optimistic delete
    if (enableOptimisticUpdates) {
      setState(prev => ({
        ...prev,
        data: prev.data.filter(item => item.id !== id),
      }));
    }

    try {
      const result = await service.deleteById(id);
      const success = handleServiceResult(result, 'delete') !== null;
      
      if (success) {
        setState(prev => ({
          ...prev,
          data: prev.data.filter(item => item.id !== id),
          item: prev.item?.id === id ? null : prev.item,
          isDeleting: false,
        }));
      } else {
        setState(prev => ({ ...prev, isDeleting: false }));
      }
      
      return success;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isDeleting: false,
        error: error as AppError,
      }));
      return false;
    }
  }, [service, handleServiceResult, enableOptimisticUpdates]);

  // Batch operations
  const createBatch = useCallback(async (
    items: Omit<T, 'id' | 'created_at' | 'updated_at'>[]
  ): Promise<T[]> => {
    setState(prev => ({ ...prev, isCreating: true, error: null }));

    try {
      const result = await service.createBatch(items);
      const created = handleServiceResult(result, 'createBatch');
      
      if (created) {
        setState(prev => ({
          ...prev,
          data: [...prev.data, ...created],
          isCreating: false,
        }));
        return created;
      } else {
        setState(prev => ({ ...prev, isCreating: false }));
        return [];
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isCreating: false,
        error: error as AppError,
      }));
      return [];
    }
  }, [service, handleServiceResult]);

  const updateBatch = useCallback(async (
    updates: { id: string | number; data: Partial<T> }[]
  ): Promise<T[]> => {
    setState(prev => ({ ...prev, isUpdating: true, error: null }));

    try {
      const result = await service.updateBatch(updates);
      const updated = handleServiceResult(result, 'updateBatch');
      
      if (updated) {
        setState(prev => ({
          ...prev,
          data: prev.data.map(item => {
            const update = updated.find(u => u.id === item.id);
            return update || item;
          }),
          isUpdating: false,
        }));
        return updated;
      } else {
        setState(prev => ({ ...prev, isUpdating: false }));
        return [];
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isUpdating: false,
        error: error as AppError,
      }));
      return [];
    }
  }, [service, handleServiceResult]);

  const deleteBatch = useCallback(async (ids: (string | number)[]): Promise<boolean> => {
    setState(prev => ({ ...prev, isDeleting: true, error: null }));

    try {
      const result = await service.deleteBatch(ids);
      const success = handleServiceResult(result, 'deleteBatch') !== null;
      
      if (success) {
        setState(prev => ({
          ...prev,
          data: prev.data.filter(item => !ids.includes(item.id)),
          isDeleting: false,
        }));
      } else {
        setState(prev => ({ ...prev, isDeleting: false }));
      }
      
      return success;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isDeleting: false,
        error: error as AppError,
      }));
      return false;
    }
  }, [service, handleServiceResult]);

  // State management
  const reset = useCallback(() => {
    setState({
      data: [],
      item: null,
      paginatedData: null,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      error: null,
      lastFetch: null,
    });
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const invalidateCache = useCallback(() => {
    setState(prev => ({ ...prev, lastFetch: null }));
    if ('clearAllCache' in service) {
      (service as any).clearAllCache();
    }
  }, [service]);

  // Optimistic updates
  const optimisticUpdate = useCallback((id: string | number, data: Partial<T>) => {
    if (!enableOptimisticUpdates) return;
    
    setState(prev => ({
      ...prev,
      data: prev.data.map(item => 
        item.id === id ? { ...item, ...data } : item
      ),
    }));
  }, [enableOptimisticUpdates]);

  const optimisticCreate = useCallback((data: Omit<T, 'id' | 'created_at' | 'updated_at'>): string => {
    if (!enableOptimisticUpdates) return '';
    
    const tempId = `temp-${Date.now()}`;
    const tempItem = {
      ...data,
      id: tempId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    } as T;
    
    setState(prev => ({
      ...prev,
      data: [...prev.data, tempItem],
    }));
    
    return tempId;
  }, [enableOptimisticUpdates]);

  const optimisticDelete = useCallback((id: string | number) => {
    if (!enableOptimisticUpdates) return;
    
    setState(prev => ({
      ...prev,
      data: prev.data.filter(item => item.id !== id),
    }));
  }, [enableOptimisticUpdates]);

  // Auto-fetch on mount
  useEffect(() => {
    if (autoFetch) {
      fetchAll();
    }
  }, [autoFetch, fetchAll]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const actions: UseBaseServiceActions<T> = {
    fetchAll,
    fetchById,
    fetchPaginated,
    create,
    update,
    partialUpdate,
    deleteById,
    createBatch,
    updateBatch,
    deleteBatch,
    reset,
    clearError,
    invalidateCache,
    optimisticUpdate,
    optimisticCreate,
    optimisticDelete,
  };

  return [state, actions];
}
