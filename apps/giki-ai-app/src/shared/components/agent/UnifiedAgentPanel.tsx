/**
 * Unified Agent Panel Component - FAB + Chat Interface
 * Matches wireframe: docs/wireframes/04-agent-integration/01-agent-panel.md
 *
 * REAL BACKEND INTEGRATION: Connected to ConversationalAgent API
 */
import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Textarea } from '@/shared/components/ui/textarea';
import { MessageCircle, X, Send, Paperclip, ArrowLeft } from 'lucide-react';
import { unifiedAIService, type ConversationResponse } from '@/shared/services/ai/UnifiedAIService';
import { useWebSocketMessage, useWebSocketSender, MessageType } from '@/shared/services/websocket/UnifiedWebSocketService.tsx';
import { useNavigate } from 'react-router-dom';

interface Message {
  id: string;
  type: 'user' | 'agent';
  content: string;
  timestamp: Date;
  attachments?: string[];
  actions?: MessageAction[];
}

interface MessageAction {
  label: string;
  action: () => void;
  variant?: 'default' | 'outline';
  command?: string; // For backward compatibility
}

interface UnifiedAgentPanelProps {
  isVisible?: boolean;
  onToggle?: () => void;
  initialMessage?: string;
  workflowContext?: WorkflowContext;
}

interface WorkflowContext {
  currentPage: string;
  userRole: string;
  currentData?: any;
  availableActions?: string[];
  contextualHints?: string[];
}

const UnifiedAgentPanel: React.FC<UnifiedAgentPanelProps> = ({
  isVisible = false,
  onToggle,
  initialMessage,
  workflowContext,
}) => {
  const [isOpen, setIsOpen] = useState(isVisible);
  const [inputValue, setInputValue] = useState(initialMessage || '');
  const [isTyping, setIsTyping] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  // WebSocket integration for real-time updates using unified service
  const sendWebSocketMessage = useWebSocketSender();
  
  useWebSocketMessage(MessageType.SYSTEM_NOTIFICATION, (payload: any) => {
    if (payload.type === 'agent_response') {
      const agentResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: payload.message,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, agentResponse]);
    }
  });

  // Generate context-aware initial message
  const getContextualWelcomeMessage = () => {
    if (!workflowContext) {
      return 'How can I help you today? I can help with uploading files, generating reports, or answering questions about your transactions.';
    }

    switch (workflowContext.currentPage) {
      case 'upload':
        return 'I can help you upload files, explain supported formats, or guide you through the upload process.';
      case 'processing':
        return 'I can explain what\'s happening during processing, help with any issues, or answer questions about the AI categorization.';
      case 'review':
        return 'I can help you review transactions, explain confidence scores, or guide you through bulk approval workflows.';
      case 'improvements':
        return 'I can help you choose the best enhancement options, explain accuracy improvements, or guide you through the setup process.';
      case 'dashboard':
        return 'I can help you understand your dashboard metrics, generate reports, or explain your financial data.';
      case 'transactions':
        return 'I can help you search transactions, explain categorizations, or help you make corrections.';
      case 'categories':
        return 'I can help you manage categories, create custom rules, or explain the category hierarchy.';
      case 'reports':
        return 'I can help you generate reports, explain metrics, or customize report parameters.';
      default:
        return 'How can I help you today? I can help with uploading files, generating reports, or answering questions about your transactions.';
    }
  };

  // Real conversation data from backend
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'agent',
      content: getContextualWelcomeMessage(),
      timestamp: new Date(),
    },
  ]);

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Scroll to bottom when new messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Auto-focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  // Update welcome message when context changes
  useEffect(() => {
    if (workflowContext && messages.length > 0) {
      const newWelcomeMessage = getContextualWelcomeMessage();
      if (messages[0].content !== newWelcomeMessage) {
        setMessages(prev => [
          {
            ...prev[0],
            content: newWelcomeMessage,
            timestamp: new Date()
          },
          ...prev.slice(1)
        ]);
      }
    }
  }, [workflowContext]);

  const handleToggle = () => {
    setIsOpen(!isOpen);
    onToggle?.();
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userInput = inputValue;
    const newMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: userInput,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, newMessage]);
    setInputValue('');
    setIsTyping(true);

    try {
      // Use UnifiedAIService for conversation processing
      const response: ConversationResponse = await unifiedAIService.processConversation({
        message: userInput,
        context: workflowContext ? {
          current_page: workflowContext.currentPage,
          user_role: workflowContext.userRole,
          available_actions: workflowContext.availableActions,
          current_data: workflowContext.currentData
        } : undefined
      });

      const agentResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: response.message,
        timestamp: new Date(),
        actions: response.actions?.map((action) => ({
          label: action.label,
          action: () => handleAgentAction(action),
          variant: action.variant as 'default' | 'outline' | undefined,
          command: action.command,
        })),
      };

      setMessages((prev) => [...prev, agentResponse]);

      // Send WebSocket event for real-time updates using unified service
      sendWebSocketMessage(MessageType.AGENT_MESSAGE, {
        message: response.message,
        user_message: userInput,
        context: workflowContext?.currentPage
      });

    } catch (error) {
      console.error('Agent communication error:', error);

      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content:
          'I apologize, but I encountered an error processing your request. Please try again.',
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, errorResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      void handleSendMessage();
    }
  };

  const handleAgentAction = (action: any) => {
    const { command, label } = action;
    
    // UI-driven actions only - no command execution
    // Agent provides navigation suggestions, UI handles the actions
    
    const lowerLabel = label.toLowerCase();
    
    // Navigate based on action label (no commands)
    if (lowerLabel.includes('upload') || lowerLabel.includes('file')) {
      handleFileAttachment();
    } else if (lowerLabel.includes('report')) {
      navigate('/reports');
      setIsOpen(false);
    } else if (lowerLabel.includes('analyze') || lowerLabel.includes('dashboard')) {
      navigate('/dashboard');
      setIsOpen(false);
    } else if (lowerLabel.includes('transaction')) {
      navigate('/transactions');
      setIsOpen(false);
    } else if (lowerLabel.includes('categories')) {
      navigate('/categories');
      setIsOpen(false);
    } else if (lowerLabel.includes('settings')) {
      navigate('/settings');
      setIsOpen(false);
    } else if (lowerLabel.includes('help')) {
      // Open help documentation
      window.open('/docs', '_blank');
    } else {
      // Default: ask the agent for clarification
      setInputValue(`I need help with: ${label}`);
      void handleSendMessage();
    }
  };

  const handleFileAttachment = () => {
    fileInputRef.current?.click();
  };
  
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    try {
      // Create a user message about the file upload
      const fileMessage: Message = {
        id: Date.now().toString(),
        type: 'user',
        content: `Uploading file: ${file.name}`,
        timestamp: new Date(),
      };
      
      setMessages((prev) => [...prev, fileMessage]);
      setIsTyping(true);
      
      // Upload file using FormData
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await fetch('/api/v1/transactions/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: formData,
      });
      
      if (response.ok) {
        const uploadResult = await response.json();
        
        // Create agent response about successful upload
        const agentResponse: Message = {
          id: (Date.now() + 1).toString(),
          type: 'agent',
          content: `File uploaded successfully! I found ${uploadResult.transactions_created || 'some'} transactions. You can now view them in the transactions page or ask me to analyze them.`,
          timestamp: new Date(),
          actions: [
            { label: 'View Transactions', action: () => { navigate('/transactions'); setIsOpen(false); } },
            { label: 'Analyze Spending', action: () => { navigate('/dashboard'); setIsOpen(false); } },
          ],
        };
        
        setMessages((prev) => [...prev, agentResponse]);
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: 'Sorry, there was an error uploading your file. Please try again or check that it\'s a valid financial file format.',
        timestamp: new Date(),
      };
      
      setMessages((prev) => [...prev, errorResponse]);
    } finally {
      setIsTyping(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const renderMessage = (message: Message) => {
    const isUser = message.type === 'user';

    return (
      <div
        key={message.id}
        className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}
      >
        <div
          className={`max-w-[80%] rounded-lg p-3 ${
            isUser
              ? 'bg-giki-primary text-white'
              : 'bg-white border border-slate-200 border-l-4 border-l-green-600 text-slate-900'
          }`}
        >
          <div className="whitespace-pre-wrap text-sm">{message.content}</div>

          {message.actions && (
            <div className="flex flex-wrap gap-2 mt-3">
              {message.actions.map((action, index) => (
                <Button
                  key={index}
                  size="sm"
                  variant={action.variant || 'default'}
                  onClick={action.action}
                  className="text-xs"
                >
                  {action.label}
                </Button>
              ))}
            </div>
          )}

          <div className="text-xs opacity-60 mt-1">
            {message.timestamp.toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </div>
        </div>
      </div>
    );
  };

  // Mobile full-screen view
  if (isMobile && isOpen) {
    return (
      <div className="fixed inset-0 z-50 bg-white flex flex-col">
        {/* Mobile Header */}
        <div className="flex items-center gap-3 p-4 border-b border-slate-200 bg-white">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggle}
            className="p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-giki-primary rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">g</span>
            </div>
            <span className="font-semibold text-slate-900">giki Assistant</span>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 bg-slate-50">
          {messages.map(renderMessage)}
          {isTyping && (
            <div className="flex justify-start mb-4">
              <div className="bg-white border border-slate-200 rounded-lg p-3">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce delay-100"></div>
                  <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce delay-200"></div>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Mobile Input */}
        <div className="p-4 bg-white border-t border-slate-200">
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleFileAttachment}
              className="p-2 flex-shrink-0"
            >
              <Paperclip className="h-5 w-5" />
            </Button>
            <Textarea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message..."
              className="flex-1 min-h-[40px] max-h-[120px] resize-none"
              rows={1}
            />
            <Button
              onClick={() => {
                handleSendMessage().catch(console.error);
              }}
              disabled={!inputValue.trim()}
              className="bg-giki-primary hover:bg-giki-primary/80 p-2 flex-shrink-0"
            >
              <Send className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Floating Action Button */}
      {!isOpen && (
        <div className="fixed bottom-6 right-6 z-40">
          <Button
            onClick={handleToggle}
            className="w-14 h-14 rounded-full bg-giki-primary hover:bg-giki-primary/80 shadow-lg hover:shadow-xl transition-all duration-300 group"
          >
            <MessageCircle className="h-6 w-6 text-white transition-transform group-hover:scale-110" />
          </Button>
        </div>
      )}

      {/* Desktop Chat Panel */}
      {isOpen && !isMobile && (
        <div className="fixed bottom-6 right-6 z-50">
          <Card className="w-[400px] h-[600px] shadow-2xl flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-slate-200">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-giki-primary rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">g</span>
                </div>
                <span className="font-semibold text-slate-900">
                  giki Assistant
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleToggle}
                className="p-1 hover:bg-slate-100"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Messages */}
            <CardContent className="flex-1 overflow-y-auto p-4 bg-slate-50">
              {messages.map(renderMessage)}
              {isTyping && (
                <div className="flex justify-start mb-4">
                  <div className="bg-white border border-slate-200 rounded-lg p-3">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce delay-100"></div>
                      <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce delay-200"></div>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </CardContent>

            {/* Input Area */}
            <div className="p-4 border-t border-slate-200 bg-white">
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleFileAttachment}
                  className="p-2 flex-shrink-0"
                >
                  <Paperclip className="h-4 w-4" />
                </Button>
                <Textarea
                  ref={inputRef}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Type your message..."
                  className="flex-1 min-h-[40px] max-h-[120px] resize-none"
                  rows={1}
                />
                <Button
                  onClick={() => {
                    handleSendMessage().catch(console.error);
                  }}
                  disabled={!inputValue.trim()}
                  className="bg-giki-primary hover:bg-giki-primary/80 p-2 flex-shrink-0"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Backdrop for mobile */}
      {isOpen && isMobile && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={handleToggle}
        />
      )}
      
      {/* Hidden file input for file uploads */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv,.xlsx,.xls,.pdf"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />
    </>
  );
};

export default UnifiedAgentPanel;
