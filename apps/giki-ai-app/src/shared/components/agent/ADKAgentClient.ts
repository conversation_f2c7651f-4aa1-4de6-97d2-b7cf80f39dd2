import { logger } from '@/shared/utils/errorHandling';

interface StreamChunk {
  content: string;
  type: string;
}

interface ADKAgentClientConfig {
  agentEndpoint: string;
  enableStreaming?: boolean;
  onStreamingUpdate?: (chunk: StreamChunk) => void;
}

interface AgentAPIResponse {
  response?: string;
  message?: string;
  metadata?: Record<string, unknown>;
  conversation_id?: string;
  agent_id?: string;
  stream_chunks?: StreamChunk[];
}

interface AgentResponse {
  response: string;
  metadata?: Record<string, unknown>;
  conversationId?: string;
  agentId?: string;
}

export class ADKAgentClient {
  private config: ADKAgentClientConfig;
  private conversationId?: string;

  constructor(config: ADKAgentClientConfig) {
    this.config = config;
  }

  async sendMessage(message: string): Promise<AgentResponse> {
    try {
      // Get the access token from localStorage
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(this.config.agentEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          query: message,
          conversation_id: this.conversationId,
          enable_streaming: this.config.enableStreaming,
        }),
      });

      if (!response.ok) {
        throw new Error(`Agent request failed: ${response.statusText}`);
      }

      const data = (await response.json()) as AgentAPIResponse;

      // Update conversation ID for future messages
      if (data.conversation_id) {
        this.conversationId = data.conversation_id;
      }

      // Handle streaming updates if enabled
      if (
        this.config.enableStreaming &&
        this.config.onStreamingUpdate &&
        data.stream_chunks
      ) {
        data.stream_chunks.forEach((chunk) => {
          this.config.onStreamingUpdate(chunk);
        });
      }

      return {
        response: data.response || data.message || 'No response received',
        metadata: data.metadata,
        conversationId: data.conversation_id,
        agentId: data.agent_id,
      };
    } catch (error) {
      logger.error(
        'Failed to send message to agent',
        'ADKAgentClient',
        error as Error,
      );
      throw error;
    }
  }

  resetConversation() {
    this.conversationId = undefined;
  }

  getConversationId(): string | undefined {
    return this.conversationId;
  }
}
