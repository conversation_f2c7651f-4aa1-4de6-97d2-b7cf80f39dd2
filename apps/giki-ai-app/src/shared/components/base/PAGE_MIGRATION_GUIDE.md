# Page Patterns Migration Guide

## Overview

This guide explains how to migrate existing page components to use the new unified base page components. The base page components consolidate common patterns and provide consistent layouts, loading states, error handling, and navigation across the application.

## Key Benefits of Base Page Components

### 1. **Consistent Page Structure**
- Standardized page headers with titles, descriptions, and actions
- Unified breadcrumb navigation and back button patterns
- Consistent spacing and layout across all pages

### 2. **Built-in State Management**
- Loading states with customizable loading indicators
- Error handling with retry functionality
- Empty states with actionable messages

### 3. **Enhanced User Experience**
- SEO-friendly page structure with proper document titles
- Accessibility features built-in
- Responsive design with mobile-friendly layouts

### 4. **Reduced Code Duplication**
- Common page patterns consolidated into reusable components
- Consistent data fetching and state management patterns
- Unified form validation and error handling

## Base Page Components

### 1. BasePage
General-purpose page component for all page types.

### 2. BaseDataPage
Specialized for data-driven pages with tables, charts, and analytics.

### 3. BaseDashboardPage
Optimized for dashboard layouts with widgets and real-time data.

### 4. BaseFormPage
Designed for form-heavy pages with validation and multi-step support.

## Migration Examples

### Example 1: Simple Page Migration

**Before:**
```tsx
// Old pattern in TransactionAnalysisPage.tsx
export const TransactionAnalysisPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  return (
    <div className="p-8 space-y-6 bg-white min-h-screen">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Transaction Analysis
          </h1>
          <p className="text-gray-600">
            Analyze your transaction patterns and trends
          </p>
        </div>
        <Button onClick={handleRefresh}>
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>
      
      {isLoading && <Loading />}
      {error && <Alert variant="destructive">{error}</Alert>}
      {!isLoading && !error && (
        <div>
          {/* Page content */}
        </div>
      )}
    </div>
  );
};
```

**After:**
```tsx
// New pattern using BasePage
import { BasePage } from '@/shared/components/base';

export const TransactionAnalysisPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  return (
    <BasePage
      title="Transaction Analysis"
      description="Analyze your transaction patterns and trends"
      loading={isLoading}
      error={error}
      onRefresh={handleRefresh}
      pageTitle="Transaction Analysis | Giki AI"
    >
      {/* Page content */}
    </BasePage>
  );
};
```

### Example 2: Data Page Migration

**Before:**
```tsx
// Old pattern in ReportsPage.tsx
export const ReportsPage: React.FC = () => {
  const [data, setData] = useState([]);
  const [filters, setFilters] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  
  return (
    <div className="p-page min-h-screen">
      <div className="space-section">
        <h1 className="text-3xl font-semibold">Financial Reports</h1>
        <p className="text-gray-500">Generate and export financial reports</p>
      </div>
      
      {/* Search and filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex gap-4">
            <Input
              placeholder="Search reports..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {/* Filter components */}
          </div>
        </CardContent>
      </Card>
      
      {/* Metrics */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        {/* Metric cards */}
      </div>
      
      {/* Data content */}
      {/* Export buttons */}
    </div>
  );
};
```

**After:**
```tsx
// New pattern using BaseDataPage
import { BaseDataPage } from '@/shared/components/base';

export const ReportsPage: React.FC = () => {
  const [data, setData] = useState([]);
  const [filters, setFilters] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  
  const dataFilters = [
    {
      key: 'category',
      label: 'Category',
      type: 'select' as const,
      options: categoryOptions,
    },
    {
      key: 'dateRange',
      label: 'Date Range',
      type: 'daterange' as const,
    },
  ];
  
  const exportOptions = [
    {
      format: 'excel' as const,
      label: 'Export to Excel',
      onExport: handleExcelExport,
    },
    {
      format: 'pdf' as const,
      label: 'Export to PDF',
      onExport: handlePdfExport,
    },
  ];
  
  return (
    <BaseDataPage
      title="Financial Reports"
      description="Generate and export financial reports"
      data={data}
      filters={dataFilters}
      onFiltersChange={setFilters}
      searchValue={searchQuery}
      onSearchChange={setSearchQuery}
      exportOptions={exportOptions}
      metrics={metrics}
      onRefresh={handleRefresh}
    >
      {/* Data table or charts */}
    </BaseDataPage>
  );
};
```

### Example 3: Dashboard Migration

**Before:**
```tsx
// Old pattern in DashboardPage.tsx
export const DashboardPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState('30d');
  const [widgets, setWidgets] = useState([]);
  
  return (
    <div className="p-6 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-semibold">Dashboard</h1>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="grid grid-cols-3 gap-4">
        {widgets.map(widget => (
          <Card key={widget.id}>
            {/* Widget content */}
          </Card>
        ))}
      </div>
    </div>
  );
};
```

**After:**
```tsx
// New pattern using BaseDashboardPage
import { BaseDashboardPage } from '@/shared/components/base';

export const DashboardPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState('30d');
  const [widgets, setWidgets] = useState([]);
  
  const dashboardWidgets = widgets.map(widget => ({
    id: widget.id,
    type: widget.type,
    title: widget.title,
    // ... other widget props
  }));
  
  return (
    <BaseDashboardPage
      title="Dashboard"
      widgets={dashboardWidgets}
      timeRange={timeRange}
      onTimeRangeChange={setTimeRange}
      autoRefresh={true}
      refreshInterval={30}
      onRefresh={handleRefresh}
    />
  );
};
```

### Example 4: Form Page Migration

**Before:**
```tsx
// Old pattern in LoginPage.tsx
export const LoginPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState([]);
  
  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-md mx-auto pt-20">
        <Card>
          <CardHeader>
            <CardTitle>Sign In</CardTitle>
            <CardDescription>Enter your credentials</CardDescription>
          </CardHeader>
          <CardContent>
            {errors.length > 0 && (
              <Alert variant="destructive">
                {/* Error display */}
              </Alert>
            )}
            
            <form onSubmit={handleSubmit}>
              {/* Form fields */}
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="animate-spin" />}
                Sign In
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
```

**After:**
```tsx
// New pattern using BaseFormPage
import { BaseFormPage } from '@/shared/components/base';

export const LoginPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState([]);
  
  return (
    <BaseFormPage
      title="Sign In"
      description="Enter your credentials to access your account"
      isSubmitting={isLoading}
      onSubmit={handleSubmit}
      errors={errors}
      isValid={isFormValid}
      submitLabel="Sign In"
      maxWidth="sm"
      pageTitle="Sign In | Giki AI"
    >
      {/* Form fields */}
    </BaseFormPage>
  );
};
```

## Migration Checklist

### For Each Page Component:

1. **Identify the page type:**
   - [ ] Simple content page → `BasePage`
   - [ ] Data/analytics page → `BaseDataPage`
   - [ ] Dashboard with widgets → `BaseDashboardPage`
   - [ ] Form/settings page → `BaseFormPage`

2. **Extract common patterns:**
   - [ ] Page title and description
   - [ ] Loading and error states
   - [ ] Navigation elements (breadcrumbs, back buttons)
   - [ ] Action buttons (refresh, export, etc.)

3. **Migrate state management:**
   - [ ] Move loading state to base component props
   - [ ] Move error handling to base component props
   - [ ] Consolidate data fetching patterns

4. **Update styling:**
   - [ ] Remove custom page layout CSS
   - [ ] Use base component layout props
   - [ ] Ensure responsive design works

5. **Test functionality:**
   - [ ] Verify all existing functionality works
   - [ ] Test loading and error states
   - [ ] Check responsive behavior
   - [ ] Validate accessibility features

## Common Patterns to Look For

### 1. Page Headers
Look for repeated patterns of:
- Page titles with consistent typography
- Descriptions or subtitles
- Action buttons in the header area

### 2. Loading States
Look for:
- Custom loading spinners or skeletons
- Loading state management with useState
- Conditional rendering based on loading state

### 3. Error Handling
Look for:
- Error state display with Alert components
- Retry functionality
- Error message formatting

### 4. Navigation
Look for:
- Back button implementations
- Breadcrumb navigation
- Page-specific navigation elements

### 5. Data Management
Look for:
- Data fetching with useEffect
- Filter and search implementations
- Export functionality
- Pagination logic

## Best Practices

1. **Start with Simple Pages**: Begin migration with simple content pages before tackling complex data pages.

2. **Preserve Existing Functionality**: Ensure all existing features continue to work after migration.

3. **Test Thoroughly**: Test all page states (loading, error, empty, success) after migration.

4. **Update Tests**: Update component tests to work with the new base component structure.

5. **Document Changes**: Update any documentation that references the old page structure.

## Troubleshooting

### Common Issues:

1. **Styling Conflicts**: Remove custom page-level CSS that conflicts with base component styles.

2. **State Management**: Ensure state is properly passed to base component props.

3. **Event Handlers**: Verify all event handlers are properly connected to base component callbacks.

4. **TypeScript Errors**: Update type definitions to match base component prop types.

## Support

For questions or issues with the migration:

1. Check the base component source code for implementation details
2. Review the usage examples in the component documentation
3. Test with a simple page first to understand the patterns
4. Use TypeScript to catch prop mismatches early
