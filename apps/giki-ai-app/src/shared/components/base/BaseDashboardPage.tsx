/**
 * Base Dashboard Page Component - Specialized for dashboard layouts
 * ================================================================
 * 
 * This component extends BasePage with specialized functionality for
 * dashboard pages with widgets, metrics, and real-time data displays.
 * 
 * Key features:
 * - Widget grid layouts with responsive breakpoints
 * - Real-time data updates and refresh intervals
 * - Dashboard-specific navigation and time range selection
 * - Widget management (add, remove, resize, reorder)
 * - Export and sharing capabilities for dashboards
 * - Performance monitoring and optimization
 */

import React, { ReactNode, useState, useEffect, useCallback } from 'react';
import { Calendar, Settings, Share, Maximize2, Minimize2, MoreVertical } from 'lucide-react';
import { BasePage, BasePageProps } from './BasePage';
import { BaseDashboardWidget, BaseWidgetProps } from './BaseDashboardWidget';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/shared/components/ui/dropdown-menu';
import { cn } from '@/shared/utils/utils';

export interface DashboardWidget extends BaseWidgetProps {
  id: string;
  gridArea?: string;
  order?: number;
  isExpanded?: boolean;
  canExpand?: boolean;
  canRemove?: boolean;
  canConfigure?: boolean;
}

export interface TimeRangeOption {
  value: string;
  label: string;
  description?: string;
}

export interface DashboardConfig {
  layout: 'grid' | 'masonry' | 'flex';
  columns: number;
  gap: 'sm' | 'md' | 'lg';
  autoRefresh: boolean;
  refreshInterval: number; // in seconds
}

export interface BaseDashboardPageProps extends Omit<BasePageProps, 'children'> {
  // Widgets
  widgets: DashboardWidget[];
  onWidgetAction?: (widgetId: string, action: 'expand' | 'collapse' | 'remove' | 'configure') => void;
  onWidgetReorder?: (widgets: DashboardWidget[]) => void;
  
  // Time range selection
  timeRange?: string;
  timeRangeOptions?: TimeRangeOption[];
  onTimeRangeChange?: (range: string) => void;
  
  // Dashboard configuration
  config?: DashboardConfig;
  onConfigChange?: (config: DashboardConfig) => void;
  
  // Real-time updates
  autoRefresh?: boolean;
  refreshInterval?: number;
  lastUpdated?: Date;
  
  // Layout options
  showTimeRangeSelector?: boolean;
  showConfigButton?: boolean;
  showShareButton?: boolean;
  allowWidgetManagement?: boolean;
  
  // Custom components
  CustomTimeRangeSelector?: React.ComponentType<{
    value: string;
    options: TimeRangeOption[];
    onChange: (value: string) => void;
  }>;
  
  // Additional content
  children?: ReactNode;
}

export function BaseDashboardPage({
  widgets = [],
  onWidgetAction,
  onWidgetReorder,
  timeRange = '30d',
  timeRangeOptions = [
    { value: '24h', label: 'Last 24 hours' },
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '1y', label: 'Last year' },
  ],
  onTimeRangeChange,
  config = {
    layout: 'grid',
    columns: 3,
    gap: 'md',
    autoRefresh: true,
    refreshInterval: 30,
  },
  onConfigChange,
  autoRefresh = true,
  refreshInterval = 30,
  lastUpdated,
  showTimeRangeSelector = true,
  showConfigButton = true,
  showShareButton = true,
  allowWidgetManagement = true,
  CustomTimeRangeSelector,
  children,
  ...basePageProps
}: BaseDashboardPageProps) {
  const [expandedWidget, setExpandedWidget] = useState<string | null>(null);
  const [isConfiguring, setIsConfiguring] = useState(false);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh || !basePageProps.onRefresh) return;

    const interval = setInterval(() => {
      basePageProps.onRefresh?.();
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, basePageProps.onRefresh]);

  // Handle widget actions
  const handleWidgetAction = useCallback((widgetId: string, action: 'expand' | 'collapse' | 'remove' | 'configure') => {
    if (action === 'expand') {
      setExpandedWidget(widgetId);
    } else if (action === 'collapse') {
      setExpandedWidget(null);
    }
    
    onWidgetAction?.(widgetId, action);
  }, [onWidgetAction]);

  // Get grid layout classes
  const getGridLayoutClass = () => {
    switch (config.layout) {
      case 'grid':
        return `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-${config.columns}`;
      case 'masonry':
        return 'columns-1 md:columns-2 lg:columns-3 space-y-4';
      case 'flex':
        return 'flex flex-wrap';
      default:
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
    }
  };

  // Get gap classes
  const getGapClass = () => {
    switch (config.gap) {
      case 'sm': return 'gap-2';
      case 'md': return 'gap-4';
      case 'lg': return 'gap-6';
      default: return 'gap-4';
    }
  };

  // Render time range selector
  const renderTimeRangeSelector = () => {
    if (!showTimeRangeSelector) return null;

    if (CustomTimeRangeSelector) {
      return (
        <CustomTimeRangeSelector
          value={timeRange}
          options={timeRangeOptions}
          onChange={onTimeRangeChange || (() => {})}
        />
      );
    }

    return (
      <Select value={timeRange} onValueChange={onTimeRangeChange}>
        <SelectTrigger className="w-40">
          <Calendar className="h-4 w-4 mr-2" />
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {timeRangeOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              <div>
                <div>{option.label}</div>
                {option.description && (
                  <div className="text-xs text-muted-foreground">
                    {option.description}
                  </div>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  };

  // Render dashboard controls
  const renderDashboardControls = () => {
    const controls = [];

    if (showTimeRangeSelector) {
      controls.push(renderTimeRangeSelector());
    }

    if (showConfigButton) {
      controls.push(
        <Button
          key="config"
          variant="outline"
          size="sm"
          onClick={() => setIsConfiguring(true)}
        >
          <Settings className="h-4 w-4 mr-2" />
          Configure
        </Button>
      );
    }

    if (showShareButton) {
      controls.push(
        <Button
          key="share"
          variant="outline"
          size="sm"
          onClick={() => {
            // Handle share functionality
            navigator.clipboard.writeText(window.location.href);
          }}
        >
          <Share className="h-4 w-4 mr-2" />
          Share
        </Button>
      );
    }

    if (controls.length === 0) return null;

    return (
      <div className="flex items-center gap-2 mb-6">
        {controls}
      </div>
    );
  };

  // Render widget with management controls
  const renderWidget = (widget: DashboardWidget) => {
    const isExpanded = expandedWidget === widget.id;
    
    return (
      <div
        key={widget.id}
        className={cn(
          'relative group',
          isExpanded && 'fixed inset-4 z-50 bg-background border rounded-lg shadow-lg'
        )}
      >
        {/* Widget management controls */}
        {allowWidgetManagement && (
          <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {widget.canExpand && (
                  <DropdownMenuItem
                    onClick={() => handleWidgetAction(widget.id, isExpanded ? 'collapse' : 'expand')}
                  >
                    {isExpanded ? (
                      <>
                        <Minimize2 className="h-4 w-4 mr-2" />
                        Collapse
                      </>
                    ) : (
                      <>
                        <Maximize2 className="h-4 w-4 mr-2" />
                        Expand
                      </>
                    )}
                  </DropdownMenuItem>
                )}
                {widget.canConfigure && (
                  <DropdownMenuItem
                    onClick={() => handleWidgetAction(widget.id, 'configure')}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Configure
                  </DropdownMenuItem>
                )}
                {widget.canRemove && (
                  <DropdownMenuItem
                    onClick={() => handleWidgetAction(widget.id, 'remove')}
                    className="text-destructive"
                  >
                    Remove
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}

        {/* Expanded widget overlay */}
        {isExpanded && (
          <div className="absolute inset-0 bg-background/80 backdrop-blur-sm" />
        )}

        {/* Widget content */}
        <BaseDashboardWidget
          {...widget}
          className={cn(
            widget.className,
            isExpanded && 'h-full'
          )}
        />
      </div>
    );
  };

  // Render widgets grid
  const renderWidgets = () => {
    if (widgets.length === 0) {
      return (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12 text-center">
            <p className="text-muted-foreground mb-4">No widgets configured</p>
            <Button variant="outline">Add Widget</Button>
          </CardContent>
        </Card>
      );
    }

    return (
      <div className={cn(getGridLayoutClass(), getGapClass())}>
        {widgets
          .sort((a, b) => (a.order || 0) - (b.order || 0))
          .map(renderWidget)}
      </div>
    );
  };

  // Enhanced page props with dashboard-specific features
  const enhancedPageProps: BasePageProps = {
    ...basePageProps,
    lastUpdated,
    children: (
      <>
        {renderDashboardControls()}
        {renderWidgets()}
        {children}
      </>
    ),
  };

  return <BasePage {...enhancedPageProps} />;
}

// Convenience components for common dashboard widget types

export interface MetricsDashboardProps extends Omit<BaseDashboardPageProps, 'widgets'> {
  metrics: Array<{
    id: string;
    label: string;
    value: string | number;
    change?: number;
    trend?: 'up' | 'down' | 'neutral';
    format?: 'number' | 'currency' | 'percentage';
  }>;
}

export function MetricsDashboard({ metrics, ...props }: MetricsDashboardProps) {
  const widgets: DashboardWidget[] = metrics.map((metric) => ({
    id: metric.id,
    type: 'metric',
    title: metric.label,
    metric: {
      label: metric.label,
      value: metric.value,
      change: metric.change,
      trend: metric.trend,
      format: metric.format,
    },
    size: 'sm',
    canExpand: true,
  }));

  return <BaseDashboardPage {...props} widgets={widgets} />;
}
