/**
 * Base Dashboard Widget Component - Unified widget patterns
 * =========================================================
 * 
 * This component consolidates duplicate dashboard widget patterns across the application:
 * - Metric cards and KPI displays
 * - Chart widgets and data visualizations
 * - Progress indicators and status widgets
 * - Action cards and quick actions
 * - Loading and error states for widgets
 * 
 * Key features:
 * - Consistent widget layouts and styling
 * - Standardized metric display patterns
 * - Loading states and error handling
 * - Responsive design with mobile-friendly layouts
 * - Type-safe props and data handling
 * - Consistent action patterns and interactions
 */

import React from 'react';
import { cn } from '@/shared/utils/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Progress } from '@/shared/components/ui/progress';
import { LoadingSpinner } from '@/shared/components/ui/standardized-loading';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';

// Widget types for different use cases
export type BaseWidgetType = 
  | 'metric'         // Single metric display
  | 'chart'          // Chart or visualization
  | 'progress'       // Progress indicator
  | 'list'           // List of items
  | 'action'         // Action card
  | 'custom';        // Custom content

export type BaseWidgetSize = 'sm' | 'md' | 'lg' | 'xl';

export interface BaseWidgetMetric {
  label: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  trend?: 'up' | 'down' | 'neutral';
  format?: 'number' | 'currency' | 'percentage';
  precision?: number;
}

export interface BaseWidgetAction {
  label: string;
  icon?: React.ReactNode;
  onClick: () => void;
  variant?: 'default' | 'outline' | 'ghost';
  disabled?: boolean;
}

export interface BaseWidgetListItem {
  id: string;
  label: string;
  value?: string | number;
  status?: string;
  badge?: string;
  onClick?: () => void;
}

export interface BaseWidgetProps {
  // Widget configuration
  type?: BaseWidgetType;
  title?: string;
  description?: string;
  size?: BaseWidgetSize;
  
  // Content
  metric?: BaseWidgetMetric;
  metrics?: BaseWidgetMetric[];
  progress?: {
    value: number;
    max?: number;
    label?: string;
    showPercentage?: boolean;
  };
  listItems?: BaseWidgetListItem[];
  children?: React.ReactNode;
  
  // Actions
  actions?: BaseWidgetAction[];
  primaryAction?: BaseWidgetAction;
  
  // States
  isLoading?: boolean;
  error?: string;
  isEmpty?: boolean;
  
  // Styling
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  variant?: 'default' | 'outline' | 'ghost';
  
  // Event handlers
  onRefresh?: () => void;
  onExpand?: () => void;
  
  // Custom components
  CustomHeader?: React.ComponentType<{ title?: string; description?: string; actions?: BaseWidgetAction[] }>;
  CustomContent?: React.ComponentType;
  CustomFooter?: React.ComponentType<{ actions?: BaseWidgetAction[] }>;
}

export function BaseDashboardWidget({
  type = 'custom',
  title,
  description,
  size = 'md',
  metric,
  metrics,
  progress,
  listItems,
  children,
  actions,
  primaryAction,
  isLoading = false,
  error,
  isEmpty = false,
  className,
  headerClassName,
  contentClassName,
  variant = 'default',
  onRefresh,
  onExpand,
  CustomHeader,
  CustomContent,
  CustomFooter,
}: BaseWidgetProps) {

  // Format metric values
  const formatMetricValue = (value: string | number, format?: string, precision?: number) => {
    if (typeof value === 'string') return value;
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: precision || 0,
          maximumFractionDigits: precision || 2,
        }).format(value);
      
      case 'percentage':
        return `${(value * 100).toFixed(precision || 1)}%`;
      
      case 'number':
        return new Intl.NumberFormat('en-US', {
          minimumFractionDigits: precision || 0,
          maximumFractionDigits: precision || 2,
        }).format(value);
      
      default:
        return value.toString();
    }
  };

  // Get trend indicator
  const getTrendIndicator = (trend?: 'up' | 'down' | 'neutral', change?: number) => {
    if (!trend || change === undefined) return null;
    
    const isPositive = change > 0;
    const isNegative = change < 0;
    
    return (
      <span className={cn(
        'text-xs font-medium',
        trend === 'up' && isPositive && 'text-green-600',
        trend === 'up' && isNegative && 'text-red-600',
        trend === 'down' && isPositive && 'text-red-600',
        trend === 'down' && isNegative && 'text-green-600',
        trend === 'neutral' && 'text-gray-600'
      )}>
        {trend === 'up' && (isPositive ? '↗' : '↘')}
        {trend === 'down' && (isPositive ? '↘' : '↗')}
        {trend === 'neutral' && '→'}
        {Math.abs(change).toFixed(1)}%
      </span>
    );
  };

  // Get widget size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'min-h-[120px]';
      case 'md':
        return 'min-h-[160px]';
      case 'lg':
        return 'min-h-[200px]';
      case 'xl':
        return 'min-h-[240px]';
      default:
        return 'min-h-[160px]';
    }
  };

  // Render widget header
  const renderHeader = () => {
    if (CustomHeader) {
      return <CustomHeader title={title} description={description} actions={actions} />;
    }

    if (!title && !description && !actions?.length && !onRefresh) return null;

    return (
      <CardHeader className={cn('pb-2', headerClassName)}>
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            {title && <CardTitle className="text-sm font-medium">{title}</CardTitle>}
            {description && <p className="text-xs text-muted-foreground">{description}</p>}
          </div>
          
          {(actions?.length || onRefresh) && (
            <div className="flex items-center space-x-1">
              {onRefresh && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRefresh}
                  disabled={isLoading}
                  className="h-6 w-6 p-0"
                >
                  ⟲
                </Button>
              )}
              
              {actions?.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || 'ghost'}
                  size="sm"
                  onClick={action.onClick}
                  disabled={action.disabled || isLoading}
                  className="h-6 px-2"
                >
                  {action.icon && <span className="mr-1">{action.icon}</span>}
                  {action.label}
                </Button>
              ))}
            </div>
          )}
        </div>
      </CardHeader>
    );
  };

  // Render widget content based on type
  const renderContent = () => {
    if (CustomContent) {
      return <CustomContent />;
    }

    // Loading state
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="md" />
        </div>
      );
    }

    // Error state
    if (error) {
      return (
        <Alert variant="destructive" className="m-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      );
    }

    // Empty state
    if (isEmpty) {
      return (
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <p className="text-sm text-muted-foreground">No data available</p>
        </div>
      );
    }

    // Metric widget
    if (type === 'metric' && (metric || metrics)) {
      const metricsToShow = metrics || (metric ? [metric] : []);
      
      return (
        <div className="space-y-4">
          {metricsToShow.map((m, index) => (
            <div key={index} className="space-y-1">
              <div className="flex items-baseline justify-between">
                <p className="text-xs text-muted-foreground">{m.label}</p>
                {getTrendIndicator(m.trend, m.change)}
              </div>
              <p className="text-2xl font-bold">
                {formatMetricValue(m.value, m.format, m.precision)}
              </p>
              {m.changeLabel && (
                <p className="text-xs text-muted-foreground">{m.changeLabel}</p>
              )}
            </div>
          ))}
        </div>
      );
    }

    // Progress widget
    if (type === 'progress' && progress) {
      const percentage = ((progress.value / (progress.max || 100)) * 100);
      
      return (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium">{progress.label}</p>
            {progress.showPercentage && (
              <p className="text-sm text-muted-foreground">{percentage.toFixed(0)}%</p>
            )}
          </div>
          <Progress value={percentage} className="h-2" />
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{progress.value}</span>
            <span>{progress.max || 100}</span>
          </div>
        </div>
      );
    }

    // List widget
    if (type === 'list' && listItems) {
      return (
        <div className="space-y-2">
          {listItems.map((item) => (
            <div
              key={item.id}
              className={cn(
                'flex items-center justify-between p-2 rounded-sm',
                item.onClick && 'cursor-pointer hover:bg-muted/50'
              )}
              onClick={item.onClick}
            >
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{item.label}</span>
                {item.badge && <Badge variant="outline" className="text-xs">{item.badge}</Badge>}
              </div>
              <div className="flex items-center space-x-2">
                {item.value && (
                  <span className="text-sm text-muted-foreground">{item.value}</span>
                )}
                {item.status && (
                  <Badge variant={item.status === 'active' ? 'default' : 'secondary'}>
                    {item.status}
                  </Badge>
                )}
              </div>
            </div>
          ))}
        </div>
      );
    }

    // Action widget
    if (type === 'action' && primaryAction) {
      return (
        <div className="flex flex-col items-center justify-center py-8 space-y-4">
          <Button
            onClick={primaryAction.onClick}
            disabled={primaryAction.disabled || isLoading}
            variant={primaryAction.variant || 'default'}
            className="w-full"
          >
            {primaryAction.icon && <span className="mr-2">{primaryAction.icon}</span>}
            {primaryAction.label}
          </Button>
        </div>
      );
    }

    // Custom content
    return children;
  };

  // Render widget footer
  const renderFooter = () => {
    if (CustomFooter) {
      return <CustomFooter actions={actions} />;
    }

    if (!actions?.length || actions.length <= 2) return null;

    return (
      <div className="px-4 pb-4">
        <div className="flex items-center justify-between space-x-2">
          {actions.slice(2).map((action, index) => (
            <Button
              key={index}
              variant={action.variant || 'outline'}
              size="sm"
              onClick={action.onClick}
              disabled={action.disabled || isLoading}
              className="flex-1"
            >
              {action.icon && <span className="mr-1">{action.icon}</span>}
              {action.label}
            </Button>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Card className={cn(
      getSizeClasses(),
      variant === 'outline' && 'border-2',
      variant === 'ghost' && 'border-0 shadow-none',
      className
    )}>
      {renderHeader()}
      
      <CardContent className={cn('pt-0', contentClassName)}>
        {renderContent()}
      </CardContent>
      
      {renderFooter()}
    </Card>
  );
}

// Convenience components for common widget types

export interface MetricWidgetProps extends Omit<BaseWidgetProps, 'type'> {
  metric: BaseWidgetMetric;
}

export function MetricWidget(props: MetricWidgetProps) {
  return <BaseDashboardWidget {...props} type="metric" />;
}

export interface ProgressWidgetProps extends Omit<BaseWidgetProps, 'type'> {
  progress: NonNullable<BaseWidgetProps['progress']>;
}

export function ProgressWidget(props: ProgressWidgetProps) {
  return <BaseDashboardWidget {...props} type="progress" />;
}

export interface ListWidgetProps extends Omit<BaseWidgetProps, 'type'> {
  listItems: BaseWidgetListItem[];
}

export function ListWidget(props: ListWidgetProps) {
  return <BaseDashboardWidget {...props} type="list" />;
}

export interface ActionWidgetProps extends Omit<BaseWidgetProps, 'type'> {
  primaryAction: BaseWidgetAction;
}

export function ActionWidget(props: ActionWidgetProps) {
  return <BaseDashboardWidget {...props} type="action" />;
}
