/**
 * Base Modal Component - Unified modal patterns
 * =============================================
 * 
 * This component consolidates duplicate modal patterns across the application:
 * - Confirmation dialogs
 * - Edit modals and forms
 * - Info modals and details
 * - Action modals with custom content
 * - Loading and error states in modals
 * 
 * Key features:
 * - Consistent modal layouts and styling
 * - Standardized action buttons and patterns
 * - Loading states and error handling
 * - Responsive design with mobile-friendly layouts
 * - Type-safe props and event handling
 * - Consistent animation and behavior
 */

import React from 'react';
import { cn } from '@/shared/utils/utils';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog';
import { Button } from '@/shared/components/ui/button';
import { LoadingSpinner } from '@/shared/components/ui/standardized-loading';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';

// Modal types for different use cases
export type BaseModalType = 
  | 'info'           // Information display
  | 'confirm'        // Confirmation dialog
  | 'form'           // Form modal
  | 'custom'         // Custom content
  | 'loading'        // Loading state
  | 'error';         // Error display

export type BaseModalSize = 'sm' | 'md' | 'lg' | 'xl' | 'full';

export interface BaseModalAction {
  label: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  onClick: () => void | Promise<void>;
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
}

export interface BaseModalProps {
  // Modal state
  open: boolean;
  onOpenChange: (open: boolean) => void;
  
  // Content
  type?: BaseModalType;
  title?: string;
  description?: string;
  children?: React.ReactNode;
  
  // Configuration
  size?: BaseModalSize;
  closable?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  
  // Actions
  actions?: BaseModalAction[];
  primaryAction?: BaseModalAction;
  secondaryAction?: BaseModalAction;
  showCancel?: boolean;
  cancelText?: string;
  
  // States
  isLoading?: boolean;
  error?: string;
  
  // Styling
  className?: string;
  contentClassName?: string;
  headerClassName?: string;
  footerClassName?: string;
  
  // Event handlers
  onClose?: () => void;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void;
  
  // Custom components
  CustomHeader?: React.ComponentType<{ title?: string; description?: string }>;
  CustomFooter?: React.ComponentType<{ actions?: BaseModalAction[] }>;
  CustomContent?: React.ComponentType;
}

export function BaseModal({
  open,
  onOpenChange,
  type = 'custom',
  title,
  description,
  children,
  size = 'md',
  closable = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  actions,
  primaryAction,
  secondaryAction,
  showCancel = false,
  cancelText = 'Cancel',
  isLoading = false,
  error,
  className,
  contentClassName,
  headerClassName,
  footerClassName,
  onClose,
  onConfirm,
  onCancel,
  CustomHeader,
  CustomFooter,
  CustomContent,
}: BaseModalProps) {
  
  // Handle modal close
  const handleClose = () => {
    if (!closable && !closeOnOverlayClick) return;
    onClose?.();
    onOpenChange(false);
  };

  // Handle confirm action
  const handleConfirm = async () => {
    if (primaryAction) {
      await primaryAction.onClick();
    } else if (onConfirm) {
      await onConfirm();
    }
  };

  // Handle cancel action
  const handleCancel = () => {
    if (secondaryAction) {
      secondaryAction.onClick();
    } else if (onCancel) {
      onCancel();
    } else {
      handleClose();
    }
  };

  // Get modal size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'max-w-md';
      case 'md':
        return 'max-w-lg';
      case 'lg':
        return 'max-w-2xl';
      case 'xl':
        return 'max-w-4xl';
      case 'full':
        return 'max-w-[95vw] max-h-[95vh]';
      default:
        return 'max-w-lg';
    }
  };

  // Render modal header
  const renderHeader = () => {
    if (CustomHeader) {
      return <CustomHeader title={title} description={description} />;
    }

    if (!title && !description) return null;

    return (
      <DialogHeader className={headerClassName}>
        {title && <DialogTitle>{title}</DialogTitle>}
        {description && <DialogDescription>{description}</DialogDescription>}
      </DialogHeader>
    );
  };

  // Render modal content based on type
  const renderContent = () => {
    if (CustomContent) {
      return <CustomContent />;
    }

    // Loading state
    if (isLoading || type === 'loading') {
      return (
        <div className="flex flex-col items-center justify-center py-8 space-y-4">
          <LoadingSpinner size="lg" />
          <p className="text-sm text-muted-foreground">
            {children || 'Loading...'}
          </p>
        </div>
      );
    }

    // Error state
    if (error || type === 'error') {
      return (
        <Alert variant="destructive">
          <AlertDescription>
            {error || children || 'An error occurred'}
          </AlertDescription>
        </Alert>
      );
    }

    // Confirmation dialog
    if (type === 'confirm') {
      return (
        <div className="py-4">
          <p className="text-sm text-muted-foreground">
            {children || 'Are you sure you want to continue?'}
          </p>
        </div>
      );
    }

    // Info modal
    if (type === 'info') {
      return (
        <div className="py-4">
          {children}
        </div>
      );
    }

    // Form or custom content
    return children;
  };

  // Render modal footer
  const renderFooter = () => {
    if (CustomFooter) {
      return <CustomFooter actions={actions} />;
    }

    // Don't show footer for loading or if no actions
    if (isLoading || type === 'loading') return null;

    const footerActions = actions || [];
    
    // Add default actions based on type
    if (type === 'confirm') {
      if (!primaryAction && !secondaryAction && footerActions.length === 0) {
        footerActions.push(
          {
            label: 'Confirm',
            variant: 'default',
            onClick: handleConfirm,
          },
          {
            label: cancelText,
            variant: 'outline',
            onClick: handleCancel,
          }
        );
      }
    }

    // Add primary and secondary actions
    if (primaryAction) {
      footerActions.unshift(primaryAction);
    }
    if (secondaryAction) {
      footerActions.push(secondaryAction);
    }

    // Add cancel button if requested
    if (showCancel && !footerActions.some(action => action.label === cancelText)) {
      footerActions.push({
        label: cancelText,
        variant: 'outline',
        onClick: handleCancel,
      });
    }

    if (footerActions.length === 0) return null;

    return (
      <DialogFooter className={footerClassName}>
        <div className="flex items-center justify-end space-x-2">
          {footerActions.map((action, index) => (
            <Button
              key={index}
              variant={action.variant || 'default'}
              onClick={action.onClick}
              disabled={action.disabled || isLoading}
              className="min-w-[80px]"
            >
              {action.loading && <LoadingSpinner size="sm" className="mr-2" />}
              {action.icon && <span className="mr-2">{action.icon}</span>}
              {action.label}
            </Button>
          ))}
        </div>
      </DialogFooter>
    );
  };

  return (
    <Dialog 
      open={open} 
      onOpenChange={closeOnOverlayClick ? onOpenChange : undefined}
    >
      <DialogContent
        className={cn(
          getSizeClasses(),
          size === 'full' && 'h-full overflow-auto',
          contentClassName,
          className
        )}
        onEscapeKeyDown={closeOnEscape ? undefined : (e) => e.preventDefault()}
        onPointerDownOutside={closeOnOverlayClick ? undefined : (e) => e.preventDefault()}
      >
        {renderHeader()}
        
        <div className="flex-1 overflow-auto">
          {renderContent()}
        </div>
        
        {renderFooter()}
      </DialogContent>
    </Dialog>
  );
}

// Convenience components for common modal types

export interface ConfirmModalProps extends Omit<BaseModalProps, 'type'> {
  confirmText?: string;
  confirmVariant?: 'default' | 'destructive';
  onConfirm: () => void | Promise<void>;
}

export function ConfirmModal({
  confirmText = 'Confirm',
  confirmVariant = 'default',
  onConfirm,
  ...props
}: ConfirmModalProps) {
  return (
    <BaseModal
      {...props}
      type="confirm"
      primaryAction={{
        label: confirmText,
        variant: confirmVariant,
        onClick: onConfirm,
      }}
      showCancel={true}
    />
  );
}

export interface InfoModalProps extends Omit<BaseModalProps, 'type'> {
  okText?: string;
}

export function InfoModal({
  okText = 'OK',
  ...props
}: InfoModalProps) {
  return (
    <BaseModal
      {...props}
      type="info"
      primaryAction={{
        label: okText,
        variant: 'default',
        onClick: () => props.onOpenChange(false),
      }}
    />
  );
}

export interface LoadingModalProps extends Omit<BaseModalProps, 'type'> {
  loadingText?: string;
}

export function LoadingModal({
  loadingText = 'Loading...',
  ...props
}: LoadingModalProps) {
  return (
    <BaseModal
      {...props}
      type="loading"
      closable={false}
      closeOnOverlayClick={false}
      closeOnEscape={false}
    >
      {loadingText}
    </BaseModal>
  );
}

export interface ErrorModalProps extends Omit<BaseModalProps, 'type'> {
  errorMessage: string;
  retryAction?: () => void;
}

export function ErrorModal({
  errorMessage,
  retryAction,
  ...props
}: ErrorModalProps) {
  const actions: BaseModalAction[] = [];
  
  if (retryAction) {
    actions.push({
      label: 'Retry',
      variant: 'default',
      onClick: retryAction,
    });
  }
  
  actions.push({
    label: 'Close',
    variant: 'outline',
    onClick: () => props.onOpenChange(false),
  });

  return (
    <BaseModal
      {...props}
      type="error"
      error={errorMessage}
      actions={actions}
    />
  );
}
