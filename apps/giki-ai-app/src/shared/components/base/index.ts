/**
 * Base Components Index - Unified component patterns
 * ==================================================
 * 
 * This file exports all base components that consolidate duplicate patterns
 * across the application. These components provide consistent, reusable
 * patterns for common UI structures.
 * 
 * Component Categories:
 * - Base UI Components (tables, forms, modals, widgets)
 * - Base Page Components (pages, data pages, dashboards, forms)
 * - Specialized Components (auth, onboarding, reports)
 */

// Base UI Components
export { BaseDataTable } from './BaseDataTable';
export type { BaseDataTableProps, TableColumn, TableAction } from './BaseDataTable';

export { BaseForm } from './BaseForm';
export type { BaseFormProps, FormField, FormSection } from './BaseForm';

export { BaseModal } from './BaseModal';
export type { BaseModalProps } from './BaseModal';

export { BaseDashboardWidget, MetricWidget, ProgressWidget, ListWidget, ActionWidget } from './BaseDashboardWidget';
export type { 
  BaseWidgetProps, 
  BaseWidgetType, 
  BaseWidgetSize, 
  BaseWidgetMetric, 
  BaseWidgetAction, 
  BaseWidgetListItem,
  MetricWidgetProps,
  ProgressWidgetProps,
  ListWidgetProps,
  ActionWidgetProps
} from './BaseDashboardWidget';

// Base Page Components
export { BasePage } from './BasePage';
export type { BasePageProps, PageAction, BreadcrumbItem } from './BasePage';

export { BaseDataPage } from './BaseDataPage';
export type { 
  BaseDataPageProps, 
  FilterOption, 
  DataFilter, 
  ExportOption, 
  DataMetric 
} from './BaseDataPage';

export { BaseDashboardPage, MetricsDashboard } from './BaseDashboardPage';
export type { 
  BaseDashboardPageProps, 
  DashboardWidget, 
  TimeRangeOption, 
  DashboardConfig,
  MetricsDashboardProps
} from './BaseDashboardPage';

export { BaseFormPage } from './BaseFormPage';
export type { 
  BaseFormPageProps, 
  FormStep, 
  FormValidationError 
} from './BaseFormPage';

// Convenience re-exports for common patterns
export {
  // Common UI components that are frequently used with base components
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Badge,
  Alert,
  AlertDescription,
  Progress,
  Loading,
} from '../ui';

// Utility functions for base components
export { cn } from '@/shared/utils/utils';

// Common hooks that work well with base components
export { useToast } from '../ui/use-toast';

/**
 * Usage Examples:
 * 
 * // Simple page with header and content
 * <BasePage title="My Page" description="Page description">
 *   <div>Page content</div>
 * </BasePage>
 * 
 * // Data page with filtering and export
 * <BaseDataPage
 *   title="Transactions"
 *   data={transactions}
 *   filters={filters}
 *   exportOptions={exportOptions}
 *   metrics={metrics}
 * >
 *   <BaseDataTable columns={columns} data={transactions} />
 * </BaseDataPage>
 * 
 * // Dashboard with widgets
 * <BaseDashboardPage
 *   title="Dashboard"
 *   widgets={widgets}
 *   timeRange="30d"
 *   autoRefresh={true}
 * />
 * 
 * // Form page with validation
 * <BaseFormPage
 *   title="Settings"
 *   onSubmit={handleSubmit}
 *   errors={errors}
 *   isValid={isValid}
 * >
 *   <BaseForm fields={fields} />
 * </BaseFormPage>
 * 
 * // Multi-step form
 * <BaseFormPage
 *   title="Onboarding"
 *   steps={steps}
 *   currentStep={currentStep}
 *   onStepChange={setCurrentStep}
 *   formLayout="wizard"
 * />
 */
