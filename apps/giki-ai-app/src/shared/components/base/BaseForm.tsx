/**
 * Base Form Component - Unified form patterns
 * ===========================================
 * 
 * This component consolidates duplicate form patterns across the application:
 * - Create/edit forms across all domains
 * - Search and filter forms
 * - Modal forms and dialog forms
 * - Form validation and error handling
 * - Loading states and submission handling
 * 
 * Key features:
 * - Consistent form layouts and styling
 * - Standardized validation and error display
 * - Loading states and submission handling
 * - Responsive design with mobile-friendly layouts
 * - Type-safe field definitions
 * - Consistent button patterns and actions
 */

import React, { useState } from 'react';
import { useForm, UseFormReturn, FieldValues, Path } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { cn } from '@/shared/utils/utils';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Textarea } from '@/shared/components/ui/textarea';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { Switch } from '@/shared/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shared/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { LoadingSpinner } from '@/shared/components/ui/standardized-loading';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';

// Base field types
export type BaseFieldType = 
  | 'text' 
  | 'email' 
  | 'password' 
  | 'number' 
  | 'textarea' 
  | 'select' 
  | 'checkbox' 
  | 'switch' 
  | 'date' 
  | 'currency'
  | 'custom';

export interface BaseFormField<T extends FieldValues = FieldValues> {
  name: Path<T>;
  label: string;
  type: BaseFieldType;
  placeholder?: string;
  description?: string;
  required?: boolean;
  disabled?: boolean;
  
  // Field-specific options
  options?: Array<{ value: string; label: string }>;
  min?: number;
  max?: number;
  step?: number;
  rows?: number;
  
  // Custom rendering
  render?: (field: any, form: UseFormReturn<T>) => React.ReactNode;
  
  // Layout
  className?: string;
  fullWidth?: boolean;
  span?: number; // Grid span (1-12)
}

export interface BaseFormSection<T extends FieldValues = FieldValues> {
  title?: string;
  description?: string;
  fields: BaseFormField<T>[];
  className?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export interface BaseFormProps<T extends FieldValues = FieldValues> {
  // Form configuration
  title?: string;
  description?: string;
  schema?: z.ZodSchema<T>;
  defaultValues?: Partial<T>;
  
  // Layout
  sections?: BaseFormSection<T>[];
  fields?: BaseFormField<T>[]; // Alternative to sections for simple forms
  layout?: 'single' | 'two-column' | 'grid';
  
  // Behavior
  isLoading?: boolean;
  isSubmitting?: boolean;
  submitText?: string;
  cancelText?: string;
  showCancel?: boolean;
  showReset?: boolean;
  
  // Event handlers
  onSubmit: (data: T) => void | Promise<void>;
  onCancel?: () => void;
  onReset?: () => void;
  onChange?: (data: Partial<T>) => void;
  
  // Styling
  className?: string;
  formClassName?: string;
  cardWrapper?: boolean;
  
  // Error handling
  error?: string;
  fieldErrors?: Record<string, string>;
  
  // Custom components
  CustomActions?: React.ComponentType<{ 
    form: UseFormReturn<T>; 
    isSubmitting: boolean; 
    onCancel?: () => void; 
  }>;
}

export function BaseForm<T extends FieldValues = FieldValues>({
  title,
  description,
  schema,
  defaultValues,
  sections,
  fields,
  layout = 'single',
  isLoading = false,
  isSubmitting = false,
  submitText = 'Submit',
  cancelText = 'Cancel',
  showCancel = false,
  showReset = false,
  onSubmit,
  onCancel,
  onReset,
  onChange,
  className,
  formClassName,
  cardWrapper = false,
  error,
  fieldErrors,
  CustomActions,
}: BaseFormProps<T>) {
  const [isExpanded, setIsExpanded] = useState<Record<string, boolean>>({});

  // Initialize form
  const form = useForm<T>({
    resolver: schema ? zodResolver(schema) : undefined,
    defaultValues: defaultValues as any,
  });

  // Watch for changes
  React.useEffect(() => {
    if (onChange) {
      const subscription = form.watch((data) => {
        onChange(data as Partial<T>);
      });
      return () => subscription.unsubscribe();
    }
  }, [form, onChange]);

  // Handle form submission
  const handleSubmit = async (data: T) => {
    try {
      await onSubmit(data);
    } catch (err) {
      console.error('Form submission error:', err);
    }
  };

  // Render individual field
  const renderField = (field: BaseFormField<T>) => {
    return (
      <FormField
        key={field.name}
        control={form.control}
        name={field.name}
        render={({ field: formField }) => (
          <FormItem className={cn(
            field.fullWidth && 'col-span-full',
            field.span && `col-span-${field.span}`,
            field.className
          )}>
            <FormLabel className={field.required ? 'required' : ''}>
              {field.label}
            </FormLabel>
            <FormControl>
              {field.render ? (
                field.render(formField, form)
              ) : (
                renderFieldControl(field, formField)
              )}
            </FormControl>
            {field.description && (
              <FormDescription>{field.description}</FormDescription>
            )}
            <FormMessage />
            {fieldErrors?.[field.name] && (
              <p className="text-sm text-destructive">{fieldErrors[field.name]}</p>
            )}
          </FormItem>
        )}
      />
    );
  };

  // Render field control based on type
  const renderFieldControl = (field: BaseFormField<T>, formField: any) => {
    switch (field.type) {
      case 'text':
      case 'email':
      case 'password':
        return (
          <Input
            type={field.type}
            placeholder={field.placeholder}
            disabled={field.disabled}
            {...formField}
          />
        );

      case 'number':
      case 'currency':
        return (
          <Input
            type="number"
            placeholder={field.placeholder}
            disabled={field.disabled}
            min={field.min}
            max={field.max}
            step={field.step}
            {...formField}
          />
        );

      case 'textarea':
        return (
          <Textarea
            placeholder={field.placeholder}
            disabled={field.disabled}
            rows={field.rows || 3}
            {...formField}
          />
        );

      case 'select':
        return (
          <Select
            onValueChange={formField.onChange}
            defaultValue={formField.value}
            disabled={field.disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder={field.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={formField.value}
              onCheckedChange={formField.onChange}
              disabled={field.disabled}
            />
            <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {field.placeholder}
            </label>
          </div>
        );

      case 'switch':
        return (
          <div className="flex items-center space-x-2">
            <Switch
              checked={formField.value}
              onCheckedChange={formField.onChange}
              disabled={field.disabled}
            />
            <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {field.placeholder}
            </label>
          </div>
        );

      case 'date':
        return (
          <Input
            type="date"
            disabled={field.disabled}
            {...formField}
          />
        );

      default:
        return (
          <Input
            placeholder={field.placeholder}
            disabled={field.disabled}
            {...formField}
          />
        );
    }
  };

  // Render form sections
  const renderSections = () => {
    const formSections = sections || [{ fields: fields || [] }];
    
    return formSections.map((section, index) => (
      <div key={index} className={cn('space-y-4', section.className)}>
        {section.title && (
          <div className="space-y-2">
            <h3 className="text-lg font-medium">{section.title}</h3>
            {section.description && (
              <p className="text-sm text-muted-foreground">{section.description}</p>
            )}
          </div>
        )}
        
        <div className={cn(
          'space-y-4',
          layout === 'two-column' && 'grid grid-cols-1 md:grid-cols-2 gap-4',
          layout === 'grid' && 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
        )}>
          {section.fields.map(renderField)}
        </div>
      </div>
    ));
  };

  // Render form actions
  const renderActions = () => {
    if (CustomActions) {
      return <CustomActions form={form} isSubmitting={isSubmitting} onCancel={onCancel} />;
    }

    return (
      <div className="flex items-center justify-end space-x-2">
        {showReset && (
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              form.reset();
              onReset?.();
            }}
            disabled={isSubmitting}
          >
            Reset
          </Button>
        )}
        
        {showCancel && onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {cancelText}
          </Button>
        )}
        
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting && <LoadingSpinner size="sm" className="mr-2" />}
          {submitText}
        </Button>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const formContent = (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className={cn('space-y-6', formClassName)}>
        {/* Form header */}
        {(title || description) && (
          <div className="space-y-2">
            {title && <h2 className="text-xl font-semibold">{title}</h2>}
            {description && <p className="text-muted-foreground">{description}</p>}
          </div>
        )}

        {/* Global error */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Form sections */}
        {renderSections()}

        {/* Form actions */}
        {renderActions()}
      </form>
    </Form>
  );

  if (cardWrapper) {
    return (
      <Card className={className}>
        {title && (
          <CardHeader>
            <CardTitle>{title}</CardTitle>
            {description && <p className="text-sm text-muted-foreground">{description}</p>}
          </CardHeader>
        )}
        <CardContent>
          {formContent}
        </CardContent>
      </Card>
    );
  }

  return <div className={className}>{formContent}</div>;
}

// Utility function to create form schemas
export const createFormSchema = <T extends Record<string, any>>(fields: BaseFormField<T>[]) => {
  const schemaFields: Record<string, z.ZodTypeAny> = {};
  
  fields.forEach((field) => {
    let fieldSchema: z.ZodTypeAny;
    
    switch (field.type) {
      case 'email':
        fieldSchema = z.string().email('Invalid email address');
        break;
      case 'number':
      case 'currency':
        fieldSchema = z.number();
        if (field.min !== undefined) fieldSchema = fieldSchema.min(field.min);
        if (field.max !== undefined) fieldSchema = fieldSchema.max(field.max);
        break;
      case 'checkbox':
      case 'switch':
        fieldSchema = z.boolean();
        break;
      default:
        fieldSchema = z.string();
        if (field.min !== undefined) fieldSchema = fieldSchema.min(field.min);
        if (field.max !== undefined) fieldSchema = fieldSchema.max(field.max);
    }
    
    if (!field.required) {
      fieldSchema = fieldSchema.optional();
    }
    
    schemaFields[field.name as string] = fieldSchema;
  });
  
  return z.object(schemaFields);
};
