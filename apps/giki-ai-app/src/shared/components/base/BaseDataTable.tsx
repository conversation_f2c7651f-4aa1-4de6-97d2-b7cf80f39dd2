/**
 * Base Data Table Component - Unified table patterns
 * ==================================================
 * 
 * This component consolidates duplicate table patterns across the application:
 * - TransactionTable patterns
 * - CategoryTable patterns  
 * - ExcelTable patterns
 * - Various data grid implementations
 * 
 * Key features:
 * - Consistent sorting, filtering, and pagination
 * - Standardized selection and bulk actions
 * - Responsive design with mobile-friendly layouts
 * - Type-safe column definitions
 * - Consistent styling and behavior
 */

import React, { useState, useMemo, useCallback } from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  Table as ReactTable,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import { cn } from '@/shared/utils/utils';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { Badge } from '@/shared/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu';
import { LoadingSpinner } from '@/shared/components/ui/standardized-loading';

// Base interfaces for type safety
export interface BaseTableColumn<T = any> {
  key: keyof T;
  label: string;
  type?: 'text' | 'number' | 'currency' | 'date' | 'status' | 'percentage' | 'badge' | 'custom';
  width?: string;
  sortable?: boolean;
  filterable?: boolean;
  hideable?: boolean;
  formatValue?: (value: any, row: T) => React.ReactNode;
  aggregatable?: boolean;
  className?: string;
}

export interface BaseTableProps<T = any> {
  data: T[];
  columns: BaseTableColumn<T>[];
  
  // Table configuration
  title?: string;
  description?: string;
  isLoading?: boolean;
  pageSize?: number;
  
  // Feature toggles
  enableSorting?: boolean;
  enableFiltering?: boolean;
  enableSelection?: boolean;
  enableColumnVisibility?: boolean;
  enablePagination?: boolean;
  enableExport?: boolean;
  enableRefresh?: boolean;
  showAggregations?: boolean;
  
  // Event handlers
  onRefresh?: () => void;
  onExport?: (format: 'csv' | 'excel' | 'pdf') => void;
  onSelectionChange?: (selectedRows: T[]) => void;
  onRowClick?: (row: T) => void;
  onBulkAction?: (action: string, selectedRows: T[]) => void;
  
  // Styling
  className?: string;
  tableClassName?: string;
  headerClassName?: string;
  rowClassName?: string | ((row: T) => string);
  
  // Custom components
  EmptyState?: React.ComponentType;
  LoadingState?: React.ComponentType;
  ToolbarActions?: React.ComponentType<{ selectedRows: T[] }>;
}

export function BaseDataTable<T extends Record<string, any>>({
  data,
  columns,
  title,
  description,
  isLoading = false,
  pageSize = 20,
  
  // Feature toggles
  enableSorting = true,
  enableFiltering = true,
  enableSelection = false,
  enableColumnVisibility = true,
  enablePagination = true,
  enableExport = false,
  enableRefresh = false,
  showAggregations = false,
  
  // Event handlers
  onRefresh,
  onExport,
  onSelectionChange,
  onRowClick,
  onBulkAction,
  
  // Styling
  className,
  tableClassName,
  headerClassName,
  rowClassName,
  
  // Custom components
  EmptyState,
  LoadingState,
  ToolbarActions,
}: BaseTableProps<T>) {
  // Table state
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [globalFilter, setGlobalFilter] = useState('');

  // Format cell values based on column type
  const formatCellValue = useCallback((value: any, column: BaseTableColumn<T>, row: T) => {
    if (column.formatValue) {
      return column.formatValue(value, row);
    }

    switch (column.type) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        }).format(Number(value) || 0);

      case 'number':
        return new Intl.NumberFormat('en-US').format(Number(value) || 0);

      case 'percentage':
        return `${((Number(value) || 0) * 100).toFixed(1)}%`;

      case 'date':
        return value ? new Date(value).toLocaleDateString() : '';

      case 'status':
        return (
          <Badge variant={value === 'active' ? 'default' : 'secondary'}>
            {String(value)}
          </Badge>
        );

      case 'badge':
        return <Badge variant="outline">{String(value)}</Badge>;

      default:
        return String(value || '');
    }
  }, []);

  // Build React Table columns
  const tableColumns: ColumnDef<T>[] = useMemo(() => {
    const cols: ColumnDef<T>[] = [];

    // Selection column
    if (enableSelection) {
      cols.push({
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && 'indeterminate')
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      });
    }

    // Data columns
    columns.forEach((column) => {
      cols.push({
        accessorKey: column.key as string,
        header: column.label,
        cell: ({ row }) => (
          <div className={cn('truncate', column.className)}>
            {formatCellValue(row.getValue(column.key as string), column, row.original)}
          </div>
        ),
        enableSorting: enableSorting && column.sortable !== false,
        enableHiding: enableColumnVisibility && column.hideable !== false,
        size: column.width ? parseInt(column.width) : undefined,
      });
    });

    return cols;
  }, [columns, enableSelection, enableSorting, enableColumnVisibility, formatCellValue]);

  // React Table instance
  const table: ReactTable<T> = useReactTable({
    data,
    columns: tableColumns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: enablePagination ? getPaginationRowModel() : undefined,
    getSortedRowModel: enableSorting ? getSortedRowModel() : undefined,
    getFilteredRowModel: enableFiltering ? getFilteredRowModel() : undefined,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
    initialState: {
      pagination: {
        pageSize,
      },
    },
  });

  // Get selected rows
  const selectedRows = useMemo(() => {
    return table.getFilteredSelectedRowModel().rows.map(row => row.original);
  }, [table, rowSelection]);

  // Notify parent of selection changes
  React.useEffect(() => {
    if (onSelectionChange) {
      onSelectionChange(selectedRows);
    }
  }, [selectedRows, onSelectionChange]);

  // Calculate aggregations
  const aggregations = useMemo(() => {
    if (!showAggregations) return {};
    
    const aggs: Record<string, any> = {};
    columns.forEach((column) => {
      if (column.aggregatable && ['number', 'currency'].includes(column.type || '')) {
        const values = data
          .map(row => Number(row[column.key]))
          .filter(v => !isNaN(v));
        
        if (values.length > 0) {
          aggs[column.key as string] = {
            sum: values.reduce((acc, val) => acc + val, 0),
            avg: values.reduce((acc, val) => acc + val, 0) / values.length,
            min: Math.min(...values),
            max: Math.max(...values),
            count: values.length,
          };
        }
      }
    });
    
    return aggs;
  }, [data, columns, showAggregations]);

  if (isLoading && LoadingState) {
    return <LoadingState />;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (data.length === 0 && EmptyState) {
    return <EmptyState />;
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header */}
      {(title || description || enableRefresh || enableExport || enableFiltering) && (
        <div className="flex flex-col gap-4">
          {/* Title and actions */}
          <div className="flex items-center justify-between">
            <div>
              {title && <h3 className="text-lg font-semibold">{title}</h3>}
              {description && <p className="text-sm text-muted-foreground">{description}</p>}
            </div>
            
            <div className="flex items-center gap-2">
              {enableRefresh && onRefresh && (
                <Button variant="outline" size="sm" onClick={onRefresh}>
                  ⟲ Refresh
                </Button>
              )}
              
              {enableColumnVisibility && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      ⌄ Columns
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {table
                      .getAllColumns()
                      .filter((column) => column.getCanHide())
                      .map((column) => {
                        return (
                          <DropdownMenuCheckboxItem
                            key={column.id}
                            className="capitalize"
                            checked={column.getIsVisible()}
                            onCheckedChange={(value) => column.toggleVisibility(!!value)}
                          >
                            {column.id}
                          </DropdownMenuCheckboxItem>
                        );
                      })}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
              
              {enableExport && onExport && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      ↓ Export
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuCheckboxItem onClick={() => onExport('csv')}>
                      CSV
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem onClick={() => onExport('excel')}>
                      Excel
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem onClick={() => onExport('pdf')}>
                      PDF
                    </DropdownMenuCheckboxItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>

          {/* Global search */}
          {enableFiltering && (
            <div className="flex items-center gap-4">
              <Input
                placeholder="Search all columns..."
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="max-w-sm"
              />
              
              {selectedRows.length > 0 && ToolbarActions && (
                <ToolbarActions selectedRows={selectedRows} />
              )}
            </div>
          )}
        </div>
      )}

      {/* Table */}
      <div className="rounded-md border">
        <Table className={tableClassName}>
          <TableHeader className={headerClassName}>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className={cn(
                    onRowClick && 'cursor-pointer hover:bg-muted/50',
                    typeof rowClassName === 'function' 
                      ? rowClassName(row.original)
                      : rowClassName
                  )}
                  onClick={() => onRowClick?.(row.original)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={tableColumns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Footer with pagination and aggregations */}
      {(enablePagination || showAggregations) && (
        <div className="flex items-center justify-between">
          <div className="flex-1 text-sm text-muted-foreground">
            {enableSelection && selectedRows.length > 0 && (
              <span>{selectedRows.length} of {table.getFilteredRowModel().rows.length} row(s) selected.</span>
            )}
          </div>
          
          {enablePagination && (
            <div className="flex items-center space-x-6 lg:space-x-8">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium">Rows per page</p>
                <select
                  value={table.getState().pagination.pageSize}
                  onChange={(e) => table.setPageSize(Number(e.target.value))}
                  className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm"
                >
                  {[10, 20, 30, 40, 50].map((pageSize) => (
                    <option key={pageSize} value={pageSize}>
                      {pageSize}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                Page {table.getState().pagination.pageIndex + 1} of{' '}
                {table.getPageCount()}
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  className="h-8 w-8 p-0"
                  onClick={() => table.setPageIndex(0)}
                  disabled={!table.getCanPreviousPage()}
                >
                  ⟪
                </Button>
                <Button
                  variant="outline"
                  className="h-8 w-8 p-0"
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                >
                  ⟨
                </Button>
                <Button
                  variant="outline"
                  className="h-8 w-8 p-0"
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                >
                  ⟩
                </Button>
                <Button
                  variant="outline"
                  className="h-8 w-8 p-0"
                  onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                  disabled={!table.getCanNextPage()}
                >
                  ⟫
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
