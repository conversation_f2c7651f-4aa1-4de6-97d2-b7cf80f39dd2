/**
 * Base Data Page Component - Specialized for data-driven pages
 * ===========================================================
 * 
 * This component extends BasePage with specialized functionality for pages
 * that display data tables, charts, and analytics. Common in reports,
 * transactions, intelligence, and dashboard pages.
 * 
 * Key features:
 * - Built-in data fetching patterns
 * - Filtering and search capabilities
 * - Export functionality
 * - Data refresh and real-time updates
 * - Pagination support
 * - Column management for tables
 * - Chart/visualization containers
 */

import React, { ReactNode, useState, useEffect, useCallback } from 'react';
import { Search, Download } from 'lucide-react';
import { BasePage, BasePageProps } from './BasePage';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Badge } from '@/shared/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/shared/components/ui/dropdown-menu';
import { cn } from '@/shared/utils/utils';

export interface FilterOption {
  key: string;
  label: string;
  value: string;
  count?: number;
}

export interface DataFilter {
  key: string;
  label: string;
  type: 'select' | 'multiselect' | 'daterange' | 'search' | 'number';
  options?: FilterOption[];
  value?: any;
  placeholder?: string;
}

export interface ExportOption {
  format: 'csv' | 'excel' | 'pdf' | 'json';
  label: string;
  description?: string;
  onExport: () => Promise<void> | void;
}

export interface DataMetric {
  label: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  trend?: 'up' | 'down' | 'neutral';
  format?: 'number' | 'currency' | 'percentage';
}

export interface BaseDataPageProps extends Omit<BasePageProps, 'children'> {
  // Data management
  data?: any[];
  totalCount?: number;
  isLoading?: boolean;
  onRefresh?: () => void;
  
  // Filtering and search
  filters?: DataFilter[];
  onFiltersChange?: (filters: Record<string, any>) => void;
  searchPlaceholder?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  
  // Export functionality
  exportOptions?: ExportOption[];
  
  // Metrics/summary
  metrics?: DataMetric[];
  
  // Pagination
  currentPage?: number;
  totalPages?: number;
  pageSize?: number;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (size: number) => void;
  
  // Layout options
  showFilters?: boolean;
  showSearch?: boolean;
  showExport?: boolean;
  showMetrics?: boolean;
  showPagination?: boolean;
  
  // Content
  children: ReactNode;
  
  // Custom components
  CustomFilters?: React.ComponentType<{ filters: DataFilter[]; onChange: (filters: Record<string, any>) => void }>;
  CustomMetrics?: React.ComponentType<{ metrics: DataMetric[] }>;
}

export function BaseDataPage({
  data = [],
  totalCount,
  isLoading = false,
  onRefresh,
  filters = [],
  onFiltersChange,
  searchPlaceholder = 'Search...',
  searchValue = '',
  onSearchChange,
  exportOptions = [],
  metrics = [],
  currentPage = 1,
  totalPages = 1,
  pageSize = 50,
  onPageChange,
  onPageSizeChange,
  showFilters = true,
  showSearch = true,
  showExport = true,
  showMetrics = true,
  showPagination = true,
  children,
  CustomFilters,
  CustomMetrics,
  ...basePageProps
}: BaseDataPageProps) {
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({});
  const [isExporting, setIsExporting] = useState<string | null>(null);

  // Initialize filters
  useEffect(() => {
    const initialFilters: Record<string, any> = {};
    filters.forEach(filter => {
      if (filter.value !== undefined) {
        initialFilters[filter.key] = filter.value;
      }
    });
    setActiveFilters(initialFilters);
  }, [filters]);

  // Handle filter changes
  const handleFilterChange = useCallback((key: string, value: any) => {
    const newFilters = { ...activeFilters, [key]: value };
    setActiveFilters(newFilters);
    onFiltersChange?.(newFilters);
  }, [activeFilters, onFiltersChange]);

  // Handle export
  const handleExport = async (option: ExportOption) => {
    setIsExporting(option.format);
    try {
      await option.onExport();
    } finally {
      setIsExporting(null);
    }
  };

  // Format metric values
  const formatMetricValue = (value: string | number, format?: string) => {
    if (typeof value === 'string') return value;
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
        }).format(value);
      case 'percentage':
        return `${(value * 100).toFixed(1)}%`;
      case 'number':
        return new Intl.NumberFormat('en-US').format(value);
      default:
        return value.toString();
    }
  };

  // Get trend indicator
  const getTrendIndicator = (trend?: 'up' | 'down' | 'neutral', change?: number) => {
    if (!trend || change === undefined) return null;
    
    const isPositive = change > 0;
    return (
      <span className={cn(
        'text-xs font-medium ml-2',
        trend === 'up' && isPositive && 'text-green-600',
        trend === 'up' && !isPositive && 'text-red-600',
        trend === 'down' && isPositive && 'text-red-600',
        trend === 'down' && !isPositive && 'text-green-600',
        trend === 'neutral' && 'text-gray-600'
      )}>
        {trend === 'up' && (isPositive ? '↗' : '↘')}
        {trend === 'down' && (isPositive ? '↘' : '↗')}
        {trend === 'neutral' && '→'}
        {Math.abs(change).toFixed(1)}%
      </span>
    );
  };

  // Render metrics
  const renderMetrics = () => {
    if (!showMetrics || metrics.length === 0) return null;

    if (CustomMetrics) {
      return <CustomMetrics metrics={metrics} />;
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">{metric.label}</p>
                <div className="flex items-baseline">
                  <p className="text-2xl font-bold">
                    {formatMetricValue(metric.value, metric.format)}
                  </p>
                  {getTrendIndicator(metric.trend, metric.change)}
                </div>
                {metric.changeLabel && (
                  <p className="text-xs text-muted-foreground">{metric.changeLabel}</p>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  // Render filters and search
  const renderFiltersAndSearch = () => {
    if (!showFilters && !showSearch && !showExport) return null;

    return (
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            {showSearch && (
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={searchPlaceholder}
                    value={searchValue}
                    onChange={(e) => onSearchChange?.(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            )}

            {/* Filters */}
            {showFilters && filters.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {CustomFilters ? (
                  <CustomFilters filters={filters} onChange={handleFilterChange} />
                ) : (
                  filters.map((filter) => (
                    <Select
                      key={filter.key}
                      value={activeFilters[filter.key] || ''}
                      onValueChange={(value) => handleFilterChange(filter.key, value)}
                    >
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder={filter.placeholder || filter.label} />
                      </SelectTrigger>
                      <SelectContent>
                        {filter.options?.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                            {option.count && (
                              <Badge variant="secondary" className="ml-2">
                                {option.count}
                              </Badge>
                            )}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ))
                )}
              </div>
            )}

            {/* Export */}
            {showExport && exportOptions.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {exportOptions.map((option) => (
                    <DropdownMenuItem
                      key={option.format}
                      onClick={() => handleExport(option)}
                      disabled={isExporting === option.format}
                    >
                      {isExporting === option.format ? (
                        <span className="animate-spin mr-2">⟳</span>
                      ) : null}
                      {option.label}
                      {option.description && (
                        <span className="text-xs text-muted-foreground ml-2">
                          {option.description}
                        </span>
                      )}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  // Render pagination
  const renderPagination = () => {
    if (!showPagination || totalPages <= 1) return null;

    return (
      <div className="flex items-center justify-between mt-6">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount || 0)} of {totalCount || 0} results
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            Previous
          </Button>
          
          <span className="text-sm">
            Page {currentPage} of {totalPages}
          </span>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(currentPage + 1)}
            disabled={currentPage >= totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    );
  };

  // Enhanced page props with data-specific actions
  const enhancedPageProps: BasePageProps = {
    ...basePageProps,
    loading: isLoading,
    onRefresh,
    children: (
      <>
        {renderMetrics()}
        {renderFiltersAndSearch()}
        {children}
        {renderPagination()}
      </>
    ),
  };

  return <BasePage {...enhancedPageProps} />;
}
