/**
 * Base Form Page Component - Specialized for form-driven pages
 * ===========================================================
 * 
 * This component extends BasePage with specialized functionality for pages
 * that contain forms, wizards, and multi-step processes. Common in auth,
 * onboarding, settings, and configuration pages.
 * 
 * Key features:
 * - Form validation and error handling
 * - Multi-step wizard support
 * - Auto-save and draft functionality
 * - Form progress tracking
 * - Consistent form layouts and styling
 * - Accessibility features for forms
 */

import React, { ReactNode, useState, useEffect, useCallback } from 'react';
import { Save, X, ArrowLeft, ArrowRight, AlertCircle, CheckCircle } from 'lucide-react';
import { BasePage, BasePageProps } from './BasePage';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Progress } from '@/shared/components/ui/progress';
import { Badge } from '@/shared/components/ui/badge';
import { cn } from '@/shared/utils/utils';

export interface FormStep {
  id: string;
  title: string;
  description?: string;
  isComplete?: boolean;
  isOptional?: boolean;
  component: ReactNode;
}

export interface FormValidationError {
  field: string;
  message: string;
}

export interface BaseFormPageProps extends Omit<BasePageProps, 'children'> {
  // Form management
  isSubmitting?: boolean;
  onSubmit?: () => Promise<void> | void;
  onCancel?: () => void;
  onSave?: () => Promise<void> | void; // For auto-save/draft
  
  // Validation
  errors?: FormValidationError[];
  isValid?: boolean;
  
  // Multi-step support
  steps?: FormStep[];
  currentStep?: number;
  onStepChange?: (step: number) => void;
  
  // Auto-save
  autoSave?: boolean;
  autoSaveInterval?: number; // in seconds
  lastSaved?: Date;
  isDirty?: boolean;
  
  // Form layout
  formLayout?: 'single' | 'split' | 'wizard';
  showProgress?: boolean;
  showStepNavigation?: boolean;
  
  // Actions
  submitLabel?: string;
  cancelLabel?: string;
  saveLabel?: string;
  nextLabel?: string;
  previousLabel?: string;
  
  // Custom components
  CustomStepIndicator?: React.ComponentType<{
    steps: FormStep[];
    currentStep: number;
    onStepClick: (step: number) => void;
  }>;
  
  // Content
  children?: ReactNode;
}

export function BaseFormPage({
  isSubmitting = false,
  onSubmit,
  onCancel,
  onSave,
  errors = [],
  isValid = true,
  steps = [],
  currentStep = 0,
  onStepChange,
  autoSave = false,
  autoSaveInterval = 30,
  lastSaved,
  isDirty = false,
  formLayout = 'single',
  showProgress = true,
  showStepNavigation = true,
  submitLabel = 'Submit',
  cancelLabel = 'Cancel',
  saveLabel = 'Save Draft',
  nextLabel = 'Next',
  previousLabel = 'Previous',
  CustomStepIndicator,
  children,
  ...basePageProps
}: BaseFormPageProps) {
  const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  const isMultiStep = steps.length > 0;
  const currentStepData = isMultiStep ? steps[currentStep] : null;
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === steps.length - 1;

  // Auto-save functionality
  useEffect(() => {
    if (!autoSave || !onSave || !isDirty) return;

    const interval = setInterval(async () => {
      setAutoSaveStatus('saving');
      try {
        await onSave();
        setAutoSaveStatus('saved');
        setTimeout(() => setAutoSaveStatus('idle'), 2000);
      } catch {
        setAutoSaveStatus('error');
        setTimeout(() => setAutoSaveStatus('idle'), 3000);
      }
    }, autoSaveInterval * 1000);

    return () => clearInterval(interval);
  }, [autoSave, onSave, isDirty, autoSaveInterval]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!onSubmit || isSubmitting) return;
    
    try {
      await onSubmit();
    } catch (error) {
      console.error('Form submission error:', error);
    }
  }, [onSubmit, isSubmitting]);

  // Handle step navigation
  const handleStepChange = useCallback((step: number) => {
    if (step < 0 || step >= steps.length) return;
    onStepChange?.(step);
  }, [steps.length, onStepChange]);

  // Calculate progress percentage
  const getProgressPercentage = () => {
    if (!isMultiStep) return 100;
    return ((currentStep + 1) / steps.length) * 100;
  };

  // Render step indicator
  const renderStepIndicator = () => {
    if (!isMultiStep || !showStepNavigation) return null;

    if (CustomStepIndicator) {
      return (
        <CustomStepIndicator
          steps={steps}
          currentStep={currentStep}
          onStepClick={handleStepChange}
        />
      );
    }

    return (
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={cn(
                'flex items-center cursor-pointer',
                index < steps.length - 1 && 'flex-1'
              )}
              onClick={() => handleStepChange(index)}
            >
              <div className="flex items-center">
                <div
                  className={cn(
                    'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                    index === currentStep && 'bg-primary text-primary-foreground',
                    index < currentStep && 'bg-green-500 text-white',
                    index > currentStep && 'bg-muted text-muted-foreground'
                  )}
                >
                  {index < currentStep ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    index + 1
                  )}
                </div>
                <div className="ml-3">
                  <p className={cn(
                    'text-sm font-medium',
                    index === currentStep && 'text-foreground',
                    index !== currentStep && 'text-muted-foreground'
                  )}>
                    {step.title}
                  </p>
                  {step.description && (
                    <p className="text-xs text-muted-foreground">
                      {step.description}
                    </p>
                  )}
                </div>
              </div>
              {index < steps.length - 1 && (
                <div className="flex-1 h-px bg-border mx-4" />
              )}
            </div>
          ))}
        </div>

        {showProgress && (
          <Progress value={getProgressPercentage()} className="h-2" />
        )}
      </div>
    );
  };

  // Render form errors
  const renderErrors = () => {
    if (errors.length === 0) return null;

    return (
      <Alert variant="destructive" className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-1">
            <p className="font-medium">Please fix the following errors:</p>
            <ul className="list-disc list-inside space-y-1">
              {errors.map((error, index) => (
                <li key={index} className="text-sm">
                  {error.message}
                </li>
              ))}
            </ul>
          </div>
        </AlertDescription>
      </Alert>
    );
  };

  // Render auto-save status
  const renderAutoSaveStatus = () => {
    if (!autoSave) return null;

    return (
      <div className="flex items-center gap-2 text-xs text-muted-foreground">
        {autoSaveStatus === 'saving' && (
          <>
            <div className="animate-spin h-3 w-3 border border-current border-t-transparent rounded-full" />
            Saving...
          </>
        )}
        {autoSaveStatus === 'saved' && (
          <>
            <CheckCircle className="h-3 w-3 text-green-500" />
            Saved
          </>
        )}
        {autoSaveStatus === 'error' && (
          <>
            <AlertCircle className="h-3 w-3 text-red-500" />
            Save failed
          </>
        )}
        {autoSaveStatus === 'idle' && lastSaved && (
          <>
            Last saved: {lastSaved.toLocaleTimeString()}
          </>
        )}
      </div>
    );
  };

  // Render form actions
  const renderFormActions = () => {
    return (
      <div className="flex items-center justify-between pt-6 border-t">
        <div className="flex items-center gap-4">
          {renderAutoSaveStatus()}
          {isDirty && (
            <Badge variant="outline" className="text-xs">
              Unsaved changes
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2">
          {onCancel && (
            <Button variant="outline" onClick={onCancel}>
              <X className="h-4 w-4 mr-2" />
              {cancelLabel}
            </Button>
          )}

          {onSave && (
            <Button variant="outline" onClick={onSave}>
              <Save className="h-4 w-4 mr-2" />
              {saveLabel}
            </Button>
          )}

          {isMultiStep && !isFirstStep && (
            <Button
              variant="outline"
              onClick={() => handleStepChange(currentStep - 1)}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {previousLabel}
            </Button>
          )}

          {isMultiStep && !isLastStep ? (
            <Button
              onClick={() => handleStepChange(currentStep + 1)}
              disabled={!isValid}
            >
              {nextLabel}
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={!isValid || isSubmitting}
            >
              {isSubmitting && (
                <div className="animate-spin h-4 w-4 border border-current border-t-transparent rounded-full mr-2" />
              )}
              {submitLabel}
            </Button>
          )}
        </div>
      </div>
    );
  };

  // Render form content
  const renderFormContent = () => {
    if (isMultiStep && currentStepData) {
      return (
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-2">{currentStepData.title}</h2>
            {currentStepData.description && (
              <p className="text-muted-foreground">{currentStepData.description}</p>
            )}
          </div>
          {currentStepData.component}
        </div>
      );
    }

    return children;
  };

  // Enhanced page props with form-specific features
  const enhancedPageProps: BasePageProps = {
    ...basePageProps,
    children: (
      <Card>
        <CardContent className="p-6">
          {renderStepIndicator()}
          {renderErrors()}
          {renderFormContent()}
          {renderFormActions()}
        </CardContent>
      </Card>
    ),
  };

  return <BasePage {...enhancedPageProps} />;
}
