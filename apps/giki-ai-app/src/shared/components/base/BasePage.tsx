/**
 * Base Page Component - Unified page patterns
 * ==========================================
 * 
 * This component consolidates duplicate page patterns across the application:
 * - Consistent page headers with titles, descriptions, and actions
 * - Standardized loading states and error handling
 * - Common layout structures and spacing
 * - Unified navigation patterns (breadcrumbs, back buttons)
 * - Consistent data fetching and state management patterns
 * 
 * Key features:
 * - Flexible page header with customizable actions
 * - Built-in loading and error states
 * - Responsive layout with consistent spacing
 * - Breadcrumb navigation support
 * - SEO-friendly page structure
 * - Accessibility features
 */

import React, { ReactNode, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, RefreshCw } from 'lucide-react';
import { cn } from '@/shared/utils/utils';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Loading } from '@/shared/components/ui/loading';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/shared/components/ui/breadcrumb';

export interface PageAction {
  label: string;
  icon?: ReactNode;
  onClick: () => void;
  variant?: 'default' | 'outline' | 'ghost' | 'destructive';
  disabled?: boolean;
  loading?: boolean;
}

export interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

export interface BasePageProps {
  // Page metadata
  title: string;
  description?: string;
  subtitle?: string;
  
  // Navigation
  showBackButton?: boolean;
  backButtonLabel?: string;
  onBack?: () => void;
  breadcrumbs?: BreadcrumbItem[];
  
  // Actions
  primaryAction?: PageAction;
  secondaryActions?: PageAction[];
  
  // States
  loading?: boolean;
  error?: string | Error;
  isEmpty?: boolean;
  emptyStateMessage?: string;
  emptyStateAction?: PageAction;
  
  // Layout
  children: ReactNode;
  className?: string;
  contentClassName?: string;
  headerClassName?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  
  // Data management
  onRefresh?: () => void;
  refreshing?: boolean;
  lastUpdated?: Date;
  
  // SEO and accessibility
  pageTitle?: string; // For document title
  metaDescription?: string;
}

export function BasePage({
  title,
  description,
  subtitle,
  showBackButton = false,
  backButtonLabel = 'Back',
  onBack,
  breadcrumbs,
  primaryAction,
  secondaryActions,
  loading = false,
  error,
  isEmpty = false,
  emptyStateMessage = 'No data available',
  emptyStateAction,
  children,
  className,
  contentClassName,
  headerClassName,
  maxWidth = 'full',
  padding = 'md',
  onRefresh,
  refreshing = false,
  lastUpdated,
  pageTitle,
  metaDescription,
}: BasePageProps) {
  const navigate = useNavigate();

  // Set document title
  useEffect(() => {
    if (pageTitle) {
      document.title = pageTitle;
    } else if (title) {
      document.title = `${title} | Giki AI`;
    }
  }, [pageTitle, title]);

  // Set meta description
  useEffect(() => {
    if (metaDescription) {
      const metaTag = document.querySelector('meta[name="description"]');
      if (metaTag) {
        metaTag.setAttribute('content', metaDescription);
      }
    }
  }, [metaDescription]);

  // Handle back navigation
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate(-1);
    }
  };

  // Get max width classes
  const getMaxWidthClass = () => {
    switch (maxWidth) {
      case 'sm': return 'max-w-2xl';
      case 'md': return 'max-w-4xl';
      case 'lg': return 'max-w-6xl';
      case 'xl': return 'max-w-7xl';
      case '2xl': return 'max-w-screen-2xl';
      case 'full': return 'max-w-full';
      default: return 'max-w-full';
    }
  };

  // Get padding classes
  const getPaddingClass = () => {
    switch (padding) {
      case 'none': return '';
      case 'sm': return 'p-4';
      case 'md': return 'p-6';
      case 'lg': return 'p-8';
      default: return 'p-6';
    }
  };

  // Render breadcrumbs
  const renderBreadcrumbs = () => {
    if (!breadcrumbs || breadcrumbs.length === 0) return null;

    return (
      <Breadcrumb className="mb-4">
        <BreadcrumbList>
          {breadcrumbs.map((item, index) => (
            <React.Fragment key={index}>
              <BreadcrumbItem>
                {item.current ? (
                  <BreadcrumbPage>{item.label}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink href={item.href || '#'}>
                    {item.label}
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
            </React.Fragment>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    );
  };

  // Render page header
  const renderHeader = () => (
    <div className={cn('space-y-4', headerClassName)}>
      {renderBreadcrumbs()}
      
      <div className="flex items-start justify-between gap-4">
        <div className="flex-1 min-w-0">
          {showBackButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="mb-2 -ml-2"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {backButtonLabel}
            </Button>
          )}
          
          <div className="space-y-1">
            <h1 className="text-3xl font-semibold text-foreground tracking-tight">
              {title}
            </h1>
            {subtitle && (
              <p className="text-lg text-muted-foreground">{subtitle}</p>
            )}
            {description && (
              <p className="text-sm text-muted-foreground max-w-2xl">
                {description}
              </p>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2 flex-shrink-0">
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={cn('h-4 w-4', refreshing && 'animate-spin')} />
            </Button>
          )}
          
          {secondaryActions?.map((action, index) => (
            <Button
              key={index}
              variant={action.variant || 'outline'}
              size="sm"
              onClick={action.onClick}
              disabled={action.disabled || action.loading}
            >
              {action.loading ? (
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
              ) : (
                action.icon && <span className="mr-2">{action.icon}</span>
              )}
              {action.label}
            </Button>
          ))}
          
          {primaryAction && (
            <Button
              variant={primaryAction.variant || 'default'}
              onClick={primaryAction.onClick}
              disabled={primaryAction.disabled || primaryAction.loading}
            >
              {primaryAction.loading ? (
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
              ) : (
                primaryAction.icon && <span className="mr-2">{primaryAction.icon}</span>
              )}
              {primaryAction.label}
            </Button>
          )}
        </div>
      </div>

      {lastUpdated && (
        <p className="text-xs text-muted-foreground">
          Last updated: {lastUpdated.toLocaleString()}
        </p>
      )}
    </div>
  );

  // Render content based on state
  const renderContent = () => {
    // Loading state
    if (loading) {
      return (
        <div className="flex items-center justify-center py-12">
          <Loading size="lg" text="Loading..." />
        </div>
      );
    }

    // Error state
    if (error) {
      const errorMessage = error instanceof Error ? error.message : error;
      return (
        <Alert variant="destructive">
          <AlertDescription>
            {errorMessage}
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                className="ml-2"
              >
                Try Again
              </Button>
            )}
          </AlertDescription>
        </Alert>
      );
    }

    // Empty state
    if (isEmpty) {
      return (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12 text-center">
            <p className="text-muted-foreground mb-4">{emptyStateMessage}</p>
            {emptyStateAction && (
              <Button
                variant={emptyStateAction.variant || 'default'}
                onClick={emptyStateAction.onClick}
                disabled={emptyStateAction.disabled}
              >
                {emptyStateAction.icon && (
                  <span className="mr-2">{emptyStateAction.icon}</span>
                )}
                {emptyStateAction.label}
              </Button>
            )}
          </CardContent>
        </Card>
      );
    }

    // Normal content
    return children;
  };

  return (
    <div className={cn(
      'min-h-screen bg-background',
      getPaddingClass(),
      className
    )}>
      <div className={cn('mx-auto', getMaxWidthClass())}>
        {renderHeader()}
        
        <div className={cn('mt-6', contentClassName)}>
          {renderContent()}
        </div>
      </div>
    </div>
  );
}
