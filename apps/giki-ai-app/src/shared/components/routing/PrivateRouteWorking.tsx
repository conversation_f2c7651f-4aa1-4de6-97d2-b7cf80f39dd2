/**
 * Private Route Component - Working Version
 * Uses unified auth system without mocking
 */
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useUnifiedServices';

interface PrivateRouteProps {
  children: React.ReactNode;
}

export const PrivateRouteWorking: React.FC<PrivateRouteProps> = ({ children }) => {
  // Use real unified auth system
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading while checking auth
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F8F9FA] flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-[#295343] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Render protected content
  return <>{children}</>;
};

export default PrivateRouteWorking;