/**
 * Global Header Component - Professional B2B Header
 *
 * Provides consistent header across all pages with:
 * - Breadcrumb navigation for user orientation
 * - Connection status indicators
 * - User profile dropdown with professional styling
 * - Brand compliance with design system
 */

import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Wifi, WifiOff, User, Settings, LogOut } from 'lucide-react';
import Logo from '@/shared/components/ui/logo';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu';
import { useAuth } from '@/shared/hooks/useUnifiedServices';

interface GlobalHeaderProps {
  className?: string;
}

// Page title mapping for breadcrumbs
const PAGE_TITLES: Record<string, string> = {
  '/dashboard': 'Dashboard',
  '/upload': 'Upload Files',
  '/transactions': 'Review Transactions',
  '/reports': 'Financial Reports',
  '/categories': 'Category Management',
  '/settings': 'Settings',
  '/profile': 'User Profile',
};

// Connection status simulation - replace with actual connection logic
const useConnectionStatus = () => {
  const [isConnected, setIsConnected] = React.useState(true);
  
  React.useEffect(() => {
    // Simulate checking connection status
    const checkConnection = () => {
      setIsConnected(navigator.onLine);
    };
    
    window.addEventListener('online', checkConnection);
    window.addEventListener('offline', checkConnection);
    
    return () => {
      window.removeEventListener('online', checkConnection);
      window.removeEventListener('offline', checkConnection);
    };
  }, []);
  
  return isConnected;
};

const GlobalHeaderComponent: React.FC<GlobalHeaderProps> = ({ className = '' }) => {
  const location = useLocation();
  const { user, logout } = useAuth();
  const isConnected = useConnectionStatus();

  // Generate breadcrumb trail
  const breadcrumbs = React.useMemo(() => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const crumbs = [{ path: '/dashboard', title: 'Dashboard' }];
    
    let currentPath = '';
    pathSegments.forEach((segment) => {
      currentPath += `/${segment}`;
      const title = PAGE_TITLES[currentPath] || segment.charAt(0).toUpperCase() + segment.slice(1);
      if (currentPath !== '/dashboard') {
        crumbs.push({ path: currentPath, title });
      }
    });
    
    return crumbs;
  }, [location.pathname]);

  const currentPageTitle = PAGE_TITLES[location.pathname] || 'Page';

  return (
    <header 
      className={`
        bg-white border-b border-gray-200 h-16 
        flex items-center justify-between px-6
        sticky top-0 z-50 shadow-sm
        ${className}
      `}
    >
      {/* Left side: Logo and Breadcrumbs */}
      <div className="flex items-center space-x-6">
        {/* Logo */}
        <Link to="/dashboard" className="flex items-center shrink-0">
          <Logo className="h-8 w-auto" showText={true} size="sm" />
        </Link>

        {/* Breadcrumb Navigation */}
        <nav className="hidden md:flex items-center space-x-2 text-sm text-gray-600">
          {breadcrumbs.map((crumb, index) => (
            <React.Fragment key={crumb.path}>
              {index > 0 && (
                <ChevronRight className="h-4 w-4 text-gray-400 shrink-0" />
              )}
              {index === breadcrumbs.length - 1 ? (
                <span className="font-medium text-gray-900 truncate">
                  {crumb.title}
                </span>
              ) : (
                <Link
                  to={crumb.path}
                  className="hover:text-brand-primary transition-colors duration-200 truncate"
                >
                  {crumb.title}
                </Link>
              )}
            </React.Fragment>
          ))}
        </nav>

        {/* Mobile page title */}
        <div className="md:hidden">
          <h1 className="text-lg font-semibold text-gray-900 truncate">
            {currentPageTitle}
          </h1>
        </div>
      </div>

      {/* Right side: Status and User */}
      <div className="flex items-center space-x-4">
        {/* Connection Status */}
        <div className="flex items-center space-x-2">
          {isConnected ? (
            <div className="flex items-center space-x-1 text-success">
              <Wifi className="h-4 w-4" />
              <span className="text-xs font-medium hidden sm:block">Connected</span>
            </div>
          ) : (
            <div className="flex items-center space-x-1 text-error">
              <WifiOff className="h-4 w-4" />
              <span className="text-xs font-medium hidden sm:block">Disconnected</span>
            </div>
          )}
        </div>

        {/* User Profile Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger className="flex items-center space-x-2 hover:bg-gray-50 rounded-lg p-2 transition-colors duration-200">
            <div className="w-8 h-8 bg-brand-primary rounded-lg flex items-center justify-center text-white font-semibold text-sm">
              {user?.email?.[0]?.toUpperCase() || 'U'}
            </div>
            <div className="hidden sm:block text-left">
              <div className="text-sm font-medium text-gray-900 truncate max-w-32">
                {user?.email || 'User'}
              </div>
              <div className="text-xs text-gray-500">
                {user?.role || 'Member'}
              </div>
            </div>
          </DropdownMenuTrigger>
          
          <DropdownMenuContent align="end" className="w-56">
            <div className="px-3 py-2 border-b border-gray-100">
              <div className="font-medium text-gray-900 truncate">
                {user?.email || 'User'}
              </div>
              <div className="text-sm text-gray-500">
                {user?.role || 'Member'}
              </div>
            </div>
            
            <DropdownMenuItem asChild>
              <Link 
                to="/profile" 
                className="flex items-center space-x-2 cursor-pointer"
              >
                <User className="h-4 w-4" />
                <span>Profile</span>
              </Link>
            </DropdownMenuItem>
            
            <DropdownMenuItem asChild>
              <Link 
                to="/settings" 
                className="flex items-center space-x-2 cursor-pointer"
              >
                <Settings className="h-4 w-4" />
                <span>Settings</span>
              </Link>
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem
              onClick={logout}
              className="flex items-center space-x-2 text-error cursor-pointer"
            >
              <LogOut className="h-4 w-4" />
              <span>Sign out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export const GlobalHeader = React.memo(GlobalHeaderComponent);
GlobalHeader.displayName = 'GlobalHeader';

export default GlobalHeader;