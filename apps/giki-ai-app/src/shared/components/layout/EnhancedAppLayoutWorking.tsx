/**
 * Enhanced App Layout - Working Version
 * For systematic app restoration without complex dependencies
 * Professional three-panel layout following design system
 */
import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../../features/auth/hooks/useAuthSimple';

interface EnhancedAppLayoutWorkingProps {
  children: React.ReactNode;
}

export const EnhancedAppLayoutWorking: React.FC<EnhancedAppLayoutWorkingProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const isActivePath = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const navigationItems = [
    { path: '/upload', label: 'Upload', icon: '↑' },
    { path: '/dashboard', label: 'Dashboard', icon: '📊' },
    { path: '/transactions', label: 'Transactions', icon: '💳' },
    { path: '/categories', label: 'Categories', icon: '📁' },
    { path: '/reports', label: 'Reports', icon: '📋' },
    { path: '/settings', label: 'Settings', icon: '⚙️' },
  ];

  return (
    <div className="min-h-screen bg-[#F8F9FA] font-inter">
      {/* Professional Header */}
      <header className="bg-white border-b border-[#E1E5E9] fixed top-0 left-0 right-0 z-50">
        <div className="px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left: Logo and Mobile Menu */}
            <div className="flex items-center gap-4">
              <button
                onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                className="lg:hidden p-2 text-gray-600 hover:text-gray-900"
              >
                ☰
              </button>
              <Link to="/dashboard" className="flex items-center">
                <span className="text-2xl font-semibold text-[#295343]">giki.ai</span>
              </Link>
            </div>

            {/* Right: User Menu */}
            <div className="flex items-center gap-4">
              <div className="hidden sm:block">
                <span className="text-sm text-gray-600">Welcome, {user?.name || user?.email}</span>
              </div>
              <button
                onClick={handleLogout}
                className="text-sm text-[#295343] hover:text-[#1D372E] font-medium"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex pt-16">
        {/* Sidebar Navigation */}
        <nav className={`
          fixed inset-y-0 left-0 transform ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
          lg:relative lg:translate-x-0 transition duration-200 ease-in-out lg:block z-30
          w-64 bg-white border-r border-[#E1E5E9] pt-16 lg:pt-0
        `}>
          <div className="p-6">
            <ul className="space-y-2">
              {navigationItems.map((item) => (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    onClick={() => setIsSidebarOpen(false)}
                    className={`
                      flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200
                      ${isActivePath(item.path)
                        ? 'bg-[#295343] text-white shadow-sm'
                        : 'text-gray-700 hover:bg-[#F1F5F9] hover:text-[#295343]'
                      }
                    `}
                  >
                    <span className="text-lg">{item.icon}</span>
                    <span className="font-medium">{item.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </nav>

        {/* Mobile Overlay */}
        {isSidebarOpen && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden"
            onClick={() => setIsSidebarOpen(false)}
          />
        )}

        {/* Main Content */}
        <main className="flex-1 lg:ml-0">
          <div className="p-6 lg:p-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default EnhancedAppLayoutWorking;