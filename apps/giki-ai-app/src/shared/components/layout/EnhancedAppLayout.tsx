/**
 * Enhanced App Layout - Professional Three Panel System
 *
 * Advanced layout with sophisticated panel mutual exclusion,
 * performance optimizations, and professional B2B design standards.
 *
 * Features:
 * - Professional header with breadcrumbs and user management
 * - Consistent footer with system status and links
 * - Intelligent panel mutual exclusion based on screen size
 * - Smooth transitions and state management
 * - Professional brand color #295343 integration
 * - Geometric icon system (no emojis)
 * - Excel-inspired B2B interface patterns
 * - Enhanced UnifiedAgentPanel integration
 * - Responsive breakpoints for optimal UX
 * - Standardized spacing and border system
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
// Removed Lucide imports - replaced with geometric icons
// X → ✕, User → ⬢, LogOut → ⟶
import Logo from '@/shared/components/ui/logo';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/shared/components/ui/tooltip';
import { useAuth } from '@/shared/hooks/useUnifiedServices';
import { usePanelState } from '@/core/providers/PanelStateContext';
import { ProfessionalAgentPanel } from '@/shared/components/agent/ProfessionalAgentPanel';
import { GlobalHeader } from './GlobalHeader';
import { GlobalFooter } from './GlobalFooter';

interface EnhancedAppLayoutProps {
  children: React.ReactNode;
}

// Professional navigation configuration with geometric icons - Matching Mockups
const navigationItems = [
  {
    href: '/dashboard',
    label: 'Dashboard',
    icon: '□',
    tooltip: 'Financial overview and key performance metrics',
  },
  {
    href: '/files/upload',
    label: 'Upload',
    icon: '↑',
    tooltip: 'Upload transaction files for AI-powered categorization',
  },
  {
    href: '/transactions',
    label: 'Transactions', 
    icon: '⊞',
    tooltip: 'Review, edit, and manage categorized transactions',
  },
  {
    href: '/export',
    label: 'Export',
    icon: '∷',
    tooltip: 'Export your categorized transactions',
  },
  {
    href: '/categories',
    label: 'Categories',
    icon: '⚬',
    tooltip: 'Manage transaction categories and business rules',
  },
];

// Professional layout configuration matching mockups
const LAYOUT_CONFIG = {
  navCollapsed: 64,
  navExpanded: 240,
  agentPanel: 400,
  uploadPanel: 460,
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1280,
  },
} as const;

// Panel width constants
const PANEL_WIDTHS = {
  leftCollapsed: LAYOUT_CONFIG.navCollapsed,
  leftExpanded: LAYOUT_CONFIG.navExpanded,
  agent: LAYOUT_CONFIG.agentPanel,
} as const;

export const EnhancedAppLayout: React.FC<EnhancedAppLayoutProps> = ({
  children,
}) => {
  const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [isNavigationExpanded, setIsNavigationExpanded] = useState(false);

  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();

  // Enhanced panel state management with mutual exclusion
  const {
    isAgentPanelCollapsed,
    isLeftNavCollapsed,
    agentPanelWidth,
    toggleAgentPanel,
    toggleLeftNav,
  } = usePanelState();

  // Professional screen size detection matching mockups
  const updateScreenSize = useCallback(() => {
    const width = window.innerWidth;
    if (width < LAYOUT_CONFIG.breakpoints.mobile) {
      setScreenSize('mobile');
    } else if (width < LAYOUT_CONFIG.breakpoints.tablet) {
      setScreenSize('tablet');
    } else {
      setScreenSize('desktop');
    }
  }, []);

  useEffect(() => {
    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    return () => window.removeEventListener('resize', updateScreenSize);
  }, [updateScreenSize]);

  // Clean three-panel layout with proper mutual exclusion for all screen sizes
  const layoutConfig = useMemo(() => {
    const isMobile = screenSize === 'mobile';
    const isTablet = screenSize === 'tablet';
    const isDesktop = screenSize === 'desktop';
    
    // Mobile: Single column with overlay navigation, agent disabled
    if (isMobile) {
      return {
        navWidth: isNavigationExpanded ? '280px' : '0px',
        navVisible: isNavigationExpanded,
        navOverlay: true,
        agentVisible: false,
        agentOverlay: false,
        gridCols: '1fr',
      };
    }

    // Tablet: Collapsed nav with agent as overlay (doesn't affect grid)
    if (isTablet) {
      return {
        navWidth: `${LAYOUT_CONFIG.navCollapsed}px`,
        navVisible: true,
        navOverlay: false,
        agentVisible: !isAgentPanelCollapsed,
        agentOverlay: true, // Agent as overlay on tablet
        gridCols: `${LAYOUT_CONFIG.navCollapsed}px 1fr`, // Only nav | content (agent is overlay)
      };
    }

    // Desktop: Full three-panel layout with mutual exclusion
    if (isDesktop) {
      const navWidth = isLeftNavCollapsed ? LAYOUT_CONFIG.navCollapsed : LAYOUT_CONFIG.navExpanded;
      const agentVisible = !isAgentPanelCollapsed;
      
      // Dynamic grid columns based on panel visibility
      const gridCols = agentVisible 
        ? `${navWidth}px 1fr ${LAYOUT_CONFIG.agentPanel}px`  // nav | content | agent
        : `${navWidth}px 1fr`;                               // nav | content (no agent column)
      
      return {
        navWidth: `${navWidth}px`,
        navVisible: true,
        navOverlay: false,
        agentVisible,
        agentOverlay: false,
        gridCols,
      };
    }

    // Fallback to desktop behavior
    const navWidth = isLeftNavCollapsed ? LAYOUT_CONFIG.navCollapsed : LAYOUT_CONFIG.navExpanded;
    const agentVisible = !isAgentPanelCollapsed;
    const gridCols = agentVisible 
      ? `${navWidth}px 1fr ${LAYOUT_CONFIG.agentPanel}px`
      : `${navWidth}px 1fr`;
    
    return {
      navWidth: `${navWidth}px`,
      navVisible: true,
      navOverlay: false,
      agentVisible,
      agentOverlay: false,
      gridCols,
    };
  }, [
    screenSize,
    isAgentPanelCollapsed,
    isLeftNavCollapsed,
    isNavigationExpanded,
  ]);

  // Enhanced keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Cmd/Ctrl + K for agent toggle
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault();
        toggleAgentPanel();
      }

      // Cmd/Ctrl + B for navigation toggle
      if ((event.metaKey || event.ctrlKey) && event.key === 'b') {
        event.preventDefault();
        if (screenSize === 'mobile') {
          setIsNavigationExpanded(!isNavigationExpanded);
        } else {
          toggleLeftNav();
        }
      }

      // Escape key management
      if (event.key === 'Escape') {
        if (showUserMenu) {
          setShowUserMenu(false);
        } else if (screenSize === 'mobile' && isNavigationExpanded) {
          setIsNavigationExpanded(false);
        } else if (!isAgentPanelCollapsed) {
          toggleAgentPanel();
        }
      }

      // Navigation shortcuts (Cmd/Ctrl + 1-6)
      if (
        (event.metaKey || event.ctrlKey) &&
        event.key >= '1' &&
        event.key <= '6'
      ) {
        event.preventDefault();
        const index = parseInt(event.key) - 1;
        if (navigationItems[index]) {
          navigate(navigationItems[index].href);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [
    navigate,
    isAgentPanelCollapsed,
    toggleAgentPanel,
    toggleLeftNav,
    showUserMenu,
    screenSize,
    isNavigationExpanded,
  ]);

  // Handle logout
  const handleLogout = () => {
    void (async () => {
      try {
        await logout();
        navigate('/login');
      } catch (error) {
        console.error('Logout failed:', error);
      }
      setShowUserMenu(false);
    })();
  };

  // Navigation toggle for mobile
  const handleNavigationToggle = () => {
    setIsNavigationExpanded(!isNavigationExpanded);
  };

  // Enhanced logo click behavior
  const handleLogoClick = () => {
    if (screenSize === 'mobile') {
      setIsNavigationExpanded(false);
    } else if (!isAgentPanelCollapsed) {
      toggleAgentPanel();
    } else {
      toggleLeftNav();
    }
  };

  return (
    <div className="relative min-h-screen bg-white flex flex-col">
      {/* Global Header */}
      <GlobalHeader />

      {/* Mobile Navigation Toggle Button */}
      {screenSize === 'mobile' && (
        <button
          onClick={() => setIsNavigationExpanded(!isNavigationExpanded)}
          className="fixed top-4 left-4 z-60 bg-brand-primary text-white rounded-lg p-2 shadow-lg touch-target"
          aria-label="Toggle navigation menu"
          style={{ zIndex: 60 }}
        >
          <span className="w-5 h-5 text-lg font-bold flex items-center justify-center">
            {isNavigationExpanded ? '✕' : '☰'}
          </span>
        </button>
      )}

      {/* Mobile Navigation Overlay */}
      {screenSize === 'mobile' && isNavigationExpanded && (
        <div
          className="fixed inset-0 bg-black/50 z-40"
          onClick={() => setIsNavigationExpanded(false)}
        />
      )}

      {/* Main Content Grid Layout */}
      <div 
        className="flex-1 grid transition-all duration-300 ease-out font-sans text-gray-700"
        style={{
          gridTemplateColumns: layoutConfig.gridCols,
          gridTemplateRows: '1fr',
          minHeight: 'calc(100vh - 4rem)', // Account for header height
        }}
      >
        {/* Left Navigation Panel */}
        <div
          className={`
            border-r border-border flex flex-col overflow-hidden transition-all duration-300
            ${
              screenSize === 'mobile'
                ? layoutConfig.navOverlay
                  ? 'fixed inset-y-0 left-0 z-50 shadow-2xl'
                  : 'hidden'
                : 'relative'
            }
          `}
          style={{
            background: 'var(--giki-ai-gradient-processing)',
            width:
              screenSize === 'mobile' && layoutConfig.navOverlay
                ? '280px'
                : layoutConfig.navWidth,
          }}
        >
          {/* Navigation Header - Matching Mockup */}
          <div className="container-padding-sm border-b border-primary-foreground/20 text-center">
            <div className="flex items-center justify-between">
              <div
                className="flex items-center component-gap-sm cursor-pointer hover:bg-primary-foreground/10 card-padding-sm rounded-lg transition-colors flex-1 justify-center"
                onClick={handleLogoClick}
              >
                <Logo
                  size={
                    layoutConfig.navWidth === `${PANEL_WIDTHS.leftCollapsed}px`
                      ? 'sm'
                      : 'md'
                  }
                  showText={layoutConfig.navWidth !== `${PANEL_WIDTHS.leftCollapsed}px`}
                  variant="default"
                  isDarkBackground={true}
                  className="opacity-90"
                />
              </div>

              {screenSize === 'mobile' && (
                <button
                  onClick={() => setIsNavigationExpanded(false)}
                  className="touch-target text-white hover:bg-primary-foreground/10 rounded-lg transition-colors"
                  aria-label="Close navigation menu"
                >
                  <span className="h-5 w-5 text-lg font-bold flex items-center justify-center">✕</span>
                </button>
              )}
            </div>
          </div>

          {/* Enhanced Navigation - Matching Mockup Design */}
          <nav className="flex-1 container-padding-sm overflow-y-auto">
            <div className="container-padding-sm">              
              <div className="list-item-spacing">
                {navigationItems.map((item) => {
                  const isActive =
                    location.pathname === item.href ||
                    (item.href === '/dashboard' && location.pathname === '/') ||
                    (location.pathname.startsWith(item.href) && item.href !== '/') ||
                    (item.href === '/upload' && location.pathname.startsWith('/processing')) ||
                    (item.href === '/upload' && location.pathname.startsWith('/results'));
                  const isCollapsed =
                    layoutConfig.navWidth === `${PANEL_WIDTHS.leftCollapsed}px`;

                  const linkContent = (
                    <Link
                      key={item.href}
                      to={item.href}
                      onClick={() =>
                        screenSize === 'mobile' && setIsNavigationExpanded(false)
                      }
                      className={`
                        nav-item-professional ${isCollapsed ? 'justify-center' : 'justify-start component-gap-sm'} 
                        transition-all duration-150 ease-in-out font-medium ${screenSize === 'mobile' ? 'touch-target' : 'btn-touch-friendly'}
                        ${
                          isActive
                            ? 'bg-primary-foreground/20 text-white font-semibold'
                            : 'text-white/70 hover:bg-primary-foreground/10 hover:text-white'
                        }
                      `}
                      title={item.label}
                    >
                      <span
                        className={`
                          text-2xl w-8 h-8 flex items-center justify-center flex-shrink-0 font-bold
                          ${isActive ? 'text-white' : 'text-white/70'}
                        `}
                      >
                        {item.icon}
                      </span>
                      {!isCollapsed && (
                        <span className={`text-base ${isActive ? 'text-white' : 'text-white/90'}`}>
                          {item.label}
                        </span>
                      )}
                    </Link>
                  );

                  // Only show tooltip for collapsed navigation
                  if (isCollapsed) {
                    return (
                      <Tooltip key={item.href} delayDuration={200}>
                        <TooltipTrigger asChild>{linkContent}</TooltipTrigger>
                        <TooltipContent
                          side="right"
                          align="center"
                          className="bg-giki-primary text-white border-giki-primary"
                        >
                          <p className="font-medium">{item.label}</p>
                          <p className="text-xs opacity-90">{item.tooltip}</p>
                        </TooltipContent>
                      </Tooltip>
                    );
                  }
                  
                  return linkContent;
                })}
              </div>
            </div>
          </nav>

          {/* Enhanced User Section - Dynamic based on nav state */}
          <div className="card-padding-sm border-t border-primary-foreground/20 relative">
            {layoutConfig.navWidth === `${PANEL_WIDTHS.leftCollapsed}px` ? (
              <Tooltip delayDuration={200}>
                <TooltipTrigger asChild>
                  <div
                    className="flex items-center justify-center cursor-pointer hover:bg-primary-foreground/10 rounded-lg 
                               transition-all duration-200 shadow-sm border border-primary-foreground/20 p-3"
                    onClick={() => setShowUserMenu(!showUserMenu)}
                  >
                    <div className="w-8 h-8 rounded-lg bg-white text-brand-primary flex items-center justify-center text-sm font-bold shadow-sm">
                      NS
                    </div>
                  </div>
                </TooltipTrigger>
                <TooltipContent
                  side="right"
                  align="center"
                  className="bg-white text-brand-primary border-brand-primary"
                >
                  <p className="font-medium">Nikhil Singh</p>
                  <p className="text-xs opacity-70">Administrator</p>
                </TooltipContent>
              </Tooltip>
            ) : (
              <div
                className="flex items-center gap-3 cursor-pointer hover:bg-primary-foreground/10 rounded-lg 
                           transition-all duration-200 shadow-sm border border-primary-foreground/20 p-3"
                onClick={() => setShowUserMenu(!showUserMenu)}
              >
                <div className="w-8 h-8 rounded-lg bg-white text-brand-primary flex items-center justify-center text-sm font-bold shadow-sm">
                  NS
                </div>
                <div className="text-left">
                  <p className="text-sm font-medium text-white">Nikhil Singh</p>
                  <p className="text-xs text-white/70">Administrator</p>
                </div>
              </div>
            )}

            {/* Enhanced User Dropdown - Dynamic position based on nav state */}
            {showUserMenu && (
              <div className={`fixed bottom-4 bg-white border border-brand-primary/20 rounded-lg shadow-xl py-2 z-50 min-w-[200px] ${
                layoutConfig.navWidth === `${PANEL_WIDTHS.leftCollapsed}px` ? 'left-20' : 'left-64'
              }`}>
                <div className="px-4 py-3 border-b border-border">
                  <div className="text-sm font-semibold text-brand-primary">
                    Nikhil Singh
                  </div>
                  <div className="text-xs text-muted-foreground"><EMAIL></div>
                </div>

                <button
                  onClick={() => {
                    navigate('/admin');
                    setShowUserMenu(false);
                  }}
                  className="w-full flex items-center gap-3 px-4 py-3 text-sm text-primary-foreground hover:bg-brand-primary/10 transition-colors"
                >
                  <span className="w-4 h-4 text-base font-bold flex items-center justify-center">⬢</span>
                  Account Settings
                </button>

                <button
                  onClick={() => {
                    navigate('/help');
                    setShowUserMenu(false);
                  }}
                  className="w-full flex items-center gap-3 px-4 py-3 text-sm text-primary-foreground hover:bg-brand-primary/10 transition-colors"
                >
                  <span className="w-4 h-4 text-center font-medium">?</span>
                  Help & Support
                </button>

                <div className="border-t border-border my-1" />

                <button
                  onClick={handleLogout}
                  className="w-full flex items-center gap-3 px-4 py-3 text-sm text-destructive hover:bg-destructive/10 transition-colors"
                >
                  <span className="w-4 h-4 text-base font-bold flex items-center justify-center">⟶</span>
                  Sign Out
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Professional Main Content */}
        <main 
          id="main-content"
          className="overflow-auto relative px-6 py-8"
          style={{ 
            backgroundColor: 'var(--giki-bg-secondary)', // Professional surface color
          }}
        >
          {/* Mobile Header */}
          {screenSize === 'mobile' && (
            <div 
              className="sticky top-0 z-30 px-4 py-3"
              style={{ 
                backgroundColor: 'var(--giki-bg-primary)',
                borderBottom: '1px solid var(--giki-border-primary)'
              }}
            >
              <div className="flex items-center justify-between">
                <button
                  onClick={handleNavigationToggle}
                  className="p-2 rounded-lg transition-colors"
                  style={{ 
                    color: 'var(--giki-text-muted)',
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--giki-bg-accent)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <span className="text-lg">≡</span>
                </button>

                <div 
                  className="font-semibold text-lg"
                  style={{ color: 'var(--giki-primary)' }}
                >
                  giki.ai
                </div>

                <button
                  onClick={toggleAgentPanel}
                  className="p-2 rounded-lg transition-colors"
                  style={{ 
                    color: 'var(--giki-text-muted)',
                  }}
                  disabled // Agent panel disabled on mobile
                >
                  <div 
                    className="w-6 h-6 rounded flex items-center justify-center"
                    style={{ backgroundColor: 'var(--giki-primary)/10' }}
                  >
                    <span 
                      className="text-xs font-bold"
                      style={{ color: 'var(--giki-primary)' }}
                    >
                      AI
                    </span>
                  </div>
                </button>
              </div>
            </div>
          )}

          {children}
        </main>

        {/* Professional Agent Panel */}
        {layoutConfig.agentVisible && (
          <aside 
            className="flex flex-col relative overflow-hidden"
            style={{
              backgroundColor: 'var(--giki-bg-primary)',
              borderLeft: '1px solid var(--giki-border-primary)',
              width: `${LAYOUT_CONFIG.agentPanel}px`,
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
            }}
          >
            <ProfessionalAgentPanel
              isOpen={layoutConfig.agentVisible}
              onClose={toggleAgentPanel}
              agentName="giki"
              welcomeMessage="Hello! I'm your AI assistant for financial transaction management. How can I help you today?"
              showQuickActions={true}
              enableFileUpload={true}
              enableVoice={false}
              maxHeight="100vh"
              className="h-full border-none shadow-none rounded-none"
            />
          </aside>
        )}

        {/* Professional Agent Activation Badge */}
        {screenSize !== 'mobile' && (
          <div
            onClick={toggleAgentPanel}
            className="fixed top-1/2 z-50 transition-all duration-300 ease-out w-12 h-12 cursor-pointer flex items-center justify-center"
            style={{
              right: layoutConfig.agentVisible ? `${LAYOUT_CONFIG.agentPanel}px` : '0px',
              transform: 'translateY(-50%)',
              borderRadius: '12px 0 0 12px',
              boxShadow: '-2px 0 8px rgba(0, 0, 0, 0.1)',
              background: 'var(--giki-ai-gradient-processing)',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'var(--giki-primary-hover)';
              e.currentTarget.style.transform = 'translateY(-50%) translateX(-4px)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'var(--giki-ai-gradient-processing)';
              e.currentTarget.style.transform = 'translateY(-50%)';
            }}
            title={
              layoutConfig.agentVisible
                ? 'Close AI Assistant (Cmd+K or Esc)'
                : 'Open AI Assistant (Cmd+K)'
            }
          >
            <div 
              className="w-6 h-6 rounded flex items-center justify-center font-bold text-sm transition-transform duration-200"
              style={{
                background: 'rgba(255, 255, 255, 0.9)',
                color: 'var(--giki-primary)'
              }}
            >
              G
            </div>
            
            {/* Pulse Animation Dot */}
            {!layoutConfig.agentVisible && (
              <div className="absolute top-1/2 left-1/2 w-2 h-2 bg-white rounded-full animate-pulse opacity-60" 
                style={{ transform: 'translate(-50%, -50%)' }}
              />
            )}
          </div>
        )}
      </div>

      {/* Global Footer */}
      <GlobalFooter />

      {/* Mobile menu click outside handler */}
      {screenSize === 'mobile' && isNavigationExpanded && (
        <div
          className="fixed inset-0 z-30"
          onClick={() => setIsNavigationExpanded(false)}
        />
      )}
    </div>
  );
};

export default EnhancedAppLayout;
