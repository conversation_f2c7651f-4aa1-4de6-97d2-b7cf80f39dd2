/**
 * Unified Error Management System (Frontend)
 * ==========================================
 * 
 * Consolidated error handling system that merges functionality from:
 * - shared/utils/errorHandling.ts
 * - shared/types/errors.ts
 * - Various component-specific error handlers
 * 
 * This system provides:
 * - Standardized error types and responses
 * - Consistent error logging and monitoring
 * - Error recovery and retry mechanisms
 * - User-friendly error messages
 * - Error analytics and reporting
 */

import { toast } from '@/shared/components/ui/use-toast';
import { logger } from '@/shared/lib/logger';

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum ErrorCategory {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'not_found',
  BUSINESS_LOGIC = 'business_logic',
  EXTERNAL_SERVICE = 'external_service',
  DATABASE = 'database',
  NETWORK = 'network',
  SYSTEM = 'system',
  UNKNOWN = 'unknown',
}

export interface ErrorContext {
  userId?: string;
  tenantId?: number;
  requestId?: string;
  endpoint?: string;
  method?: string;
  userAgent?: string;
  timestamp: string;
  additionalData?: Record<string, any>;
}

export interface StandardError {
  errorId: string;
  errorCode: string;
  message: string;
  details?: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  timestamp: string;
  context?: ErrorContext;
  suggestions: string[];
  retryAfter?: number;
}

export interface RetryConfig {
  maxAttempts: number;
  initialDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableErrors?: ErrorCategory[];
}

export class UnifiedErrorManager {
  private errorStats: Map<ErrorCategory, number> = new Map();
  private retryAttempts: Map<string, number> = new Map();

  categorizeError(error: any): ErrorCategory {
    // Check for API error response
    if (error?.response?.status) {
      const status = error.response.status;
      if (status === 401) return ErrorCategory.AUTHENTICATION;
      if (status === 403) return ErrorCategory.AUTHORIZATION;
      if (status === 404) return ErrorCategory.NOT_FOUND;
      if (status >= 400 && status < 500) return ErrorCategory.BUSINESS_LOGIC;
      if (status >= 500) return ErrorCategory.SYSTEM;
    }

    // Check for network errors
    if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('network')) {
      return ErrorCategory.NETWORK;
    }

    // Check for validation errors
    if (error?.name === 'ValidationError' || error?.message?.includes('validation')) {
      return ErrorCategory.VALIDATION;
    }

    // Check for backend error categories
    if (error?.category) {
      return error.category as ErrorCategory;
    }

    return ErrorCategory.UNKNOWN;
  }

  determineSeverity(error: any, category: ErrorCategory): ErrorSeverity {
    // Check if severity is provided by backend
    if (error?.severity) {
      return error.severity as ErrorSeverity;
    }

    // Determine based on category
    switch (category) {
      case ErrorCategory.SYSTEM:
      case ErrorCategory.DATABASE:
        return ErrorSeverity.HIGH;
      case ErrorCategory.EXTERNAL_SERVICE:
      case ErrorCategory.NETWORK:
        return ErrorSeverity.MEDIUM;
      case ErrorCategory.VALIDATION:
      case ErrorCategory.BUSINESS_LOGIC:
        return ErrorSeverity.LOW;
      case ErrorCategory.AUTHENTICATION:
      case ErrorCategory.AUTHORIZATION:
        return ErrorSeverity.MEDIUM;
      default:
        return ErrorSeverity.MEDIUM;
    }
  }

  generateErrorId(): string {
    return Math.random().toString(36).substr(2, 8);
  }

  getUserFriendlyMessage(error: any, category: ErrorCategory): string {
    // Use backend message if available
    if (error?.message && typeof error.message === 'string') {
      return error.message;
    }

    const messages: Record<ErrorCategory, string> = {
      [ErrorCategory.VALIDATION]: 'Please check your input and try again.',
      [ErrorCategory.AUTHENTICATION]: 'Please log in to continue.',
      [ErrorCategory.AUTHORIZATION]: "You don't have permission to perform this action.",
      [ErrorCategory.NOT_FOUND]: 'The requested resource was not found.',
      [ErrorCategory.BUSINESS_LOGIC]: 'Unable to complete the request. Please try again.',
      [ErrorCategory.EXTERNAL_SERVICE]: 'External service is temporarily unavailable.',
      [ErrorCategory.DATABASE]: 'Database error occurred. Please try again later.',
      [ErrorCategory.NETWORK]: 'Network error. Please check your connection.',
      [ErrorCategory.SYSTEM]: 'System error occurred. Our team has been notified.',
      [ErrorCategory.UNKNOWN]: 'An unexpected error occurred. Please try again.',
    };

    return messages[category] || 'An error occurred. Please try again.';
  }

  getSuggestions(error: any, category: ErrorCategory): string[] {
    // Use backend suggestions if available
    if (error?.suggestions && Array.isArray(error.suggestions)) {
      return error.suggestions;
    }

    const suggestions: Record<ErrorCategory, string[]> = {
      [ErrorCategory.VALIDATION]: [
        'Check that all required fields are filled',
        'Verify that data formats are correct',
        'Ensure values are within acceptable ranges',
      ],
      [ErrorCategory.AUTHENTICATION]: [
        'Log in with valid credentials',
        'Check if your session has expired',
        'Reset your password if needed',
      ],
      [ErrorCategory.AUTHORIZATION]: [
        'Contact your administrator for access',
        'Verify you have the correct permissions',
        'Check if your account is active',
      ],
      [ErrorCategory.NOT_FOUND]: [
        'Check the URL or resource ID',
        'Verify the resource exists',
        'Try refreshing the page',
      ],
      [ErrorCategory.NETWORK]: [
        'Check your internet connection',
        'Try again in a few moments',
        'Contact support if the problem persists',
      ],
      [ErrorCategory.SYSTEM]: [
        'Try again in a few minutes',
        'Contact support if the issue continues',
        'Check our status page for updates',
      ],
    };

    return suggestions[category] || ['Try again later', 'Contact support if the issue persists'];
  }

  createStandardError(
    error: any,
    context?: Partial<ErrorContext>,
    customMessage?: string
  ): StandardError {
    const category = this.categorizeError(error);
    const severity = this.determineSeverity(error, category);
    const errorId = error?.errorId || this.generateErrorId();

    // Generate error code
    const errorCode = error?.errorCode || `${category.toUpperCase()}_${errorId}`;

    // Get user-friendly message
    const message = customMessage || this.getUserFriendlyMessage(error, category);

    // Get suggestions
    const suggestions = this.getSuggestions(error, category);

    // Determine retry delay
    const retryAfter = error?.retryAfter || (
      [ErrorCategory.EXTERNAL_SERVICE, ErrorCategory.NETWORK, ErrorCategory.SYSTEM].includes(category)
        ? 30 : undefined
    );

    // Update error statistics
    const currentCount = this.errorStats.get(category) || 0;
    this.errorStats.set(category, currentCount + 1);

    return {
      errorId,
      errorCode,
      message,
      details: severity === ErrorSeverity.HIGH || severity === ErrorSeverity.CRITICAL 
        ? error?.details || error?.stack : undefined,
      category,
      severity,
      timestamp: new Date().toISOString(),
      context: {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        ...context,
      },
      suggestions,
      retryAfter,
    };
  }

  logError(error: any, standardError: StandardError, context?: ErrorContext): void {
    const logData = {
      errorId: standardError.errorId,
      errorCode: standardError.errorCode,
      category: standardError.category,
      severity: standardError.severity,
      message: error?.message || error,
      context,
    };

    switch (standardError.severity) {
      case ErrorSeverity.CRITICAL:
        logger.error('Critical error occurred', 'UnifiedErrorManager', logData);
        break;
      case ErrorSeverity.HIGH:
        logger.error('High severity error', 'UnifiedErrorManager', logData);
        break;
      case ErrorSeverity.MEDIUM:
        logger.warn('Medium severity error', 'UnifiedErrorManager', logData);
        break;
      default:
        logger.info('Low severity error', 'UnifiedErrorManager', logData);
    }
  }

  showUserNotification(standardError: StandardError): void {
    const variant = standardError.severity === ErrorSeverity.LOW ? 'default' : 'destructive';
    
    toast({
      variant,
      title: this.getNotificationTitle(standardError.category),
      description: standardError.message,
      duration: this.getNotificationDuration(standardError.severity),
    });
  }

  private getNotificationTitle(category: ErrorCategory): string {
    const titles: Record<ErrorCategory, string> = {
      [ErrorCategory.VALIDATION]: 'Validation Error',
      [ErrorCategory.AUTHENTICATION]: 'Authentication Required',
      [ErrorCategory.AUTHORIZATION]: 'Access Denied',
      [ErrorCategory.NOT_FOUND]: 'Not Found',
      [ErrorCategory.BUSINESS_LOGIC]: 'Request Failed',
      [ErrorCategory.EXTERNAL_SERVICE]: 'Service Unavailable',
      [ErrorCategory.DATABASE]: 'Database Error',
      [ErrorCategory.NETWORK]: 'Connection Error',
      [ErrorCategory.SYSTEM]: 'System Error',
      [ErrorCategory.UNKNOWN]: 'Error',
    };

    return titles[category] || 'Error';
  }

  private getNotificationDuration(severity: ErrorSeverity): number {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 10000; // 10 seconds
      case ErrorSeverity.MEDIUM:
        return 7000; // 7 seconds
      default:
        return 5000; // 5 seconds
    }
  }

  handleError(
    error: any,
    context?: Partial<ErrorContext>,
    options?: {
      showNotification?: boolean;
      customMessage?: string;
      onRetry?: () => void;
    }
  ): StandardError {
    const { showNotification = true, customMessage, onRetry } = options || {};

    // Create standard error
    const standardError = this.createStandardError(error, context, customMessage);

    // Log the error
    this.logError(error, standardError, standardError.context);

    // Show user notification
    if (showNotification) {
      this.showUserNotification(standardError);
    }

    // Handle specific error types
    this.handleSpecificError(standardError, onRetry);

    return standardError;
  }

  private handleSpecificError(standardError: StandardError, onRetry?: () => void): void {
    switch (standardError.category) {
      case ErrorCategory.AUTHENTICATION:
        // Redirect to login or refresh token
        this.handleAuthenticationError();
        break;
      case ErrorCategory.NETWORK:
        // Offer retry option
        if (onRetry && standardError.retryAfter) {
          setTimeout(onRetry, standardError.retryAfter * 1000);
        }
        break;
      case ErrorCategory.SYSTEM:
        // Report to monitoring service
        this.reportToMonitoring(standardError);
        break;
    }
  }

  private handleAuthenticationError(): void {
    // Clear tokens and redirect to login
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    
    // Emit authentication error event
    window.dispatchEvent(new CustomEvent('auth-error', {
      detail: { message: 'Authentication required' }
    }));
  }

  private reportToMonitoring(standardError: StandardError): void {
    // Send error to monitoring service
    // This would integrate with services like Sentry, LogRocket, etc.
    console.error('System error reported:', standardError);
  }

  getErrorStats(): Record<string, any> {
    const stats: Record<string, number> = {};
    this.errorStats.forEach((count, category) => {
      stats[category] = count;
    });

    return {
      totalErrors: Array.from(this.errorStats.values()).reduce((sum, count) => sum + count, 0),
      byCategory: stats,
      timestamp: new Date().toISOString(),
    };
  }

  resetStats(): void {
    this.errorStats.clear();
    this.retryAttempts.clear();
  }
}

// Global error manager instance
export const errorManager = new UnifiedErrorManager();

// Convenience function for handling errors
export function handleError(
  error: any,
  context?: Partial<ErrorContext>,
  options?: {
    showNotification?: boolean;
    customMessage?: string;
    onRetry?: () => void;
  }
): StandardError {
  return errorManager.handleError(error, context, options);
}

// Retry handler class
export class RetryHandler {
  private config: RetryConfig;

  constructor(config: Partial<RetryConfig> = {}) {
    this.config = {
      maxAttempts: 3,
      initialDelay: 1000,
      maxDelay: 10000,
      backoffMultiplier: 2,
      retryableErrors: [
        ErrorCategory.NETWORK,
        ErrorCategory.EXTERNAL_SERVICE,
        ErrorCategory.SYSTEM,
      ],
      ...config,
    };
  }

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= this.config.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        const category = errorManager.categorizeError(error);
        const isRetryable = this.config.retryableErrors?.includes(category) ?? false;
        
        if (!isRetryable || attempt === this.config.maxAttempts) {
          throw error;
        }
        
        const delay = Math.min(
          this.config.initialDelay * Math.pow(this.config.backoffMultiplier, attempt - 1),
          this.config.maxDelay
        );
        
        logger.warn(`Retrying operation (attempt ${attempt}/${this.config.maxAttempts}) after ${delay}ms`, 'RetryHandler');
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }
}

// Default retry handler instance
export const defaultRetryHandler = new RetryHandler();
