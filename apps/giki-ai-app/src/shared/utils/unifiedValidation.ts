/**
 * Unified Validation System (Frontend)
 * ====================================
 * 
 * Consolidated validation system that merges functionality from:
 * - Various component-specific validation logic
 * - Form validation patterns
 * - Input sanitization and normalization
 * 
 * This system provides:
 * - Standardized validation patterns
 * - Reusable validation rules
 * - Consistent error messages
 * - Input sanitization
 * - Type-safe validation
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  sanitizedValue?: any;
  metadata?: Record<string, any>;
}

export abstract class ValidationRule {
  protected errorMessage: string;

  constructor(errorMessage: string = 'Validation failed') {
    this.errorMessage = errorMessage;
  }

  abstract validate(value: any, context?: Record<string, any>): ValidationResult;
}

export class RequiredRule extends ValidationRule {
  constructor(errorMessage: string = 'This field is required') {
    super(errorMessage);
  }

  validate(value: any, context?: Record<string, any>): ValidationResult {
    const isEmpty = value === null || 
                   value === undefined || 
                   (typeof value === 'string' && value.trim() === '') ||
                   (Array.isArray(value) && value.length === 0);

    return {
      isValid: !isEmpty,
      errors: isEmpty ? [this.errorMessage] : [],
      warnings: [],
      sanitizedValue: value,
    };
  }
}

export class EmailRule extends ValidationRule {
  private emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  constructor(errorMessage: string = 'Please enter a valid email address') {
    super(errorMessage);
  }

  validate(value: any, context?: Record<string, any>): ValidationResult {
    if (typeof value !== 'string') {
      return {
        isValid: false,
        errors: ['Email must be a string'],
        warnings: [],
      };
    }

    const trimmedValue = value.trim().toLowerCase();
    const isValid = this.emailRegex.test(trimmedValue);

    return {
      isValid,
      errors: isValid ? [] : [this.errorMessage],
      warnings: [],
      sanitizedValue: trimmedValue,
    };
  }
}

export class PasswordRule extends ValidationRule {
  private minLength: number;
  private requireUppercase: boolean;
  private requireLowercase: boolean;
  private requireDigits: boolean;
  private requireSpecial: boolean;

  constructor(options: {
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireDigits?: boolean;
    requireSpecial?: boolean;
    errorMessage?: string;
  } = {}) {
    const {
      minLength = 8,
      requireUppercase = true,
      requireLowercase = true,
      requireDigits = true,
      requireSpecial = true,
      errorMessage = `Password must be at least ${minLength} characters long`,
    } = options;

    super(errorMessage);
    this.minLength = minLength;
    this.requireUppercase = requireUppercase;
    this.requireLowercase = requireLowercase;
    this.requireDigits = requireDigits;
    this.requireSpecial = requireSpecial;
  }

  validate(value: any, context?: Record<string, any>): ValidationResult {
    if (typeof value !== 'string') {
      return {
        isValid: false,
        errors: ['Password must be a string'],
        warnings: [],
      };
    }

    const errors: string[] = [];

    if (value.length < this.minLength) {
      errors.push(`Password must be at least ${this.minLength} characters long`);
    }

    if (this.requireUppercase && !/[A-Z]/.test(value)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (this.requireLowercase && !/[a-z]/.test(value)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (this.requireDigits && !/\d/.test(value)) {
      errors.push('Password must contain at least one digit');
    }

    if (this.requireSpecial && !/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: [],
      sanitizedValue: value,
    };
  }
}

export class NumericRule extends ValidationRule {
  private minValue?: number;
  private maxValue?: number;
  private allowDecimal: boolean;

  constructor(options: {
    minValue?: number;
    maxValue?: number;
    allowDecimal?: boolean;
    errorMessage?: string;
  } = {}) {
    const {
      minValue,
      maxValue,
      allowDecimal = true,
      errorMessage = 'Please enter a valid number',
    } = options;

    super(errorMessage);
    this.minValue = minValue;
    this.maxValue = maxValue;
    this.allowDecimal = allowDecimal;
  }

  validate(value: any, context?: Record<string, any>): ValidationResult {
    let numericValue: number;

    // Try to convert to number
    if (typeof value === 'string') {
      const trimmed = value.trim();
      numericValue = this.allowDecimal ? parseFloat(trimmed) : parseInt(trimmed, 10);
    } else if (typeof value === 'number') {
      numericValue = value;
    } else {
      return {
        isValid: false,
        errors: [this.errorMessage],
        warnings: [],
      };
    }

    if (isNaN(numericValue)) {
      return {
        isValid: false,
        errors: [this.errorMessage],
        warnings: [],
      };
    }

    const errors: string[] = [];

    if (this.minValue !== undefined && numericValue < this.minValue) {
      errors.push(`Value must be at least ${this.minValue}`);
    }

    if (this.maxValue !== undefined && numericValue > this.maxValue) {
      errors.push(`Value must be at most ${this.maxValue}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: [],
      sanitizedValue: numericValue,
    };
  }
}

export class DateRule extends ValidationRule {
  private minDate?: Date;
  private maxDate?: Date;

  constructor(options: {
    minDate?: Date;
    maxDate?: Date;
    errorMessage?: string;
  } = {}) {
    const {
      minDate,
      maxDate,
      errorMessage = 'Please enter a valid date',
    } = options;

    super(errorMessage);
    this.minDate = minDate;
    this.maxDate = maxDate;
  }

  validate(value: any, context?: Record<string, any>): ValidationResult {
    let dateValue: Date;

    if (typeof value === 'string') {
      dateValue = new Date(value);
    } else if (value instanceof Date) {
      dateValue = value;
    } else {
      return {
        isValid: false,
        errors: [this.errorMessage],
        warnings: [],
      };
    }

    if (isNaN(dateValue.getTime())) {
      return {
        isValid: false,
        errors: [this.errorMessage],
        warnings: [],
      };
    }

    const errors: string[] = [];

    if (this.minDate && dateValue < this.minDate) {
      errors.push(`Date must be on or after ${this.minDate.toLocaleDateString()}`);
    }

    if (this.maxDate && dateValue > this.maxDate) {
      errors.push(`Date must be on or before ${this.maxDate.toLocaleDateString()}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: [],
      sanitizedValue: dateValue,
    };
  }
}

export class FileRule extends ValidationRule {
  private allowedExtensions: string[];
  private maxSizeMB?: number;
  private minSizeKB?: number;

  constructor(options: {
    allowedExtensions?: string[];
    maxSizeMB?: number;
    minSizeKB?: number;
    errorMessage?: string;
  } = {}) {
    const {
      allowedExtensions = [],
      maxSizeMB,
      minSizeKB,
      errorMessage = 'Invalid file',
    } = options;

    super(errorMessage);
    this.allowedExtensions = allowedExtensions.map(ext => ext.toLowerCase());
    this.maxSizeMB = maxSizeMB;
    this.minSizeKB = minSizeKB;
  }

  validate(value: any, context?: Record<string, any>): ValidationResult {
    if (!(value instanceof File)) {
      return {
        isValid: false,
        errors: ['Invalid file object'],
        warnings: [],
      };
    }

    const errors: string[] = [];

    // Check file extension
    if (this.allowedExtensions.length > 0) {
      const extension = value.name.split('.').pop()?.toLowerCase() || '';
      if (!this.allowedExtensions.includes(extension)) {
        errors.push(`File type not allowed. Allowed types: ${this.allowedExtensions.join(', ')}`);
      }
    }

    // Check file size
    if (this.maxSizeMB && value.size > this.maxSizeMB * 1024 * 1024) {
      errors.push(`File size must be less than ${this.maxSizeMB}MB`);
    }

    if (this.minSizeKB && value.size < this.minSizeKB * 1024) {
      errors.push(`File size must be at least ${this.minSizeKB}KB`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: [],
      sanitizedValue: value,
    };
  }
}

export type Sanitizer = (value: any) => any;

export class UnifiedValidator {
  private rules: Map<string, ValidationRule[]> = new Map();
  private sanitizers: Map<string, Sanitizer[]> = new Map();

  addRule(field: string, rule: ValidationRule): UnifiedValidator {
    if (!this.rules.has(field)) {
      this.rules.set(field, []);
    }
    this.rules.get(field)!.push(rule);
    return this;
  }

  addSanitizer(field: string, sanitizer: Sanitizer): UnifiedValidator {
    if (!this.sanitizers.has(field)) {
      this.sanitizers.set(field, []);
    }
    this.sanitizers.get(field)!.push(sanitizer);
    return this;
  }

  validateField(field: string, value: any, context?: Record<string, any>): ValidationResult {
    // Apply sanitizers first
    let sanitizedValue = value;
    const fieldSanitizers = this.sanitizers.get(field) || [];
    for (const sanitizer of fieldSanitizers) {
      sanitizedValue = sanitizer(sanitizedValue);
    }

    // Apply validation rules
    const allErrors: string[] = [];
    const allWarnings: string[] = [];

    const fieldRules = this.rules.get(field) || [];
    for (const rule of fieldRules) {
      const result = rule.validate(sanitizedValue, context);
      if (!result.isValid) {
        allErrors.push(...result.errors);
      }
      allWarnings.push(...result.warnings);

      // Update sanitized value if rule provides one
      if (result.sanitizedValue !== undefined) {
        sanitizedValue = result.sanitizedValue;
      }
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
      sanitizedValue,
    };
  }

  validateData(data: Record<string, any>, context?: Record<string, any>): Record<string, ValidationResult> {
    const results: Record<string, ValidationResult> = {};

    // Validate each field that has rules
    for (const field of this.rules.keys()) {
      const value = data[field];
      results[field] = this.validateField(field, value, context);
    }

    return results;
  }

  isValid(data: Record<string, any>, context?: Record<string, any>): boolean {
    const results = this.validateData(data, context);
    return Object.values(results).every(result => result.isValid);
  }

  getSanitizedData(data: Record<string, any>, context?: Record<string, any>): Record<string, any> {
    const results = this.validateData(data, context);
    const sanitized: Record<string, any> = {};

    for (const [field, result] of Object.entries(results)) {
      sanitized[field] = result.sanitizedValue !== undefined ? result.sanitizedValue : data[field];
    }

    return sanitized;
  }

  getErrors(data: Record<string, any>, context?: Record<string, any>): Record<string, string[]> {
    const results = this.validateData(data, context);
    const errors: Record<string, string[]> = {};

    for (const [field, result] of Object.entries(results)) {
      if (result.errors.length > 0) {
        errors[field] = result.errors;
      }
    }

    return errors;
  }
}

// Common sanitizers
export const stripWhitespace: Sanitizer = (value: any) => 
  typeof value === 'string' ? value.trim() : value;

export const toLowerCase: Sanitizer = (value: any) => 
  typeof value === 'string' ? value.toLowerCase() : value;

export const normalizeEmail: Sanitizer = (value: any) => 
  typeof value === 'string' ? value.trim().toLowerCase() : value;

// Pre-configured validators for common use cases
export const createLoginValidator = (): UnifiedValidator => 
  new UnifiedValidator()
    .addSanitizer('email', normalizeEmail)
    .addRule('email', new RequiredRule())
    .addRule('email', new EmailRule())
    .addRule('password', new RequiredRule());

export const createRegistrationValidator = (): UnifiedValidator => 
  new UnifiedValidator()
    .addSanitizer('email', normalizeEmail)
    .addRule('email', new RequiredRule())
    .addRule('email', new EmailRule())
    .addRule('password', new RequiredRule())
    .addRule('password', new PasswordRule());

export const createTransactionValidator = (): UnifiedValidator => 
  new UnifiedValidator()
    .addSanitizer('description', stripWhitespace)
    .addRule('description', new RequiredRule('Transaction description is required'))
    .addRule('amount', new RequiredRule('Transaction amount is required'))
    .addRule('amount', new NumericRule({ errorMessage: 'Amount must be a valid number' }))
    .addRule('date', new RequiredRule('Transaction date is required'))
    .addRule('date', new DateRule());

export const createFileUploadValidator = (): UnifiedValidator => 
  new UnifiedValidator()
    .addRule('file', new RequiredRule('File is required'))
    .addRule('file', new FileRule({
      allowedExtensions: ['csv', 'xlsx', 'xls'],
      maxSizeMB: 50,
      minSizeKB: 1,
    }));

// Global validator instances
export const loginValidator = createLoginValidator();
export const registrationValidator = createRegistrationValidator();
export const transactionValidator = createTransactionValidator();
export const fileUploadValidator = createFileUploadValidator();
