/**
 * Unified Configuration for Frontend
 * 
 * This module provides access to the unified configuration system while
 * maintaining backward compatibility with existing environment patterns.
 */

// Use legacy environment configuration (unified config not yet available)
const config = {
  environment: import.meta.env.NODE_ENV || 'development',
  api: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || '/api/v1',
    timeout: 30000,
    corsOrigins: ['http://localhost:4200']
  },
  frontend: {
    enableDevTools: import.meta.env.NODE_ENV === 'development',
    enablePerformanceMonitoring: import.meta.env.NODE_ENV === 'production',
    enableAnalytics: import.meta.env.NODE_ENV === 'production',
    logLevel: import.meta.env.NODE_ENV === 'development' ? 'debug' : 'error',
    cacheDuration: import.meta.env.NODE_ENV === 'development' ? 5 * 60 * 1000 : 30 * 60 * 1000,
    maxRetries: import.meta.env.NODE_ENV === 'development' ? 3 : 5,
    retryDelay: import.meta.env.NODE_ENV === 'development' ? 1000 : 2000
  },
  external: {
    avatarService: {
      baseUrl: 'https://ui-avatars.com/api/',
      defaultParams: 'background=random&color=fff'
    }
  }
};
const isUnified = false;

/**
 * Get unified configuration with backward compatibility
 */
export const getUnifiedConfig = () => {
  return {
    ...config,
    // Provide legacy environment interface for backward compatibility
    production: config.environment === 'production',
    apiUrl: config.api?.baseUrl || config.apiUrl,
    enableDevTools: config.frontend?.enableDevTools ?? config.enableDevTools,
    enablePerformanceMonitoring: config.frontend?.enablePerformanceMonitoring ?? config.enablePerformanceMonitoring,
    enableAnalytics: config.frontend?.enableAnalytics ?? config.enableAnalytics,
    logLevel: config.frontend?.logLevel ?? config.logLevel,
    cacheDuration: config.frontend?.cacheDuration ?? config.cacheDuration,
    maxRetries: config.frontend?.maxRetries ?? config.maxRetries,
    retryDelay: config.frontend?.retryDelay ?? config.retryDelay,
    avatarService: config.external?.avatarService ?? config.avatarService,
    
    // Additional unified config properties
    isUnified,
    environment: config.environment,
    api: config.api,
    frontend: config.frontend
  };
};

/**
 * Export unified configuration
 */
export const unifiedConfig = getUnifiedConfig();

/**
 * Legacy environment export for backward compatibility
 */
export const environment = unifiedConfig;

export default unifiedConfig;