/**
 * Unified WebSocket Service - Frontend TypeScript Implementation
 * 
 * This service consolidates all frontend WebSocket patterns:
 * 1. AgentWebSocketService - Agent communication and status updates
 * 2. WebSocketService - General real-time updates and events
 * 3. Scattered WebSocket usage across components - File processing, categorization progress
 * 4. realtimeSync patterns - Event emission and subscription
 * 
 * Features:
 * - Type-safe WebSocket communication
 * - Auto-reconnection with exponential backoff
 * - Message queuing and retry mechanisms
 * - Multi-tenant isolation and authentication
 * - Event-based subscription system
 * - React hooks integration
 * - Backward compatibility with existing patterns
 */

import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';

// ====== TYPES AND INTERFACES ======

export enum MessageType {
  // Connection management
  CONNECTION_ESTABLISHED = 'connection.established',
  CONNECTION_LOST = 'connection.lost',
  HEARTBEAT = 'heartbeat',
  
  // Agent communication
  AGENT_MESSAGE = 'agent.message',
  AGENT_TYPING = 'agent.typing',
  AGENT_STATUS = 'agent.status',
  AGENT_TRANSFER = 'agent.transfer',
  
  // File processing
  FILE_UPLOADED = 'file.uploaded',
  FILE_PROCESSING_STARTED = 'file.processing_started',
  FILE_PROCESSING_PROGRESS = 'file.processing_progress',
  FILE_PROCESSING_COMPLETED = 'file.processing_completed',
  FILE_PROCESSING_ERROR = 'file.processing_error',
  
  // Categorization
  CATEGORIZATION_STARTED = 'categorization.started',
  CATEGORIZATION_PROGRESS = 'categorization.progress',
  CATEGORIZATION_COMPLETED = 'categorization.completed',
  CATEGORIZATION_ERROR = 'categorization.error',
  
  // Transactions
  TRANSACTION_UPDATED = 'transaction.updated',
  TRANSACTION_CATEGORIZED = 'transaction.categorized',
  TRANSACTION_SELECTED = 'transaction.selected',
  
  // Categories
  CATEGORY_CREATED = 'category.created',
  CATEGORY_UPDATED = 'category.updated',
  
  // Reports
  REPORT_GENERATE = 'report.generate',
  REPORT_READY = 'report.ready',
  REPORT_ERROR = 'report.error',
  
  // Accuracy
  ACCURACY_UPDATED = 'accuracy.updated',
  ACCURACY_TEST_STARTED = 'accuracy.test_started',
  ACCURACY_TEST_COMPLETED = 'accuracy.test_completed',
  
  // System notifications
  SYSTEM_NOTIFICATION = 'system.notification',
  SYSTEM_ERROR = 'system.error',
  DATA_REFRESH_REQUIRED = 'data.refresh_required',
  UI_STATE_UPDATE = 'ui.state_update'
}

export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export type ConnectionState = 'connecting' | 'connected' | 'disconnected' | 'error';

export interface WebSocketMessage<T = any> {
  type: MessageType;
  payload: T;
  timestamp: number;
  message_id: string;
  tenant_id?: number;
  user_id?: string;
  priority?: Priority;
  requires_ack?: boolean;
  retry_count?: number;
  expires_at?: number;
}

export interface ConnectionInfo {
  tenant_id?: number;
  user_id?: string;
  connected_at: number;
  last_heartbeat: number;
  subscriptions: Set<MessageType>;
  metadata: Record<string, any>;
}

export interface UnifiedWebSocketConfig {
  url?: string;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  heartbeatInterval?: number;
  messageQueueSize?: number;
  authToken?: string;
  enableAutoReconnect?: boolean;
}

export type MessageHandler<T = any> = (message: WebSocketMessage<T>) => void;
export type ConnectionHandler = (state: ConnectionState) => void;
export type ErrorHandler = (error: Error) => void;

export interface AgentStatus {
  status: 'online' | 'busy' | 'typing' | 'offline';
  lastSeen: string;
  currentTask?: string;
  capabilities: string[];
}

// ====== UNIFIED WEBSOCKET SERVICE ======

export class UnifiedWebSocketService {
  private ws: WebSocket | null = null;
  private config: Required<UnifiedWebSocketConfig>;
  private connectionState: ConnectionState = 'disconnected';
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private sessionId: string;
  private messageQueue: WebSocketMessage[] = [];
  
  // Connection info
  private connectionInfo: ConnectionInfo = {
    connected_at: 0,
    last_heartbeat: 0,
    subscriptions: new Set(),
    metadata: {}
  };
  
  // Event handlers
  private messageHandlers = new Map<MessageType, Set<MessageHandler>>();
  private connectionHandlers = new Set<ConnectionHandler>();
  private errorHandlers = new Set<ErrorHandler>();
  private globalMessageHandlers = new Set<MessageHandler>();
  
  // Statistics
  private stats = {
    connections_total: 0,
    messages_sent: 0,
    messages_received: 0,
    errors: 0,
    reconnections: 0
  };

  constructor(config: UnifiedWebSocketConfig = {}) {
    this.config = {
      url: config.url || this.getWebSocketUrl(),
      reconnectAttempts: config.reconnectAttempts || 5,
      reconnectDelay: config.reconnectDelay || 3000,
      heartbeatInterval: config.heartbeatInterval || 30000,
      messageQueueSize: config.messageQueueSize || 100,
      authToken: config.authToken || '',
      enableAutoReconnect: config.enableAutoReconnect ?? true
    };
    
    this.sessionId = this.generateSessionId();
  }

  // ====== CONNECTION MANAGEMENT ======

  public connect(authToken?: string): void {
    if (authToken) {
      this.config.authToken = authToken;
    }
    
    if (this.connectionState === 'connected' || this.connectionState === 'connecting') {
      return;
    }

    this.setConnectionState('connecting');

    try {
      const wsUrl = new URL(this.config.url);
      
      // Add authentication and session info
      if (this.config.authToken) {
        wsUrl.searchParams.set('token', this.config.authToken);
      }
      wsUrl.searchParams.set('session_id', this.sessionId);

      this.ws = new WebSocket(wsUrl.toString());
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      
    } catch (error) {
      this.handleConnectionError(new Error(`Failed to create WebSocket connection: ${error}`));
    }
  }

  public disconnect(): void {
    this.config.enableAutoReconnect = false;
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.close(1000, 'Client disconnect');
    }

    this.ws = null;
    this.setConnectionState('disconnected');
  }

  public reconnect(): void {
    this.disconnect();
    this.reconnectAttempts = 0;
    this.config.enableAutoReconnect = true;
    this.connect();
  }

  // ====== MESSAGE HANDLING ======

  public sendMessage<T = any>(
    type: MessageType,
    payload: T,
    options: {
      priority?: Priority;
      requires_ack?: boolean;
      expires_in_ms?: number;
    } = {}
  ): void {
    const message: WebSocketMessage<T> = {
      type,
      payload,
      timestamp: Date.now(),
      message_id: this.generateMessageId(),
      tenant_id: this.connectionInfo.tenant_id,
      user_id: this.connectionInfo.user_id,
      priority: options.priority || Priority.MEDIUM,
      requires_ack: options.requires_ack || false,
      retry_count: 0,
      expires_at: options.expires_in_ms ? Date.now() + options.expires_in_ms : undefined
    };

    if (this.isConnected()) {
      this.sendMessageNow(message);
    } else {
      this.queueMessage(message);
    }
  }

  private sendMessageNow(message: WebSocketMessage): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      this.queueMessage(message);
      return;
    }

    try {
      this.ws.send(JSON.stringify(message));
      this.stats.messages_sent++;
    } catch (error) {
      console.error('Failed to send WebSocket message:', error);
      this.stats.errors++;
      this.queueMessage(message);
    }
  }

  private queueMessage(message: WebSocketMessage): void {
    if (this.messageQueue.length >= this.config.messageQueueSize) {
      this.messageQueue.shift(); // Remove oldest message
    }
    this.messageQueue.push(message);
  }

  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift()!;
      
      // Check if message has expired
      if (message.expires_at && Date.now() > message.expires_at) {
        continue;
      }
      
      this.sendMessageNow(message);
    }
  }

  // ====== EVENT SUBSCRIPTION ======

  public subscribe<T = any>(
    messageType: MessageType,
    handler: MessageHandler<T>
  ): () => void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, new Set());
    }
    
    this.messageHandlers.get(messageType)!.add(handler);
    this.connectionInfo.subscriptions.add(messageType);
    
    return () => {
      const handlers = this.messageHandlers.get(messageType);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          this.messageHandlers.delete(messageType);
          this.connectionInfo.subscriptions.delete(messageType);
        }
      }
    };
  }

  public subscribeAll(handler: MessageHandler): () => void {
    this.globalMessageHandlers.add(handler);
    
    return () => {
      this.globalMessageHandlers.delete(handler);
    };
  }

  public onConnectionChange(handler: ConnectionHandler): () => void {
    this.connectionHandlers.add(handler);
    
    return () => {
      this.connectionHandlers.delete(handler);
    };
  }

  public onError(handler: ErrorHandler): () => void {
    this.errorHandlers.add(handler);
    
    return () => {
      this.errorHandlers.delete(handler);
    };
  }

  // ====== EVENT HANDLERS ======

  private handleOpen(): void {
    this.setConnectionState('connected');
    this.reconnectAttempts = 0;
    this.stats.connections_total++;
    
    this.connectionInfo.connected_at = Date.now();
    this.connectionInfo.last_heartbeat = Date.now();
    
    this.startHeartbeat();
    this.flushMessageQueue();
    
    // Send connection established message
    this.sendMessage(MessageType.CONNECTION_ESTABLISHED, {
      sessionId: this.sessionId,
      timestamp: Date.now(),
      capabilities: Object.values(MessageType)
    });
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      if (!this.isValidMessage(message)) {
        console.warn('Invalid WebSocket message received:', message);
        return;
      }
      
      this.stats.messages_received++;
      
      // Handle system messages
      if (message.type === MessageType.HEARTBEAT) {
        this.connectionInfo.last_heartbeat = Date.now();
        return;
      }
      
      // Update connection info from message
      if (message.tenant_id) {
        this.connectionInfo.tenant_id = message.tenant_id;
      }
      if (message.user_id) {
        this.connectionInfo.user_id = message.user_id;
      }
      
      // Notify global handlers
      this.globalMessageHandlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Error in global message handler:', error);
        }
      });
      
      // Notify specific handlers
      const handlers = this.messageHandlers.get(message.type);
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(message);
          } catch (error) {
            console.error(`Error in ${message.type} handler:`, error);
          }
        });
      }
      
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
      this.stats.errors++;
    }
  }

  private handleClose(event: CloseEvent): void {
    this.setConnectionState('disconnected');
    this.stopHeartbeat();
    
    // Attempt to reconnect if not a normal closure and auto-reconnect is enabled
    if (event.code !== 1000 && 
        this.config.enableAutoReconnect && 
        this.reconnectAttempts < this.config.reconnectAttempts) {
      this.scheduleReconnect();
    }
  }

  private handleError(event: Event): void {
    console.error('WebSocket error:', event);
    this.handleConnectionError(new Error('WebSocket connection error'));
  }

  private handleConnectionError(error: Error): void {
    this.setConnectionState('error');
    this.stats.errors++;
    
    this.errorHandlers.forEach(handler => {
      try {
        handler(error);
      } catch (handlerError) {
        console.error('Error in error handler:', handlerError);
      }
    });
  }

  // ====== UTILITY METHODS ======

  private setConnectionState(state: ConnectionState): void {
    if (this.connectionState !== state) {
      this.connectionState = state;
      this.connectionHandlers.forEach(handler => {
        try {
          handler(state);
        } catch (error) {
          console.error('Error in connection handler:', error);
        }
      });
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.reconnectAttempts++;
    this.stats.reconnections++;
    
    const delay = Math.min(
      this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
      30000 // Max 30 seconds
    );

    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, delay);
  }

  private startHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }

    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.sendMessage(MessageType.HEARTBEAT, { timestamp: Date.now() });
      }
    }, this.config.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private isValidMessage(message: any): message is WebSocketMessage {
    return message && 
           typeof message.type === 'string' && 
           message.payload !== undefined && 
           typeof message.message_id === 'string';
  }

  private getWebSocketUrl(): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;

    if (process.env.NODE_ENV === 'development') {
      return 'ws://localhost:8000/ws/agent';
    }

    return `${protocol}//${host}/ws/agent`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // ====== PUBLIC GETTERS ======

  public getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  public getSessionId(): string {
    return this.sessionId;
  }

  public isConnected(): boolean {
    return this.connectionState === 'connected' && 
           this.ws !== null && 
           this.ws.readyState === WebSocket.OPEN;
  }

  public getStats() {
    return {
      ...this.stats,
      connection_state: this.connectionState,
      queued_messages: this.messageQueue.length,
      active_subscriptions: this.connectionInfo.subscriptions.size,
      reconnect_attempts: this.reconnectAttempts
    };
  }

  public getConnectionInfo(): ConnectionInfo {
    return { ...this.connectionInfo };
  }

  public clearQueue(): void {
    this.messageQueue = [];
  }
}

// ====== REACT CONTEXT AND HOOKS ======

interface UnifiedWebSocketContextType {
  service: UnifiedWebSocketService;
  connectionState: ConnectionState;
  isConnected: boolean;
  stats: any;
}

const UnifiedWebSocketContext = createContext<UnifiedWebSocketContextType | null>(null);

export interface UnifiedWebSocketProviderProps {
  children: React.ReactNode;
  config?: UnifiedWebSocketConfig;
  authToken?: string;
  autoConnect?: boolean;
}

export const UnifiedWebSocketProvider: React.FC<UnifiedWebSocketProviderProps> = ({
  children,
  config = {},
  authToken,
  autoConnect = true
}) => {
  const serviceRef = useRef<UnifiedWebSocketService | null>(null);
  const [connectionState, setConnectionState] = useState<ConnectionState>('disconnected');
  const [stats, setStats] = useState({});

  // Initialize service
  if (!serviceRef.current) {
    serviceRef.current = new UnifiedWebSocketService({
      ...config,
      authToken: authToken || config.authToken
    });
  }

  const service = serviceRef.current;

  useEffect(() => {
    // Subscribe to connection changes
    const unsubscribeConnection = service.onConnectionChange(setConnectionState);

    // Auto-connect if enabled
    if (autoConnect) {
      service.connect(authToken);
    }

    // Update stats periodically
    const statsInterval = setInterval(() => {
      setStats(service.getStats());
    }, 5000);

    return () => {
      unsubscribeConnection();
      clearInterval(statsInterval);
      service.disconnect();
    };
  }, [service, authToken, autoConnect]);

  const contextValue: UnifiedWebSocketContextType = {
    service,
    connectionState,
    isConnected: service.isConnected(),
    stats
  };

  return (
    <UnifiedWebSocketContext.Provider value={contextValue}>
      {children}
    </UnifiedWebSocketContext.Provider>
  );
};

// ====== REACT HOOKS ======

export const useUnifiedWebSocket = () => {
  const context = useContext(UnifiedWebSocketContext);
  if (!context) {
    throw new Error('useUnifiedWebSocket must be used within UnifiedWebSocketProvider');
  }
  return context;
};

export const useWebSocketMessage = <T = any>(
  messageType: MessageType,
  handler: (payload: T) => void,
  deps: React.DependencyList = []
) => {
  const { service } = useUnifiedWebSocket();

  const memoizedHandler = useCallback(
    (message: WebSocketMessage<T>) => {
      handler(message.payload);
    },
    [handler, ...deps]
  );

  useEffect(() => {
    return service.subscribe(messageType, memoizedHandler);
  }, [service, messageType, memoizedHandler]);
};

export const useWebSocketSender = () => {
  const { service } = useUnifiedWebSocket();

  return useCallback(<T = any>(
    type: MessageType,
    payload: T,
    options?: {
      priority?: Priority;
      requires_ack?: boolean;
      expires_in_ms?: number;
    }
  ) => {
    service.sendMessage(type, payload, options);
  }, [service]);
};

// ====== CONVENIENCE FUNCTIONS ======

export const createAgentMessage = (
  content: string,
  agent_id: string,
  session_id: string,
  metadata?: any
) => ({
  content,
  agent_id,
  session_id,
  metadata: metadata || {}
});

export const createFileProgressMessage = (
  file_id: string,
  filename: string,
  progress: number,
  total_rows: number,
  processed_rows: number
) => ({
  file_id,
  filename,
  progress_percent: progress,
  total_rows,
  processed_rows,
  status: 'processing'
});

export const createCategorizationProgress = (
  batch_id: string,
  total_transactions: number,
  processed_transactions: number,
  accuracy_score?: number
) => ({
  batch_id,
  total_transactions,
  processed_transactions,
  progress_percent: Math.round((processed_transactions / total_transactions) * 100),
  accuracy_score
});

export const createSystemNotification = (
  title: string,
  message: string,
  notification_type: 'info' | 'success' | 'warning' | 'error' = 'info'
) => ({
  title,
  message,
  notification_type,
  timestamp: Date.now()
});

// ====== BACKWARD COMPATIBILITY ======

// Global service instance for backward compatibility
let globalServiceInstance: UnifiedWebSocketService | null = null;

export const getUnifiedWebSocketService = (config?: UnifiedWebSocketConfig): UnifiedWebSocketService => {
  if (!globalServiceInstance) {
    globalServiceInstance = new UnifiedWebSocketService(config);
  }
  return globalServiceInstance;
};

// Backward compatibility with AgentWebSocketService
export class AgentWebSocketService {
  private unifiedService: UnifiedWebSocketService;

  constructor(config: any = {}) {
    this.unifiedService = new UnifiedWebSocketService({
      url: config.url,
      reconnectAttempts: config.reconnectAttempts,
      reconnectDelay: config.reconnectDelay,
      heartbeatInterval: config.heartbeatInterval,
      authToken: config.authToken
    });
  }

  connect(): void {
    this.unifiedService.connect();
  }

  disconnect(): void {
    this.unifiedService.disconnect();
  }

  sendMessage(content: string, type: any = 'message', metadata?: any): void {
    this.unifiedService.sendMessage(MessageType.AGENT_MESSAGE, {
      content,
      metadata: { ...metadata, sessionId: this.unifiedService.getSessionId() }
    });
  }

  onMessage(handler: (message: any) => void): () => void {
    return this.unifiedService.subscribe(MessageType.AGENT_MESSAGE, (message) => {
      handler({
        type: 'message',
        id: message.message_id,
        content: message.payload.content,
        sender: 'agent',
        timestamp: new Date(message.timestamp).toISOString(),
        metadata: message.payload.metadata
      });
    });
  }

  onConnectionChange(handler: (state: any) => void): () => void {
    return this.unifiedService.onConnectionChange(handler);
  }

  getConnectionState(): ConnectionState {
    return this.unifiedService.getConnectionState();
  }

  isConnected(): boolean {
    return this.unifiedService.isConnected();
  }
}

// Backward compatibility with realtimeSync
export const emit = <T = any>(
  eventType: string,
  payload: T,
  source: 'ui' | 'agent' = 'ui'
): void => {
  const service = getUnifiedWebSocketService();
  const messageType = eventType as MessageType;
  service.sendMessage(messageType, { ...payload, source });
};

// Export singleton for backward compatibility
export const unifiedWebSocketService = getUnifiedWebSocketService();

// Export everything
export default UnifiedWebSocketService;