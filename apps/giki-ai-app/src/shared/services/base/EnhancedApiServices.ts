/**
 * Enhanced API Services - Specialized base services for complex patterns
 * =====================================================================
 * 
 * This module extends the base API service system with specialized services
 * for complex domain patterns that weren't covered in the initial consolidation:
 * 
 * - AccuracyService: AI accuracy testing and validation
 * - IntelligenceService: AI categorization and suggestions  
 * - OnboardingService: File upload and schema discovery
 * - AnalyticsService: Advanced analytics and reporting
 * - AIService: AI-powered operations and suggestions
 * 
 * These services consolidate duplicate patterns from:
 * - features/accuracy/services/accuracyService.ts
 * - features/intelligence/services/adkAgentService.ts
 * - features/onboarding/services/onboardingService.ts
 * - Various analytics and AI-related services
 */

import { BaseApiService, ServiceResult, BaseEntity } from './BaseApiService';

// ==================== SPECIALIZED ENTITY INTERFACES ====================

export interface AccuracyTest extends BaseEntity {
  test_name: string;
  test_type: 'temporal' | 'cross_validation' | 'holdout';
  status: 'pending' | 'running' | 'completed' | 'failed';
  overall_accuracy: number;
  precision: number;
  recall: number;
  f1_score: number;
  tenant_id: number;
}

export interface AIAgent extends BaseEntity {
  agent_id: string;
  name: string;
  description: string;
  status: 'available' | 'busy' | 'offline';
  capabilities: string[];
  last_active: string;
}

export interface OnboardingSession extends BaseEntity {
  session_id: string;
  tenant_id: number;
  onboarding_type: 'zero_onboarding' | 'historical_data' | 'schema_only';
  status: 'initialized' | 'ai_training' | 'accuracy_validation' | 'completed';
  progress: number;
  current_step: string;
}

export interface AnalyticsData {
  metric_name: string;
  value: number;
  timestamp: string;
  dimensions: Record<string, any>;
}

// ==================== ACCURACY SERVICE ====================

export class AccuracyService extends BaseApiService<AccuracyTest> {
  constructor() {
    super({
      baseEndpoint: '/accuracy',
      retryConfig: {
        maxAttempts: 3,
        initialDelay: 2000,
        maxDelay: 10000,
      },
      cacheConfig: {
        enabled: true,
        ttl: 5 * 60 * 1000, // 5 minutes
        key: 'accuracy',
      },
    });
  }

  // Dashboard and metrics
  async getDashboardData(): Promise<ServiceResult<{
    total_tests: number;
    avg_accuracy: number;
    recent_tests: AccuracyTest[];
    accuracy_trends: any[];
  }>> {
    return this.get('/dashboard');
  }

  async getMetrics(options?: {
    timeRange?: string;
  }): Promise<ServiceResult<{
    overall_accuracy: number;
    improvement_over_time: number;
    temporal_consistency: number;
    milestone_progress: any;
  }>> {
    const params = options ? `?${new URLSearchParams(options).toString()}` : '';
    return this.get(`/dashboard/metrics${params}`);
  }

  // Test management
  async createTest(testConfig: {
    test_name: string;
    test_type: string;
    accuracy_threshold?: number;
    start_month?: string;
    end_month?: string;
  }): Promise<ServiceResult<{ test_id: string; status: string }>> {
    return this.post('/tests', testConfig);
  }

  async getTestDetails(testId: string): Promise<ServiceResult<AccuracyTest & {
    monthly_results?: any[];
    category_breakdown?: any;
    confusion_matrix?: any;
  }>> {
    return this.get(`/tests/${testId}`);
  }

  async getTestResults(testId: string, filters?: {
    category_id?: number;
    confidence_threshold?: number;
    page?: number;
    per_page?: number;
  }): Promise<ServiceResult<{
    results: any[];
    total: number;
    accuracy_summary: any;
  }>> {
    const params = filters ? `?${new URLSearchParams(filters as any).toString()}` : '';
    return this.get(`/tests/${testId}/results${params}`);
  }

  // Comparison and analysis
  async compareTests(testIds: string[]): Promise<ServiceResult<{
    tests: AccuracyTest[];
    comparison_metrics: any[];
    recommendations: string[];
  }>> {
    return this.post('/compare', { test_ids: testIds });
  }

  async getAccuracyTrends(options?: {
    days?: number;
    scenario?: string;
  }): Promise<ServiceResult<{
    trend_data: any[];
    summary: any;
  }>> {
    const params = options ? `?${new URLSearchParams(options as any).toString()}` : '';
    return this.get(`/trends${params}`);
  }

  // Milestone validation
  async validateMilestone(tenantId?: number): Promise<ServiceResult<{
    tenant_id: number;
    milestone: string;
    validation_results: any;
    milestone_status: any;
  }>> {
    const data = tenantId ? { tenant_id: tenantId } : {};
    return this.post('/m2/validate', data);
  }

  // Schema management
  async importCategorySchema(schemaData: {
    schema_name: string;
    schema_data: any;
    overwrite_existing?: boolean;
  }): Promise<ServiceResult<{
    schema_id: number;
    categories_imported: number;
    validation_warnings: string[];
  }>> {
    return this.post('/schemas/import', schemaData);
  }

  async listCategorySchemas(): Promise<ServiceResult<{
    schemas: any[];
    total_count: number;
  }>> {
    return this.get('/schemas');
  }
}

// ==================== INTELLIGENCE SERVICE ====================

export class IntelligenceService extends BaseApiService<AIAgent> {
  private currentSession: { sessionId: string; agentId: string } | null = null;

  constructor() {
    super({
      baseEndpoint: '/intelligence',
      retryConfig: {
        maxAttempts: 2,
        initialDelay: 1000,
        maxDelay: 8000,
      },
    });
  }

  // Agent discovery and management
  async discoverAgents(): Promise<ServiceResult<AIAgent[]>> {
    return this.get('/adk/agents/discover');
  }

  async getAgentStatus(): Promise<ServiceResult<{
    totalAgents: number;
    availableAgents: number;
    busyAgents: number;
    offlineAgents: number;
    networkHealth: string;
  }>> {
    return this.get('/adk/agents/status');
  }

  // Session management
  async startSession(agentId: string): Promise<ServiceResult<{
    sessionId: string;
    agentId: string;
    status: string;
  }>> {
    const result = await this.post('/adk/sessions/start', { agent_id: agentId });
    
    if (result.success && result.data) {
      this.currentSession = {
        sessionId: result.data.sessionId,
        agentId: result.data.agentId,
      };
    }
    
    return result;
  }

  async endSession(sessionId?: string): Promise<ServiceResult<void>> {
    const id = sessionId || this.currentSession?.sessionId;
    if (!id) {
      return { success: false, error: new Error('No active session') as any };
    }

    const result = await this.post(`/adk/sessions/${id}/end`, {});
    
    if (result.success) {
      this.currentSession = null;
    }
    
    return result;
  }

  // Tool invocation
  async invokeADKTool(request: {
    toolName: string;
    parameters: any;
    agentId?: string;
    sessionId?: string;
  }): Promise<ServiceResult<any>> {
    const sessionId = request.sessionId || this.currentSession?.sessionId;
    const agentId = request.agentId || this.currentSession?.agentId;

    if (!sessionId || !agentId) {
      return { 
        success: false, 
        error: new Error('No active session. Please start a session first.') as any 
      };
    }

    return this.post('/adk/tools/invoke', {
      session_id: sessionId,
      agent_id: agentId,
      tool_name: request.toolName,
      parameters: request.parameters,
    });
  }

  // Specialized AI operations
  async loadArtifacts(artifactIds: string[]): Promise<ServiceResult<any[]>> {
    return this.invokeADKTool({
      toolName: 'load_artifacts',
      parameters: { artifact_ids: artifactIds },
    });
  }

  async searchKnowledge(query: string): Promise<ServiceResult<any[]>> {
    return this.invokeADKTool({
      toolName: 'vertex_ai_search_tool',
      parameters: {
        query,
        data_store_id: 'financial-knowledge-base',
      },
    });
  }

  async googleSearch(query: string): Promise<ServiceResult<any[]>> {
    return this.invokeADKTool({
      toolName: 'google_search_tool',
      parameters: { query },
    });
  }

  // Categorization services
  async getCategorySuggestions(description: string, options?: {
    limit?: number;
    confidence_threshold?: number;
  }): Promise<ServiceResult<{
    suggestions: any[];
    confidence_scores: number[];
  }>> {
    const params = {
      description,
      ...options,
    };
    return this.post('/categorization/suggest', params);
  }

  async categorizeTransaction(transactionData: {
    description: string;
    amount: number;
    date: string;
    account?: string;
  }): Promise<ServiceResult<{
    category_id: number;
    category_name: string;
    confidence: number;
    reasoning: string;
  }>> {
    return this.post('/categorization/categorize', transactionData);
  }

  // Network status check
  async checkNetworkStatus(): Promise<boolean> {
    try {
      const result = await this.get('/health');
      return result.success;
    } catch {
      return false;
    }
  }
}

// ==================== ONBOARDING SERVICE ====================

export class OnboardingService extends BaseApiService<OnboardingSession> {
  constructor() {
    super({
      baseEndpoint: '/onboarding',
      retryConfig: {
        maxAttempts: 3,
        initialDelay: 2000,
        maxDelay: 15000,
      },
    });
  }

  // Onboarding initialization
  async initializeOnboarding(config: {
    onboardingType: 'zero_onboarding' | 'historical_data' | 'schema_only';
    businessContext?: {
      industry?: string;
      companySize?: string;
      companyWebsite?: string;
    };
  }): Promise<ServiceResult<{
    onboarding_id: string;
    type: string;
    status: string;
    next_step: string;
  }>> {
    const endpoint = config.onboardingType === 'zero_onboarding' 
      ? '/start-zero' 
      : '/start';

    return this.post(endpoint, {
      onboarding_type: config.onboardingType,
      ...config.businessContext,
    });
  }

  // Progress tracking
  async getProgress(onboardingId: string): Promise<ServiceResult<{
    onboarding_id: string;
    type: string;
    status: string;
    progress: number;
    steps_completed: string[];
    current_step: string;
    next_step: string;
    ai_training_results?: any;
  }>> {
    return this.get(`/${onboardingId}/progress`);
  }

  async pollProgress(onboardingId: string, options?: {
    maxAttempts?: number;
    interval?: number;
    onProgress?: (progress: any) => void;
  }): Promise<ServiceResult<any>> {
    const maxAttempts = options?.maxAttempts || 30;
    const interval = options?.interval || 2000;
    
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const result = await this.getProgress(onboardingId);
      
      if (!result.success) {
        return result;
      }

      if (options?.onProgress) {
        options.onProgress(result.data);
      }

      if (result.data?.status === 'completed' || result.data?.status === 'failed') {
        return result;
      }

      await new Promise(resolve => setTimeout(resolve, interval));
    }

    return {
      success: false,
      error: new Error('Onboarding progress polling timed out') as any,
    };
  }

  // File operations
  async uploadFile(file: File, metadata?: any): Promise<ServiceResult<{
    upload_id: string;
    status: string;
  }>> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }

    return this.post('/upload', formData);
  }

  async getFileSchema(uploadId: string): Promise<ServiceResult<{
    headers: string[];
    sample_data: any[];
    suggested_mapping: Record<string, string>;
  }>> {
    return this.get(`/files/${uploadId}/schema`);
  }

  async submitColumnMapping(uploadId: string, mapping: Record<string, string>): Promise<ServiceResult<{
    status: string;
    processing_id: string;
  }>> {
    return this.post(`/files/${uploadId}/column-mapping`, { mapping });
  }

  // Schema discovery
  async getSchemaReport(tenantId: number): Promise<ServiceResult<{
    tenant_id: number;
    has_schema_discovery: boolean;
    schemas_discovered: number;
    total_categories: number;
    unified_categories: number;
    source_files: string[];
    category_mappings: Record<string, string>;
  }>> {
    return this.get(`/schema-mapping-report/${tenantId}`);
  }

  // Completion
  async completeOnboarding(onboardingId: string): Promise<ServiceResult<{
    status: string;
    redirect_url: string;
  }>> {
    return this.post(`/${onboardingId}/complete`, {});
  }
}

// ==================== ANALYTICS SERVICE ====================

export class AnalyticsService extends BaseApiService<AnalyticsData> {
  constructor() {
    super({
      baseEndpoint: '/analytics',
      cacheConfig: {
        enabled: true,
        ttl: 2 * 60 * 1000, // 2 minutes
        key: 'analytics',
      },
    });
  }

  // Dashboard analytics
  async getDashboardMetrics(options?: {
    period?: 'day' | 'week' | 'month' | 'quarter' | 'year';
    start_date?: string;
    end_date?: string;
  }): Promise<ServiceResult<{
    total_transactions: number;
    total_amount: number;
    categorized_percentage: number;
    accuracy_score: number;
    trends: any[];
  }>> {
    const params = options ? `?${new URLSearchParams(options).toString()}` : '';
    return this.get(`/dashboard${params}`);
  }

  // Spending analytics
  async getSpendingByCategory(options?: {
    start_date?: string;
    end_date?: string;
    category_ids?: number[];
    group_by?: 'category' | 'month' | 'vendor';
  }): Promise<ServiceResult<{
    items: any[];
    total_amount: number;
    category_breakdown: any[];
  }>> {
    const params = options ? `?${new URLSearchParams(options as any).toString()}` : '';
    return this.get(`/spending/by-category${params}`);
  }

  async getIncomeExpenseSummary(options?: {
    start_date?: string;
    end_date?: string;
    group_by?: 'month' | 'quarter';
  }): Promise<ServiceResult<{
    total_income: number;
    total_expenses: number;
    net_income_loss: number;
    monthly_breakdown: any[];
  }>> {
    const params = options ? `?${new URLSearchParams(options as any).toString()}` : '';
    return this.get(`/income-expense${params}`);
  }

  // Trend analysis
  async getSpendingTrends(options?: {
    period?: 'month' | 'quarter' | 'year';
    category_ids?: number[];
    compare_previous?: boolean;
  }): Promise<ServiceResult<{
    trend_data: any[];
    growth_rate: number;
    seasonal_patterns: any[];
  }>> {
    const params = options ? `?${new URLSearchParams(options as any).toString()}` : '';
    return this.get(`/trends/spending${params}`);
  }

  async getCategoryTrends(categoryId: number, options?: {
    period?: 'month' | 'quarter';
    months_back?: number;
  }): Promise<ServiceResult<{
    category_name: string;
    trend_data: any[];
    forecast: any[];
    insights: string[];
  }>> {
    const params = options ? `?${new URLSearchParams(options as any).toString()}` : '';
    return this.get(`/trends/category/${categoryId}${params}`);
  }

  // Vendor analysis
  async getVendorAnalysis(options?: {
    start_date?: string;
    end_date?: string;
    min_transactions?: number;
    top_n?: number;
  }): Promise<ServiceResult<{
    vendors: any[];
    total_vendors: number;
    concentration_score: number;
  }>> {
    const params = options ? `?${new URLSearchParams(options as any).toString()}` : '';
    return this.get(`/vendors${params}`);
  }

  // Custom analytics
  async runCustomAnalysis(config: {
    metrics: string[];
    dimensions: string[];
    filters: Record<string, any>;
    date_range: { start: string; end: string };
  }): Promise<ServiceResult<{
    results: any[];
    metadata: any;
    query_info: any;
  }>> {
    return this.post('/custom', config);
  }

  // Export analytics
  async exportAnalytics(config: {
    report_type: string;
    format: 'csv' | 'excel' | 'pdf';
    filters?: Record<string, any>;
  }): Promise<ServiceResult<{
    download_url: string;
    expires_at: string;
    file_size: number;
  }>> {
    return this.post('/export', config);
  }
}

// ==================== AI SERVICE ====================

export class AIService extends BaseApiService<any> {
  constructor() {
    super({
      baseEndpoint: '/ai',
      retryConfig: {
        maxAttempts: 2,
        initialDelay: 1500,
        maxDelay: 8000,
      },
    });
  }

  // Transaction categorization
  async categorizeTransactions(transactions: {
    id: string;
    description: string;
    amount: number;
    date: string;
  }[]): Promise<ServiceResult<{
    categorized: any[];
    confidence_scores: number[];
    suggestions: any[];
  }>> {
    return this.post('/categorization/batch', { transactions });
  }

  async getSuggestions(transactionDescription: string, options?: {
    limit?: number;
    confidence_threshold?: number;
    context?: any;
  }): Promise<ServiceResult<{
    suggestions: any[];
    reasoning: string[];
    confidence_scores: number[];
  }>> {
    return this.post('/suggestions', {
      description: transactionDescription,
      ...options,
    });
  }

  // Pattern recognition
  async detectPatterns(data: {
    transactions?: any[];
    categories?: any[];
    time_range?: { start: string; end: string };
  }): Promise<ServiceResult<{
    patterns: any[];
    insights: string[];
    recommendations: string[];
  }>> {
    return this.post('/patterns/detect', data);
  }

  async getAnomalies(options?: {
    sensitivity?: 'low' | 'medium' | 'high';
    categories?: number[];
    date_range?: { start: string; end: string };
  }): Promise<ServiceResult<{
    anomalies: any[];
    severity_scores: number[];
    explanations: string[];
  }>> {
    const params = options ? `?${new URLSearchParams(options as any).toString()}` : '';
    return this.get(`/anomalies${params}`);
  }

  // Model training and improvement
  async trainModel(config: {
    training_data: any[];
    model_type: 'categorization' | 'anomaly_detection' | 'forecasting';
    parameters?: Record<string, any>;
  }): Promise<ServiceResult<{
    model_id: string;
    training_status: string;
    estimated_completion: string;
  }>> {
    return this.post('/models/train', config);
  }

  async getModelStatus(modelId: string): Promise<ServiceResult<{
    model_id: string;
    status: 'training' | 'completed' | 'failed';
    progress: number;
    metrics?: any;
  }>> {
    return this.get(`/models/${modelId}/status`);
  }

  // Health and diagnostics
  async getAIHealth(): Promise<ServiceResult<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: Record<string, string>;
    response_times: Record<string, number>;
    error_rates: Record<string, number>;
  }>> {
    return this.get('/health');
  }
}
