/**
 * Base services package
 * 
 * This package contains the unified service architecture that consolidates
 * all API service functionality across the frontend application.
 */

export {
  BaseApiService,
  type BaseEntity,
  type PaginatedResponse,
  type ServiceResult,
  type ServiceConfig,
  type CacheConfig,
} from './BaseApiService';

export {
  ServiceFactory,
  AuthService,
  CategoryService,
  TransactionService,
  DashboardService,
  FileService,
  ReportService,
  type User,
  type Category,
  type Transaction,
  type DashboardMetrics,
} from './ServiceFactory';

// Convenience exports for common service instances
export const authService = ServiceFactory.getAuthService();
export const categoryService = ServiceFactory.getCategoryService();
export const transactionService = ServiceFactory.getTransactionService();
export const dashboardService = ServiceFactory.getDashboardService();
export const fileService = ServiceFactory.getFileService();
export const reportService = ServiceFactory.getReportService();
