/**
 * Service Factory
 * ===============
 * 
 * Factory for creating domain-specific API services based on the unified base service.
 * This consolidates service creation patterns and reduces code duplication.
 * 
 * Supported service types:
 * - AuthService: Authentication and user management
 * - CategoryService: Category management and hierarchies
 * - TransactionService: Transaction operations and analysis
 * - DashboardService: Dashboard metrics and analytics
 * - FileService: File upload and processing
 * - ReportService: Report generation and export
 */

import { BaseApiService, ServiceResult, BaseEntity } from './BaseApiService';
import {
  AccuracyService,
  IntelligenceService,
  OnboardingService,
  AnalyticsService,
  AIService
} from './EnhancedApiServices';

// Domain-specific entity interfaces
export interface User extends BaseEntity {
  email: string;
  tenant_id: number;
  is_active: boolean;
}

export interface Category extends BaseEntity {
  name: string;
  parent_id?: number;
  path?: string;
  level?: number;
  gl_code?: string;
  active: boolean;
}

export interface Transaction extends BaseEntity {
  description: string;
  amount: number;
  date: string;
  account?: string;
  category_id?: number;
  tenant_id: number;
}

export interface DashboardMetrics {
  total_transactions: number;
  total_amount: number;
  categorized_percentage: number;
  recent_uploads: number;
}

// Specialized service classes
export class AuthService extends BaseApiService<User> {
  constructor() {
    super({
      baseEndpoint: '/api/v1/auth',
      retryConfig: {
        maxAttempts: 2, // Less aggressive for auth
        initialDelay: 1000,
        maxDelay: 5000,
      },
    });
  }

  async login(email: string, password: string): Promise<ServiceResult<{ access_token: string; refresh_token: string; user: User }>> {
    // Backend expects form data for login
    const formData = new FormData();
    formData.append('username', email);  // Note: backend expects 'username' field for email
    formData.append('password', password);
    
    return this.post('/login', formData);
  }

  async register(email: string, password: string): Promise<ServiceResult<User>> {
    return this.post('/register', { email, password });
  }

  async refreshToken(refreshToken: string): Promise<ServiceResult<{ access_token: string }>> {
    return this.post('/refresh', { refresh_token: refreshToken });
  }

  async logout(): Promise<ServiceResult<void>> {
    return this.post('/logout', {});
  }

  async getCurrentUser(): Promise<ServiceResult<User>> {
    return this.get('/me');
  }

  async updateProfile(data: Partial<User>): Promise<ServiceResult<User>> {
    return this.patch('/me', data);
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<ServiceResult<void>> {
    return this.post('/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    });
  }
}

export class CategoryService extends BaseApiService<Category> {
  constructor() {
    super({
      baseEndpoint: '/categories',
      cacheConfig: {
        enabled: true,
        ttl: 5 * 60 * 1000, // 5 minutes
        key: 'categories',
      },
    });
  }

  async getHierarchy(): Promise<ServiceResult<Category[]>> {
    return this.get('/hierarchy');
  }

  async getCategoryTree(): Promise<ServiceResult<any>> {
    return this.get('/tree');
  }

  async searchCategories(query: string): Promise<ServiceResult<Category[]>> {
    return this.get(`/search?q=${encodeURIComponent(query)}`);
  }

  async getCategoryPath(categoryId: number): Promise<ServiceResult<string>> {
    return this.get(`/${categoryId}/path`);
  }

  async bulkUpdate(updates: { id: number; data: Partial<Category> }[]): Promise<ServiceResult<Category[]>> {
    return this.updateBatch(updates);
  }

  async importCategories(file: File): Promise<ServiceResult<{ imported: number; errors: string[] }>> {
    const formData = new FormData();
    formData.append('file', file);
    
    return this.post('/import', formData);
  }

  async exportCategories(format: 'csv' | 'json' = 'csv'): Promise<ServiceResult<{ download_url: string }>> {
    return this.get(`/export?format=${format}`);
  }
}

export class TransactionService extends BaseApiService<Transaction> {
  constructor() {
    super({
      baseEndpoint: '/transactions',
      cacheConfig: {
        enabled: true,
        ttl: 2 * 60 * 1000, // 2 minutes
        key: 'transactions',
      },
    });
  }

  async searchTransactions(
    query?: string,
    filters?: {
      start_date?: string;
      end_date?: string;
      category_ids?: number[];
      min_amount?: number;
      max_amount?: number;
    }
  ): Promise<ServiceResult<Transaction[]>> {
    const params = new URLSearchParams();
    
    if (query) params.append('q', query);
    if (filters?.start_date) params.append('start_date', filters.start_date);
    if (filters?.end_date) params.append('end_date', filters.end_date);
    if (filters?.category_ids) {
      filters.category_ids.forEach(id => params.append('category_ids', id.toString()));
    }
    if (filters?.min_amount) params.append('min_amount', filters.min_amount.toString());
    if (filters?.max_amount) params.append('max_amount', filters.max_amount.toString());

    return this.get(`/search?${params.toString()}`);
  }

  async categorizeTransaction(transactionId: string, categoryId: number): Promise<ServiceResult<Transaction>> {
    return this.patch(`/${transactionId}/categorize`, { category_id: categoryId });
  }

  async bulkCategorize(updates: { transaction_id: string; category_id: number }[]): Promise<ServiceResult<Transaction[]>> {
    return this.post('/bulk-categorize', { updates });
  }

  async getTransactionAnalytics(
    period: 'week' | 'month' | 'quarter' | 'year' = 'month'
  ): Promise<ServiceResult<any>> {
    return this.get(`/analytics?period=${period}`);
  }

  async exportTransactions(
    format: 'csv' | 'excel' | 'pdf',
    filters?: any
  ): Promise<ServiceResult<{ download_url: string; expires_at: string }>> {
    return this.post('/export', { format, filters });
  }

  async getProcessingStatus(uploadId?: string): Promise<ServiceResult<{
    status: 'processing' | 'completed' | 'failed';
    progress: number;
    processed: number;
    total: number;
  }>> {
    const endpoint = uploadId ? `/processing-status/${uploadId}` : '/processing-status';
    return this.get(endpoint);
  }
}

export class DashboardService extends BaseApiService<any> {
  constructor() {
    super({
      baseEndpoint: '/dashboard',
      cacheConfig: {
        enabled: true,
        ttl: 1 * 60 * 1000, // 1 minute
        key: 'dashboard',
      },
    });
  }

  async getMetrics(): Promise<ServiceResult<DashboardMetrics>> {
    return this.get('/metrics');
  }

  async getRecentTransactions(limit: number = 10): Promise<ServiceResult<Transaction[]>> {
    return this.get(`/recent-transactions?limit=${limit}`);
  }

  async getCategoryBreakdown(): Promise<ServiceResult<any[]>> {
    return this.get('/category-breakdown');
  }

  async getSpendingTrends(period: string = 'month'): Promise<ServiceResult<any[]>> {
    return this.get(`/spending-trends?period=${period}`);
  }

  async refreshMetrics(): Promise<ServiceResult<DashboardMetrics>> {
    this.clearCache('metrics');
    return this.getMetrics();
  }
}

export class FileService extends BaseApiService<any> {
  constructor() {
    super({
      baseEndpoint: '/files',
    });
  }

  async uploadFile(file: File, metadata?: any): Promise<ServiceResult<{ upload_id: string; status: string }>> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }

    return this.post('/upload', formData);
  }

  async uploadMultipleFiles(files: File[]): Promise<ServiceResult<{ upload_ids: string[]; status: string }>> {
    const formData = new FormData();
    
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });

    return this.post('/upload-multiple', formData);
  }

  async getUploadStatus(uploadId: string): Promise<ServiceResult<{
    status: 'processing' | 'completed' | 'failed';
    progress: number;
    message?: string;
  }>> {
    return this.get(`/upload-status/${uploadId}`);
  }

  async getFileColumns(uploadId: string): Promise<ServiceResult<string[]>> {
    return this.get(`/${uploadId}/columns`);
  }

  async submitColumnMapping(uploadId: string, mapping: Record<string, string>): Promise<ServiceResult<any>> {
    return this.post(`/${uploadId}/column-mapping`, { mapping });
  }
}

export class ReportService extends BaseApiService<any> {
  constructor() {
    super({
      baseEndpoint: '/reports',
    });
  }

  async getSpendingByCategory(
    startDate?: string,
    endDate?: string
  ): Promise<ServiceResult<any[]>> {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    
    return this.get(`/spending-by-category?${params.toString()}`);
  }

  async getIncomeExpenseSummary(
    startDate?: string,
    endDate?: string
  ): Promise<ServiceResult<any>> {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    
    return this.get(`/income-expense-summary?${params.toString()}`);
  }

  async generateCustomReport(config: any): Promise<ServiceResult<{ report_id: string; download_url: string }>> {
    return this.post('/custom', config);
  }

  async getSpendingByEntity(
    startDate?: string,
    endDate?: string
  ): Promise<ServiceResult<any[]>> {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    
    return this.get(`/spending-by-entity?${params.toString()}`);
  }

  async getAvailableReportTypes(): Promise<ServiceResult<string[]>> {
    return this.get('/types');
  }
}

// Service Factory
export class ServiceFactory {
  private static instances: Map<string, BaseApiService<any>> = new Map();

  static getAuthService(): AuthService {
    if (!this.instances.has('auth')) {
      this.instances.set('auth', new AuthService());
    }
    return this.instances.get('auth') as AuthService;
  }

  static getCategoryService(): CategoryService {
    if (!this.instances.has('category')) {
      this.instances.set('category', new CategoryService());
    }
    return this.instances.get('category') as CategoryService;
  }

  static getTransactionService(): TransactionService {
    if (!this.instances.has('transaction')) {
      this.instances.set('transaction', new TransactionService());
    }
    return this.instances.get('transaction') as TransactionService;
  }

  static getDashboardService(): DashboardService {
    if (!this.instances.has('dashboard')) {
      this.instances.set('dashboard', new DashboardService());
    }
    return this.instances.get('dashboard') as DashboardService;
  }

  static getFileService(): FileService {
    if (!this.instances.has('file')) {
      this.instances.set('file', new FileService());
    }
    return this.instances.get('file') as FileService;
  }

  static getReportService(): ReportService {
    if (!this.instances.has('report')) {
      this.instances.set('report', new ReportService());
    }
    return this.instances.get('report') as ReportService;
  }

  static clearAllCaches(): void {
    this.instances.forEach(service => {
      if ('clearAllCache' in service) {
        (service as any).clearAllCache();
      }
    });
  }

  static getAccuracyService(): AccuracyService {
    if (!this.instances.has('accuracy')) {
      this.instances.set('accuracy', new AccuracyService());
    }
    return this.instances.get('accuracy') as AccuracyService;
  }

  static getIntelligenceService(): IntelligenceService {
    if (!this.instances.has('intelligence')) {
      this.instances.set('intelligence', new IntelligenceService());
    }
    return this.instances.get('intelligence') as IntelligenceService;
  }

  static getOnboardingService(): OnboardingService {
    if (!this.instances.has('onboarding')) {
      this.instances.set('onboarding', new OnboardingService());
    }
    return this.instances.get('onboarding') as OnboardingService;
  }

  static getAnalyticsService(): AnalyticsService {
    if (!this.instances.has('analytics')) {
      this.instances.set('analytics', new AnalyticsService());
    }
    return this.instances.get('analytics') as AnalyticsService;
  }

  static getAIService(): AIService {
    if (!this.instances.has('ai')) {
      this.instances.set('ai', new AIService());
    }
    return this.instances.get('ai') as AIService;
  }

  static getServiceStats(): Record<string, any> {
    const stats: Record<string, any> = {};

    this.instances.forEach((service, name) => {
      if ('getCacheStats' in service) {
        stats[name] = (service as any).getCacheStats();
      }
    });

    return stats;
  }
}
