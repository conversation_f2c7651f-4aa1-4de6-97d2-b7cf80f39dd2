/**
 * Base API Service
 * ================
 * 
 * Unified base service class that consolidates common patterns from:
 * - features/auth/services/authService.ts
 * - features/categories/services/categoryService.ts
 * - features/transactions/services/transactionService.ts
 * - features/dashboard/services/dashboardService.ts
 * 
 * This base class provides:
 * - Standardized API request handling
 * - Consistent error handling and retry logic
 * - Common CRUD operations
 * - Caching mechanisms
 * - Loading state management
 * - Type-safe API responses
 */

import { apiClient, ApiRequestOptions } from '../api/apiClient';
import { 
  AppError, 
  ErrorType 
} from '@/shared/types/errors';
import { 
  handleApiError, 
  RetryHandler,
  logger 
} from '@/shared/utils/errorHandling';

// Base interfaces
export interface BaseEntity {
  id: string | number;
  created_at?: string;
  updated_at?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

export interface ServiceResult<T> {
  success: boolean;
  data?: T;
  error?: AppError;
  metadata?: Record<string, any>;
}

export interface CacheConfig {
  enabled: boolean;
  ttl: number; // Time to live in milliseconds
  key: string;
}

export interface ServiceConfig {
  baseEndpoint: string;
  retryConfig?: {
    maxAttempts: number;
    initialDelay: number;
    maxDelay: number;
  };
  cacheConfig?: CacheConfig;
}

/**
 * Base API Service class providing common functionality for all domain services.
 */
export abstract class BaseApiService<T extends BaseEntity> {
  protected readonly baseEndpoint: string;
  protected readonly retryHandler: RetryHandler;
  protected readonly cache: Map<string, { data: any; timestamp: number; ttl: number }>;
  protected readonly cacheConfig?: CacheConfig;

  constructor(config: ServiceConfig) {
    this.baseEndpoint = config.baseEndpoint;
    this.cache = new Map();
    this.cacheConfig = config.cacheConfig;
    
    // Initialize retry handler
    this.retryHandler = new RetryHandler({
      maxAttempts: config.retryConfig?.maxAttempts || 3,
      initialDelay: config.retryConfig?.initialDelay || 1000,
      maxDelay: config.retryConfig?.maxDelay || 5000,
      retryableErrors: [ErrorType.NETWORK, ErrorType.SERVER],
    });
  }

  // Cache management
  protected getCacheKey(operation: string, params?: any): string {
    const baseKey = this.cacheConfig?.key || this.baseEndpoint;
    const paramString = params ? JSON.stringify(params) : '';
    return `${baseKey}:${operation}:${paramString}`;
  }

  protected getFromCache<U>(key: string): U | null {
    if (!this.cacheConfig?.enabled) return null;
    
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    const now = Date.now();
    if (now - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data as U;
  }

  protected setCache<U>(key: string, data: U, ttl?: number): void {
    if (!this.cacheConfig?.enabled) return;
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.cacheConfig.ttl,
    });
  }

  protected clearCache(pattern?: string): void {
    if (pattern) {
      const keys = Array.from(this.cache.keys()).filter(key => 
        key.includes(pattern)
      );
      keys.forEach(key => this.cache.delete(key));
    } else {
      this.cache.clear();
    }
  }

  // HTTP request methods with error handling and retry logic
  protected async get<U = T>(
    endpoint: string, 
    options?: ApiRequestOptions
  ): Promise<ServiceResult<U>> {
    try {
      const cacheKey = this.getCacheKey('get', { endpoint, ...options });
      const cached = this.getFromCache<U>(cacheKey);
      
      if (cached) {
        logger.debug(`Cache hit for ${cacheKey}`, 'BaseApiService');
        return { success: true, data: cached };
      }

      const response = await this.retryHandler.execute(async () => {
        return await apiClient.get<U>(endpoint, options);
      });

      this.setCache(cacheKey, response.data);
      
      return { success: true, data: response.data };
    } catch (error) {
      const apiError = handleApiError(error, {
        context: `${this.constructor.name}.get`,
        defaultMessage: `Failed to fetch data from ${endpoint}`,
      });
      
      return { success: false, error: apiError };
    }
  }

  protected async post<U = T>(
    endpoint: string, 
    data: any, 
    options?: ApiRequestOptions
  ): Promise<ServiceResult<U>> {
    try {
      const response = await this.retryHandler.execute(async () => {
        return await apiClient.post<U>(endpoint, data, options);
      });

      // Clear related cache entries
      this.clearCache(endpoint);
      
      return { success: true, data: response.data };
    } catch (error) {
      const apiError = handleApiError(error, {
        context: `${this.constructor.name}.post`,
        defaultMessage: `Failed to create data at ${endpoint}`,
      });
      
      return { success: false, error: apiError };
    }
  }

  protected async put<U = T>(
    endpoint: string, 
    data: any, 
    options?: ApiRequestOptions
  ): Promise<ServiceResult<U>> {
    try {
      const response = await this.retryHandler.execute(async () => {
        return await apiClient.put<U>(endpoint, data, options);
      });

      // Clear related cache entries
      this.clearCache(endpoint);
      
      return { success: true, data: response.data };
    } catch (error) {
      const apiError = handleApiError(error, {
        context: `${this.constructor.name}.put`,
        defaultMessage: `Failed to update data at ${endpoint}`,
      });
      
      return { success: false, error: apiError };
    }
  }

  protected async patch<U = T>(
    endpoint: string, 
    data: any, 
    options?: ApiRequestOptions
  ): Promise<ServiceResult<U>> {
    try {
      const response = await this.retryHandler.execute(async () => {
        return await apiClient.patch<U>(endpoint, data, options);
      });

      // Clear related cache entries
      this.clearCache(endpoint);
      
      return { success: true, data: response.data };
    } catch (error) {
      const apiError = handleApiError(error, {
        context: `${this.constructor.name}.patch`,
        defaultMessage: `Failed to update data at ${endpoint}`,
      });
      
      return { success: false, error: apiError };
    }
  }

  protected async delete<U = void>(
    endpoint: string, 
    options?: ApiRequestOptions
  ): Promise<ServiceResult<U>> {
    try {
      const response = await this.retryHandler.execute(async () => {
        return await apiClient.delete<U>(endpoint, options);
      });

      // Clear related cache entries
      this.clearCache(endpoint);
      
      return { success: true, data: response.data };
    } catch (error) {
      const apiError = handleApiError(error, {
        context: `${this.constructor.name}.delete`,
        defaultMessage: `Failed to delete data at ${endpoint}`,
      });
      
      return { success: false, error: apiError };
    }
  }

  // Common CRUD operations
  async getAll(params?: Record<string, any>): Promise<ServiceResult<T[]>> {
    const queryString = params ? `?${new URLSearchParams(params).toString()}` : '';
    return this.get<T[]>(`${this.baseEndpoint}${queryString}`);
  }

  async getById(id: string | number): Promise<ServiceResult<T>> {
    return this.get<T>(`${this.baseEndpoint}/${id}`);
  }

  async create(data: Omit<T, 'id' | 'created_at' | 'updated_at'>): Promise<ServiceResult<T>> {
    return this.post<T>(this.baseEndpoint, data);
  }

  async update(id: string | number, data: Partial<T>): Promise<ServiceResult<T>> {
    return this.put<T>(`${this.baseEndpoint}/${id}`, data);
  }

  async partialUpdate(id: string | number, data: Partial<T>): Promise<ServiceResult<T>> {
    return this.patch<T>(`${this.baseEndpoint}/${id}`, data);
  }

  async deleteById(id: string | number): Promise<ServiceResult<void>> {
    return this.delete<void>(`${this.baseEndpoint}/${id}`);
  }

  // Paginated operations
  async getPaginated(
    page: number = 1, 
    perPage: number = 20, 
    params?: Record<string, any>
  ): Promise<ServiceResult<PaginatedResponse<T>>> {
    const queryParams = {
      page: page.toString(),
      per_page: perPage.toString(),
      ...params,
    };
    
    const queryString = new URLSearchParams(queryParams).toString();
    return this.get<PaginatedResponse<T>>(`${this.baseEndpoint}?${queryString}`);
  }

  // Batch operations
  async createBatch(items: Omit<T, 'id' | 'created_at' | 'updated_at'>[]): Promise<ServiceResult<T[]>> {
    return this.post<T[]>(`${this.baseEndpoint}/batch`, { items });
  }

  async updateBatch(updates: { id: string | number; data: Partial<T> }[]): Promise<ServiceResult<T[]>> {
    return this.put<T[]>(`${this.baseEndpoint}/batch`, { updates });
  }

  async deleteBatch(ids: (string | number)[]): Promise<ServiceResult<void>> {
    return this.delete<void>(`${this.baseEndpoint}/batch`, {
      data: { ids }
    });
  }

  // Utility methods
  protected buildEndpoint(path: string): string {
    return `${this.baseEndpoint}${path.startsWith('/') ? path : `/${path}`}`;
  }

  protected logOperation(operation: string, details?: any): void {
    logger.info(`${this.constructor.name}: ${operation}`, 'BaseApiService', details);
  }

  protected logError(operation: string, error: any, details?: any): void {
    logger.error(`${this.constructor.name}: ${operation} failed`, 'BaseApiService', {
      error: error.message || error,
      details,
    });
  }

  // Health check
  async healthCheck(): Promise<ServiceResult<{ status: string; timestamp: number }>> {
    return this.get<{ status: string; timestamp: number }>(`${this.baseEndpoint}/health`);
  }

  // Cache management methods
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }

  clearAllCache(): void {
    this.clearCache();
    this.logOperation('Cache cleared');
  }
}
