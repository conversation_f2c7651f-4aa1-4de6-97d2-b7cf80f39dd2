/**
 * Unified File Processing Service - Frontend consolidation
 * =======================================================
 * 
 * This service consolidates all file processing patterns on the frontend:
 * - File upload with progress tracking
 * - File validation and type checking
 * - WebSocket integration for real-time updates
 * - Error handling and recovery
 * - Session management
 * - Consistent API interface
 * 
 * Key features:
 * - Unified upload API for all file types
 * - Real-time progress tracking via WebSocket
 * - Comprehensive error handling
 * - File validation and security checks
 * - Session-based upload management
 * - Retry and recovery mechanisms
 */

import { apiClient } from '@/shared/services/api/apiClient';
import { MessageType, getUnifiedWebSocketService } from '@/shared/services/websocket/UnifiedWebSocketService.tsx';
import { logger } from '@/shared/utils/errorHandling';

export interface FileValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface UploadProgress {
  stage: 'validation' | 'upload' | 'processing' | 'completion';
  progress: number; // 0-100
  message: string;
  details?: Record<string, any>;
}

export interface UploadOptions {
  sessionId?: string;
  metadata?: Record<string, any>;
  onProgress?: (progress: UploadProgress) => void;
  onValidation?: (result: FileValidationResult) => void;
  maxRetries?: number;
  retryDelay?: number;
}

export interface UploadResult {
  success: boolean;
  uploadId?: string;
  filename: string;
  fileSize: number;
  processingResult?: any;
  errors: string[];
  warnings: string[];
  metadata?: Record<string, any>;
}

export interface ProcessingStatus {
  uploadId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  stage: string;
  message: string;
  result?: any;
  error?: string;
}

export class UnifiedFileProcessingService {
  private static instance: UnifiedFileProcessingService;
  private activeUploads = new Map<string, AbortController>();
  private progressCallbacks = new Map<string, (progress: UploadProgress) => void>();

  constructor() {
    this.setupWebSocketListeners();
  }

  static getInstance(): UnifiedFileProcessingService {
    if (!UnifiedFileProcessingService.instance) {
      UnifiedFileProcessingService.instance = new UnifiedFileProcessingService();
    }
    return UnifiedFileProcessingService.instance;
  }

  private setupWebSocketListeners() {
    const webSocketService = getUnifiedWebSocketService();
    
    // Listen for file processing updates
    webSocketService.subscribe(MessageType.FILE_PROCESSING_PROGRESS, (message: any) => {
      const data = message.payload;
      const callback = this.progressCallbacks.get(data.upload_id);
      if (callback) {
        callback({
          stage: data.stage,
          progress: data.progress * 100,
          message: data.message,
          details: data.details
        });
      }
    });

    webSocketService.subscribe(MessageType.FILE_PROCESSING_COMPLETED, (message: any) => {
      const data = message.payload;
      const callback = this.progressCallbacks.get(data.upload_id);
      if (callback) {
        callback({
          stage: 'completion',
          progress: 100,
          message: 'Processing completed',
          details: data.result
        });
      }
      this.progressCallbacks.delete(data.upload_id);
    });

    webSocketService.subscribe(MessageType.FILE_PROCESSING_ERROR, (message: any) => {
      const data = message.payload;
      const callback = this.progressCallbacks.get(data.upload_id);
      if (callback) {
        callback({
          stage: 'completion',
          progress: 100,
          message: `Processing failed: ${data.error}`,
          details: { error: data.error }
        });
      }
      this.progressCallbacks.delete(data.upload_id);
    });
  }

  /**
   * Validate file before upload
   */
  validateFile(
    file: File,
    options: {
      maxSize?: number; // in MB
      allowedTypes?: string[];
      customValidation?: (file: File) => Promise<FileValidationResult>;
    } = {}
  ): FileValidationResult {
    const {
      maxSize = 50,
      allowedTypes = ['.csv', '.xlsx', '.xls'],
      customValidation
    } = options;

    const errors: string[] = [];
    const warnings: string[] = [];

    // Size validation
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSize) {
      errors.push(`File size (${fileSizeMB.toFixed(2)}MB) exceeds maximum allowed size (${maxSize}MB)`);
    }

    // Type validation
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
      errors.push(`File type ${fileExtension} not supported. Allowed types: ${allowedTypes.join(', ')}`);
    }

    // Empty file check
    if (file.size === 0) {
      errors.push('File is empty');
    }

    // Name validation
    if (!file.name || file.name.trim() === '') {
      errors.push('File name is required');
    }

    // Large file warning
    if (fileSizeMB > 10) {
      warnings.push('Large file detected - processing may take longer');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Create upload session
   */
  async createSession(metadata?: Record<string, any>): Promise<string> {
    try {
      const response = await apiClient.post('/api/v1/files/sessions', {
        metadata: metadata || {}
      });

      if (response.data?.session_id) {
        return response.data.session_id;
      }

      throw new Error('Failed to create upload session');
    } catch (error) {
      logger.error('Failed to create upload session:', error);
      throw error;
    }
  }

  /**
   * Upload file with unified processing
   */
  async uploadFile(file: File, options: UploadOptions = {}): Promise<UploadResult> {
    const {
      sessionId,
      metadata = {},
      onProgress,
      onValidation,
      maxRetries = 3,
      retryDelay = 1000
    } = options;

    let uploadId: string | undefined;
    let attempt = 0;

    while (attempt < maxRetries) {
      try {
        // Validation
        onProgress?.({
          stage: 'validation',
          progress: 10,
          message: 'Validating file...'
        });

        const validationResult = this.validateFile(file);
        onValidation?.(validationResult);

        if (!validationResult.valid) {
          return {
            success: false,
            filename: file.name,
            fileSize: file.size,
            errors: validationResult.errors,
            warnings: validationResult.warnings
          };
        }

        // Create session if not provided
        let currentSessionId = sessionId;
        if (!currentSessionId) {
          currentSessionId = await this.createSession(metadata);
        }

        // Setup progress tracking
        onProgress?.({
          stage: 'upload',
          progress: 20,
          message: 'Starting upload...'
        });

        // Create form data
        const formData = new FormData();
        formData.append('file', file);
        formData.append('session_id', currentSessionId);
        
        if (metadata) {
          formData.append('metadata', JSON.stringify(metadata));
        }

        // Setup abort controller
        const abortController = new AbortController();
        uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.activeUploads.set(uploadId, abortController);

        // Register progress callback
        if (onProgress) {
          this.progressCallbacks.set(uploadId, onProgress);
        }

        // Upload file
        const response = await apiClient.post('/api/v1/files/upload-unified', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          signal: abortController.signal,
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              onProgress?.({
                stage: 'upload',
                progress: 20 + (progress * 0.3), // 20-50% for upload
                message: `Uploading... ${progress}%`
              });
            }
          }
        });

        // Cleanup
        this.activeUploads.delete(uploadId);

        if (response.data?.success) {
          const result: UploadResult = {
            success: true,
            uploadId: response.data.upload_id,
            filename: file.name,
            fileSize: file.size,
            processingResult: response.data.processing_result,
            errors: response.data.errors || [],
            warnings: response.data.warnings || validationResult.warnings,
            metadata: response.data.metadata
          };

          onProgress?.({
            stage: 'completion',
            progress: 100,
            message: 'Upload completed successfully'
          });

          return result;
        } else {
          throw new Error(response.data?.error || 'Upload failed');
        }

      } catch (error) {
        attempt++;
        
        if (uploadId) {
          this.activeUploads.delete(uploadId);
          this.progressCallbacks.delete(uploadId);
        }

        if (attempt >= maxRetries) {
          logger.error(`Upload failed after ${maxRetries} attempts:`, error);
          
          return {
            success: false,
            filename: file.name,
            fileSize: file.size,
            errors: [error instanceof Error ? error.message : 'Upload failed'],
            warnings: []
          };
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        
        onProgress?.({
          stage: 'upload',
          progress: 0,
          message: `Retrying upload (attempt ${attempt + 1}/${maxRetries})...`
        });
      }
    }

    // This should never be reached, but TypeScript requires it
    return {
      success: false,
      filename: file.name,
      fileSize: file.size,
      errors: ['Upload failed after all retry attempts'],
      warnings: []
    };
  }

  /**
   * Get processing status
   */
  async getProcessingStatus(uploadId: string): Promise<ProcessingStatus | null> {
    try {
      const response = await apiClient.get(`/api/v1/files/status/${uploadId}`);
      return response.data;
    } catch (error) {
      logger.error('Failed to get processing status:', error);
      return null;
    }
  }

  /**
   * Cancel upload
   */
  cancelUpload(uploadId: string): boolean {
    const controller = this.activeUploads.get(uploadId);
    if (controller) {
      controller.abort();
      this.activeUploads.delete(uploadId);
      this.progressCallbacks.delete(uploadId);
      return true;
    }
    return false;
  }

  /**
   * Get supported file types
   */
  async getSupportedFileTypes(): Promise<string[]> {
    try {
      const response = await apiClient.get('/api/v1/files/supported-types');
      return response.data.extensions || ['.csv', '.xlsx', '.xls'];
    } catch (error) {
      logger.error('Failed to get supported file types:', error);
      return ['.csv', '.xlsx', '.xls']; // Fallback
    }
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    // Cancel all active uploads
    for (const [uploadId, controller] of this.activeUploads) {
      controller.abort();
    }
    this.activeUploads.clear();
    this.progressCallbacks.clear();
  }
}

// Export singleton instance
export const unifiedFileProcessingService = UnifiedFileProcessingService.getInstance();

// Convenience functions
export const uploadFile = (file: File, options?: UploadOptions) => 
  unifiedFileProcessingService.uploadFile(file, options);

export const validateFile = (file: File, options?: Parameters<typeof UnifiedFileProcessingService.prototype.validateFile>[1]) =>
  unifiedFileProcessingService.validateFile(file, options);

export const createUploadSession = (metadata?: Record<string, any>) =>
  unifiedFileProcessingService.createSession(metadata);

export const getProcessingStatus = (uploadId: string) =>
  unifiedFileProcessingService.getProcessingStatus(uploadId);

export const cancelUpload = (uploadId: string) =>
  unifiedFileProcessingService.cancelUpload(uploadId);
