# Frontend API Service Migration Guide

This guide helps migrate from the original unconsolidated services to the new unified service patterns.

## Overview

The frontend API service consolidation eliminates duplicate patterns across:
- Accuracy testing services
- Intelligence/ADK agent services  
- Onboarding services
- Analytics services
- AI-powered services

## Migration Steps

### 1. Accuracy Services

**Before:**
```typescript
import { accuracyService } from '@/features/accuracy/services/accuracyService';

// Usage
const dashboardData = await accuracyService.getDashboardData();
const testResult = await accuracyService.createTest(config);
```

**After:**
```typescript
import { unifiedAccuracyService } from '@/features/accuracy/services/unifiedAccuracyService';

// Usage (same API, enhanced functionality)
const dashboardData = await unifiedAccuracyService.getDashboardData();
const testResult = await unifiedAccuracyService.createTest(config);

// New capabilities
const cacheStats = unifiedAccuracyService.getCacheStats();
unifiedAccuracyService.clearCache();
```

### 2. Intelligence Services

**Before:**
```typescript
import { adkAgentService } from '@/features/intelligence/services/adkAgentService';

// Usage
const agents = await adkAgentService.discoverAgents();
const session = await adkAgentService.startSession(agentId);
```

**After:**
```typescript
import { unifiedIntelligenceService } from '@/features/intelligence/services/unifiedIntelligenceService';

// Usage (same API, enhanced functionality)
const agents = await unifiedIntelligenceService.discoverAgents();
const session = await unifiedIntelligenceService.startSession(agentId);

// Enhanced session management
const networkStatus = await unifiedIntelligenceService.checkNetworkStatus();
```

### 3. Onboarding Services

**Before:**
```typescript
import { onboardingService } from '@/features/onboarding/services/onboardingService';

// Usage
const result = await onboardingService.startZeroOnboarding(config);
const progress = await onboardingService.getProgress(id);
```

**After:**
```typescript
import { unifiedOnboardingService } from '@/features/onboarding/services/unifiedOnboardingService';

// Usage (same API, enhanced functionality)
const result = await unifiedOnboardingService.startZeroOnboarding(config);
const progress = await unifiedOnboardingService.getProgress(id);

// Enhanced progress tracking
const finalResult = await unifiedOnboardingService.pollProgress(id, {
  onProgress: (progress) => console.log(progress),
});
```

### 4. Analytics Services

**Before:**
```typescript
// Multiple scattered analytics services
import { dashboardAnalytics } from '@/services/dashboardAnalytics';
import { spendingAnalytics } from '@/services/spendingAnalytics';
import { trendAnalytics } from '@/services/trendAnalytics';

// Usage
const dashboard = await dashboardAnalytics.getMetrics();
const spending = await spendingAnalytics.getByCategory();
const trends = await trendAnalytics.getSpendingTrends();
```

**After:**
```typescript
import { unifiedAnalyticsService } from '@/shared/services/unifiedAnalyticsService';

// Usage (consolidated API)
const dashboard = await unifiedAnalyticsService.getDashboardMetrics();
const spending = await unifiedAnalyticsService.getSpendingByCategory();
const trends = await unifiedAnalyticsService.getSpendingTrends();

// Convenience methods
const insights = await unifiedAnalyticsService.getSpendingInsights();
const monthly = await unifiedAnalyticsService.getMonthlySummary();
```

## Key Benefits

### 1. Enhanced Error Handling
- Consistent error types and messages
- Automatic retry logic with exponential backoff
- Detailed error logging and tracking

### 2. Improved Caching
- Intelligent caching with configurable TTL
- Cache invalidation strategies
- Cache statistics and monitoring

### 3. Better Performance
- Request deduplication
- Optimized retry strategies
- Connection pooling and reuse

### 4. Type Safety
- Full TypeScript support
- Consistent response types
- Better IDE support and autocomplete

### 5. Monitoring and Debugging
- Built-in logging and metrics
- Health check endpoints
- Service status monitoring

## Breaking Changes

### Service Factory Usage

**Before:**
```typescript
// Direct service instantiation
const service = new AccuracyService();
```

**After:**
```typescript
// Factory pattern (recommended)
import { ServiceFactory } from '@/shared/services/base/ServiceFactory';
const service = ServiceFactory.getAccuracyService();

// Or use unified services directly
import { unifiedAccuracyService } from '@/features/accuracy/services/unifiedAccuracyService';
```

### Error Handling

**Before:**
```typescript
try {
  const result = await service.getData();
  // Handle result
} catch (error) {
  // Basic error handling
  console.error(error);
}
```

**After:**
```typescript
try {
  const result = await service.getData();
  // Handle result
} catch (error) {
  // Enhanced error handling with typed errors
  if (error.type === 'NETWORK_ERROR') {
    // Handle network issues
  } else if (error.type === 'VALIDATION_ERROR') {
    // Handle validation issues
  }
  // Error is automatically logged
}
```

## Configuration

### Service Configuration

```typescript
// Configure services through ServiceFactory
ServiceFactory.configure({
  baseURL: process.env.REACT_APP_API_URL,
  timeout: 30000,
  retryConfig: {
    maxAttempts: 3,
    initialDelay: 1000,
    maxDelay: 10000,
  },
  cacheConfig: {
    enabled: true,
    ttl: 5 * 60 * 1000, // 5 minutes
  },
});
```

### Environment Variables

```env
# API Configuration
REACT_APP_API_URL=https://api.example.com
REACT_APP_API_TIMEOUT=30000

# Service Configuration
REACT_APP_ENABLE_CACHING=true
REACT_APP_CACHE_TTL=300000
REACT_APP_MAX_RETRIES=3
```

## Testing

### Unit Testing

```typescript
import { UnifiedAccuracyService } from '@/features/accuracy/services/unifiedAccuracyService';

describe('UnifiedAccuracyService', () => {
  let service: UnifiedAccuracyService;

  beforeEach(() => {
    service = new UnifiedAccuracyService();
  });

  it('should get dashboard data', async () => {
    const result = await service.getDashboardData();
    expect(result).toBeDefined();
  });

  it('should handle errors gracefully', async () => {
    // Test error scenarios
  });
});
```

### Integration Testing

```typescript
import { ServiceFactory } from '@/shared/services/base/ServiceFactory';

describe('Service Integration', () => {
  beforeAll(() => {
    ServiceFactory.configure({
      baseURL: 'http://localhost:3001',
    });
  });

  it('should work with real API', async () => {
    const service = ServiceFactory.getAccuracyService();
    const result = await service.getDashboardData();
    expect(result).toBeDefined();
  });
});
```

## Monitoring

### Service Health

```typescript
// Check service health
const healthStatus = await unifiedAccuracyService.getHealthStatus();
console.log('Service health:', healthStatus);

// Get cache statistics
const cacheStats = unifiedAccuracyService.getCacheStats();
console.log('Cache stats:', cacheStats);

// Clear cache if needed
unifiedAccuracyService.clearCache();
```

### Performance Monitoring

```typescript
// Get service statistics
const stats = ServiceFactory.getServiceStats();
console.log('All service stats:', stats);

// Monitor specific service
const accuracyStats = ServiceFactory.getAccuracyService().getCacheStats();
console.log('Accuracy service stats:', accuracyStats);
```

## Rollback Plan

If issues arise during migration:

1. **Gradual Migration**: Migrate one service at a time
2. **Feature Flags**: Use feature flags to toggle between old and new services
3. **Parallel Running**: Run both services in parallel during transition
4. **Quick Rollback**: Keep old services available for quick rollback

```typescript
// Feature flag example
const useUnifiedServices = process.env.REACT_APP_USE_UNIFIED_SERVICES === 'true';

const accuracyService = useUnifiedServices 
  ? unifiedAccuracyService 
  : legacyAccuracyService;
```

## Support

For questions or issues during migration:
1. Check the service documentation
2. Review error logs and service health
3. Use the debugging utilities provided
4. Contact the development team for assistance
