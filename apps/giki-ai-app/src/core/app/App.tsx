import React, { Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';

console.log('🎉 App.tsx: Phase 6 COMPLETE - Full MIS Platform Restored with Comprehensive Backend Integration!');

// Auth Pages - Working versions without complex dependencies
const LoginPageWorking = React.lazy(() => import('../../features/auth/pages/LoginPageWorking'));
const RegisterPageWorking = React.lazy(() => import('../../features/auth/pages/RegisterPageWorking'));
const GetStartedPageWorking = React.lazy(() => import('../../features/auth/pages/GetStartedPageWorking'));

// Protected Pages - Working versions with backend integration
const UploadPageWorking = React.lazy(() => import('../../pages/UploadPageWorking'));
const DashboardPageWorking = React.lazy(() => import('../../pages/DashboardPageWorking'));
const TransactionsPageWorking = React.lazy(() => import('../../pages/TransactionsPageWorking'));
const ProcessingPageWorking = React.lazy(() => import('../../pages/ProcessingPageWorking'));
const ReportsPageWorking = React.lazy(() => import('../../pages/ReportsPageWorking'));
const CategoriesPageWorking = React.lazy(() => import('../../pages/CategoriesPageWorking'));
const SettingsPageWorking = React.lazy(() => import('../../pages/SettingsPageWorking'));

// Layout and Route Protection
const PrivateRouteWorking = React.lazy(() => import('../../shared/components/routing/PrivateRouteWorking'));
const EnhancedAppLayoutWorking = React.lazy(() => import('../../shared/components/layout/EnhancedAppLayoutWorking'));

function App() {
  console.log('🎉 App: PHASE 6 COMPLETE - Full MIS Platform with Comprehensive Backend Integration!');
  
  return (
    <div className="min-h-screen">
      <Routes>
        {/* Public Auth Routes */}
        <Route path="/login" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading login...</div>}>
            <LoginPageWorking />
          </Suspense>
        } />
        <Route path="/register" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading register...</div>}>
            <RegisterPageWorking />
          </Suspense>
        } />
        <Route path="/signup" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading register...</div>}>
            <RegisterPageWorking />
          </Suspense>
        } />
        <Route path="/get-started" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading get started...</div>}>
            <GetStartedPageWorking />
          </Suspense>
        } />
        
        {/* Protected Routes with Backend Integration */}
        <Route path="/upload" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading upload...</div>}>
            <PrivateRouteWorking>
              <EnhancedAppLayoutWorking>
                <UploadPageWorking />
              </EnhancedAppLayoutWorking>
            </PrivateRouteWorking>
          </Suspense>
        } />
        
        <Route path="/dashboard" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading dashboard...</div>}>
            <PrivateRouteWorking>
              <EnhancedAppLayoutWorking>
                <DashboardPageWorking />
              </EnhancedAppLayoutWorking>
            </PrivateRouteWorking>
          </Suspense>
        } />
        
        {/* Core protected routes with backend integration */}
        <Route path="/transactions" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading transactions...</div>}>
            <PrivateRouteWorking>
              <EnhancedAppLayoutWorking>
                <TransactionsPageWorking />
              </EnhancedAppLayoutWorking>
            </PrivateRouteWorking>
          </Suspense>
        } />
        
        <Route path="/transactions/review" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading review...</div>}>
            <PrivateRouteWorking>
              <EnhancedAppLayoutWorking>
                <TransactionsPageWorking />
              </EnhancedAppLayoutWorking>
            </PrivateRouteWorking>
          </Suspense>
        } />
        
        <Route path="/processing" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading processing...</div>}>
            <PrivateRouteWorking>
              <EnhancedAppLayoutWorking>
                <ProcessingPageWorking />
              </EnhancedAppLayoutWorking>
            </PrivateRouteWorking>
          </Suspense>
        } />
        
        <Route path="/categories" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading categories...</div>}>
            <PrivateRouteWorking>
              <EnhancedAppLayoutWorking>
                <CategoriesPageWorking />
              </EnhancedAppLayoutWorking>
            </PrivateRouteWorking>
          </Suspense>
        } />
        
        <Route path="/reports" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading reports...</div>}>
            <PrivateRouteWorking>
              <EnhancedAppLayoutWorking>
                <ReportsPageWorking />
              </EnhancedAppLayoutWorking>
            </PrivateRouteWorking>
          </Suspense>
        } />
        
        <Route path="/settings" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading settings...</div>}>
            <PrivateRouteWorking>
              <EnhancedAppLayoutWorking>
                <SettingsPageWorking />
              </EnhancedAppLayoutWorking>
            </PrivateRouteWorking>
          </Suspense>
        } />
        
        {/* Default route - Get Started page */}
        <Route path="/" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading...</div>}>
            <GetStartedPageWorking />
          </Suspense>
        } />
        
        {/* Catch all other routes */}
        <Route path="*" element={
          <div style={{ padding: '20px' }}>
            <h1>🎉 PHASE 6 COMPLETE: Full MIS Platform Restored!</h1>
            <p>All core functionality restored with comprehensive backend integration:</p>
            <ul style={{ marginLeft: '20px', lineHeight: '1.6' }}>
              <li><strong>Public Routes:</strong></li>
              <li><a href="/">/ (Get Started - Marketing landing)</a></li>
              <li><a href="/login">/login (Professional auth)</a></li>
              <li><a href="/register">/register (User registration)</a></li>
              <li><strong>Protected MIS Platform (require login):</strong></li>
              <li><a href="/upload">/upload (File upload with real-time processing)</a></li>
              <li><a href="/processing">/processing (Real-time AI processing status)</a></li>
              <li><a href="/dashboard">/dashboard (MIS metrics & recent activity)</a></li>
              <li><a href="/transactions">/transactions (Transaction management & review)</a></li>
              <li><a href="/categories">/categories (Hierarchical category management)</a></li>
              <li><a href="/reports">/reports (Professional export & reports)</a></li>
              <li><a href="/settings">/settings (User preferences & configuration)</a></li>
            </ul>
            <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f0f9ff', border: '1px solid #0ea5e9', borderRadius: '8px' }}>
              <strong>✅ Complete MIS Workflow Ready:</strong><br/>
              Upload → AI Processing → Transaction Review → Category Management → Professional Reports
            </div>
          </div>
        } />
      </Routes>
    </div>
  );
}

export default App;