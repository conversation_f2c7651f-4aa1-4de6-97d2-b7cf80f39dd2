/**
 * E2E Dashboard Tests
 * End-to-end testing of dashboard functionality and user interactions
 */

import { test, expect } from '@playwright/test';

// Helper function to authenticate
async function authenticateUser(page) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'GikiTest2025Secure');
  await page.click('[data-testid="login-submit"]');
  // Wait for login redirect to upload page
  await expect(page).toHaveURL('/upload');
  // Navigate to dashboard for dashboard tests
  await page.goto('/dashboard');
}

test.describe('Dashboard E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await authenticateUser(page);
  });

  test('should display dashboard with all key metrics', async ({ page }) => {
    // Verify dashboard loads properly
    await expect(page.locator('[data-testid="welcome-message"]')).toContainText('Welcome back');
    
    // Check metrics grid is visible
    await expect(page.locator('[data-testid="dashboard-metrics-grid"]')).toBeVisible();
    
    // Verify all metric cards are present
    await expect(page.locator('text=Total Income')).toBeVisible();
    await expect(page.locator('text=Total Expenses')).toBeVisible();
    await expect(page.locator('text=Net Income')).toBeVisible();
    await expect(page.locator('text=AI Processing')).toBeVisible();
    
    // Check metric values are displayed
    await expect(page.locator('[data-testid="total-income-value"]')).toContainText('$150K');
    await expect(page.locator('[data-testid="total-expenses-value"]')).toContainText('$85,000.25');
    await expect(page.locator('[data-testid="net-income-value"]')).toContainText('$65,000.50');
    await expect(page.locator('[data-testid="transactions-processed"]')).toContainText('1.3K');
  });

  test('should show categorization progress correctly', async ({ page }) => {
    // Check categorization progress
    await expect(page.locator('[data-testid="categorization-progress"]')).toBeVisible();
    await expect(page.locator('text=90.0%')).toBeVisible(); // 1125/1250 = 90%
    
    // Verify progress bar
    const progressBar = page.locator('[data-testid="progress-bar"]');
    await expect(progressBar).toHaveCSS('width', '90%');
    
    // Check categorized vs pending counts
    await expect(page.locator('text=1.1K done')).toBeVisible();
    await expect(page.locator('text=125 pending')).toBeVisible();
  });

  test('should display recent transactions list', async ({ page }) => {
    // Recent transactions section should be visible
    await expect(page.locator('[data-testid="recent-transactions"]')).toBeVisible();
    await expect(page.locator('text=Recent Transactions')).toBeVisible();
    
    // Should show transaction items
    await expect(page.locator('[data-testid="transaction-item"]')).toHaveCount(5);
    
    // Check transaction data
    await expect(page.locator('text=Coffee Shop Payment')).toBeVisible();
    await expect(page.locator('text=Client Payment Received')).toBeVisible();
    
    // Verify view all transactions link
    await expect(page.locator('[data-testid="view-all-transactions"]')).toBeVisible();
  });

  test('should navigate to detailed views from dashboard', async ({ page }) => {
    // Click on view all transactions
    await page.click('[data-testid="view-all-transactions"]');
    await expect(page).toHaveURL('/transactions');
    
    // Go back to dashboard
    await page.click('a[href="/dashboard"]');
    await expect(page).toHaveURL('/dashboard');
    
    // Click on upload button/link
    await page.click('[data-testid="upload-new-file"]');
    await expect(page).toHaveURL('/upload');
    
    // Return to dashboard
    await page.click('a[href="/dashboard"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('should display category breakdown chart', async ({ page }) => {
    // Check if category chart is present
    await expect(page.locator('[data-testid="category-breakdown-chart"]')).toBeVisible();
    await expect(page.locator('text=Category Breakdown')).toBeVisible();
    
    // Verify chart has data
    await expect(page.locator('[data-testid="chart-container"]')).toBeVisible();
    
    // Check category legend
    await expect(page.locator('[data-testid="category-legend"]')).toBeVisible();
  });

  test('should show spending trends over time', async ({ page }) => {
    // Monthly trends chart should be visible
    await expect(page.locator('[data-testid="spending-trends-chart"]')).toBeVisible();
    await expect(page.locator('text=Spending Trends')).toBeVisible();
    
    // Verify trend data points
    await expect(page.locator('[data-testid="trend-chart"]')).toBeVisible();
  });

  test('should display quick actions panel', async ({ page }) => {
    // Quick actions should be available
    await expect(page.locator('[data-testid="quick-actions"]')).toBeVisible();
    await expect(page.locator('text=Quick Actions')).toBeVisible();
    
    // Check action items
    await expect(page.locator('[data-testid="quick-upload"]')).toBeVisible();
    await expect(page.locator('[data-testid="quick-review"]')).toBeVisible();
    await expect(page.locator('[data-testid="quick-reports"]')).toBeVisible();
  });

  test('should open and interact with agent panel', async ({ page }) => {
    // Open agent panel
    await page.click('[data-testid="agent-panel-toggle"]');
    
    // Agent panel should be visible
    await expect(page.locator('[data-testid="professional-agent-panel"]')).toBeVisible();
    await expect(page.locator('text=AI Assistant')).toBeVisible();
    
    // Try sending a message
    await page.fill('[data-testid="agent-input"]', 'Show me my expense summary');
    await page.click('[data-testid="agent-send"]');
    
    // Should show response
    await expect(page.locator('[data-testid="agent-message"]')).toBeVisible();
    
    // Close agent panel
    await page.click('[data-testid="agent-panel-close"]');
    await expect(page.locator('[data-testid="professional-agent-panel"]')).not.toBeVisible();
  });

  test('should handle date range filtering', async ({ page }) => {
    // Open date range picker
    await page.click('[data-testid="date-range-picker"]');
    
    // Should show date selection
    await expect(page.locator('[data-testid="date-picker-modal"]')).toBeVisible();
    
    // Select last 30 days
    await page.click('[data-testid="last-30-days"]');
    
    // Dashboard should update with new date range
    await expect(page.locator('[data-testid="current-date-range"]')).toContainText('Last 30 days');
    
    // Metrics should refresh
    await expect(page.locator('[data-testid="dashboard-metrics-grid"]')).toBeVisible();
  });

  test('should refresh dashboard data', async ({ page }) => {
    // Click refresh button
    await page.click('[data-testid="refresh-dashboard"]');
    
    // Should show loading state
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
    
    // Should complete loading
    await expect(page.locator('[data-testid="loading-spinner"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="dashboard-metrics-grid"]')).toBeVisible();
  });

  test('should handle empty state when no data', async ({ page }) => {
    // Simulate empty data by intercepting API
    await page.route('/api/v1/dashboard/overview', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          metrics: {
            totalIncome: 0,
            totalExpenses: 0,
            netIncome: 0,
            totalTransactions: 0,
            categorizedTransactions: 0,
            uncategorizedTransactions: 0,
          },
          recentTransactions: [],
          categoryBreakdown: [],
        }),
      });
    });
    
    // Refresh to get empty data
    await page.reload();
    
    // Should show empty state
    await expect(page.locator('[data-testid="empty-dashboard"]')).toBeVisible();
    await expect(page.locator('text=No data available')).toBeVisible();
    await expect(page.locator('[data-testid="upload-first-file"]')).toBeVisible();
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Simulate API error
    await page.route('/api/v1/dashboard/overview', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ detail: 'Internal server error' }),
      });
    });
    
    // Refresh to trigger error
    await page.reload();
    
    // Should show error state
    await expect(page.locator('[data-testid="dashboard-error"]')).toBeVisible();
    await expect(page.locator('text=Unable to load dashboard data')).toBeVisible();
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
    
    // Test retry functionality
    await page.click('[data-testid="retry-button"]');
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
  });

  test('should display processing status correctly', async ({ page }) => {
    // Check processing status card
    await expect(page.locator('[data-testid="processing-status"]')).toBeVisible();
    
    // Should show file processing info
    await expect(page.locator('text=8 of 10 files processed')).toBeVisible();
    await expect(page.locator('text=2 files pending')).toBeVisible();
    
    // Should show last processed timestamp
    await expect(page.locator('[data-testid="last-processed"]')).toBeVisible();
  });

  test('should show accuracy improvements over time', async ({ page }) => {
    // Accuracy history should be visible
    await expect(page.locator('[data-testid="accuracy-history"]')).toBeVisible();
    await expect(page.locator('text=Accuracy Trend')).toBeVisible();
    
    // Should show improvement metrics
    await expect(page.locator('[data-testid="accuracy-improvement"]')).toContainText('+15%');
  });

  test('should support mobile responsive layout', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 812 });
    
    // Dashboard should adapt to mobile
    await expect(page.locator('[data-testid="mobile-dashboard"]')).toBeVisible();
    
    // Metrics should stack vertically
    await expect(page.locator('[data-testid="dashboard-metrics-grid"]')).toHaveCSS('flex-direction', 'column');
    
    // Navigation should be mobile-friendly
    await expect(page.locator('[data-testid="mobile-nav-toggle"]')).toBeVisible();
  });

  test('should handle real-time data updates', async ({ page }) => {
    // Simulate real-time update
    await page.evaluate(() => {
      // Trigger WebSocket or polling update
      window.dispatchEvent(new CustomEvent('dashboard-update', {
        detail: { totalTransactions: 1260 }
      }));
    });
    
    // Should update metrics without full page refresh
    await expect(page.locator('[data-testid="transactions-processed"]')).toContainText('1.3K');
  });

  test('should export dashboard summary', async ({ page }) => {
    // Click export button
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="export-dashboard"]');
    
    // Should show export options
    await expect(page.locator('[data-testid="export-options"]')).toBeVisible();
    
    // Select PDF export
    await page.click('[data-testid="export-pdf"]');
    
    // Should trigger download
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toMatch(/dashboard-summary.*\.pdf$/);
  });

  test('should show tooltips for metrics', async ({ page }) => {
    // Hover over metric to show tooltip
    await page.hover('[data-testid="net-income-info"]');
    
    // Should show explanation tooltip
    await expect(page.locator('[data-testid="tooltip"]')).toBeVisible();
    await expect(page.locator('text=Total Income minus Total Expenses')).toBeVisible();
  });

  test('should navigate between different dashboard views', async ({ page }) => {
    // Switch to performance view
    await page.click('[data-testid="performance-view"]');
    await expect(page.locator('[data-testid="performance-dashboard"]')).toBeVisible();
    
    // Switch to overview view
    await page.click('[data-testid="overview-view"]');
    await expect(page.locator('[data-testid="dashboard-metrics-grid"]')).toBeVisible();
    
    // Switch to analytics view
    await page.click('[data-testid="analytics-view"]');
    await expect(page.locator('[data-testid="analytics-dashboard"]')).toBeVisible();
  });

  test('should handle dashboard customization', async ({ page }) => {
    // Open customization panel
    await page.click('[data-testid="customize-dashboard"]');
    
    // Should show customization options
    await expect(page.locator('[data-testid="customization-panel"]')).toBeVisible();
    
    // Toggle widget visibility
    await page.uncheck('[data-testid="show-category-chart"]');
    
    // Save customization
    await page.click('[data-testid="save-customization"]');
    
    // Category chart should be hidden
    await expect(page.locator('[data-testid="category-breakdown-chart"]')).not.toBeVisible();
  });

  test('should show keyboard shortcuts help', async ({ page }) => {
    // Press help shortcut
    await page.keyboard.press('?');
    
    // Should show shortcuts modal
    await expect(page.locator('[data-testid="keyboard-shortcuts"]')).toBeVisible();
    await expect(page.locator('text=Keyboard Shortcuts')).toBeVisible();
    
    // Should list available shortcuts
    await expect(page.locator('text=R - Refresh dashboard')).toBeVisible();
    await expect(page.locator('text=A - Open AI assistant')).toBeVisible();
    
    // Close with Escape
    await page.keyboard.press('Escape');
    await expect(page.locator('[data-testid="keyboard-shortcuts"]')).not.toBeVisible();
  });
});