/**
 * E2E Authentication Tests
 * End-to-end testing of complete authentication workflows
 */

import { test, expect } from '@playwright/test';

// Test data
const TEST_USER = {
  email: '<EMAIL>',
  password: 'GikiTest2025Secure',
  fullName: 'Test Owner',
  organization: 'Test Company',
};

const INVALID_USER = {
  email: '<EMAIL>',
  password: 'wrongpassword',
};

test.describe('Authentication E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Start fresh for each test
    await page.context().clearCookies();
    // Safely clear localStorage with error handling
    await page.evaluate(() => {
      try {
        localStorage.clear();
      } catch (e) {
        console.warn('Could not clear localStorage:', e);
      }
    });
  });

  test('should complete full login flow and redirect to dashboard', async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    
    // Verify login page loads correctly
    await expect(page.locator('h1')).toContainText('Turn Messy Statements');
    await expect(page.locator('h2')).toContainText('Welcome Back');
    await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="password-input"]')).toBeVisible();
    
    // Fill in login credentials
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    
    // Submit login form
    await page.click('[data-testid="login-submit"]');
    
    // Should redirect to upload page (our app's default after login)
    await expect(page).toHaveURL('/upload');
    
    // Verify dashboard content loads
    await expect(page.locator('[data-testid="welcome-message"]')).toContainText('Welcome back');
    await expect(page.locator('[data-testid="dashboard-metrics-grid"]')).toBeVisible();
    
    // Verify user info is displayed
    await expect(page.locator('[data-testid="user-name"]')).toContainText(TEST_USER.fullName);
    
    // Verify navigation is available
    await expect(page.locator('[data-testid="main-navigation"]')).toBeVisible();
    await expect(page.locator('a[href="/transactions"]')).toBeVisible();
    await expect(page.locator('a[href="/upload"]')).toBeVisible();
  });

  test('should handle invalid login credentials', async ({ page }) => {
    await page.goto('/login');
    
    // Fill in invalid credentials
    await page.fill('[data-testid="email-input"]', INVALID_USER.email);
    await page.fill('[data-testid="password-input"]', INVALID_USER.password);
    
    // Submit form
    await page.click('[data-testid="login-submit"]');
    
    // Should show error message
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Invalid email or password');
    
    // Should remain on login page
    await expect(page).toHaveURL('/login');
    await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
  });

  test('should validate required fields', async ({ page }) => {
    await page.goto('/login');
    
    // Try to submit without filling fields
    await page.click('[data-testid="login-submit"]');
    
    // Should show validation errors
    await expect(page.locator('[data-testid="email-error"]')).toContainText('Email is required');
    await expect(page.locator('[data-testid="password-error"]')).toContainText('Password is required');
    
    // Fill only email
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.click('[data-testid="login-submit"]');
    
    // Should still show password error
    await expect(page.locator('[data-testid="password-error"]')).toContainText('Password is required');
  });

  test('should validate email format', async ({ page }) => {
    await page.goto('/login');
    
    // Enter invalid email format
    await page.fill('[data-testid="email-input"]', 'invalid-email');
    await page.fill('[data-testid="password-input"]', 'somepassword');
    await page.click('[data-testid="login-submit"]');
    
    // Should show email format error
    await expect(page.locator('[data-testid="email-error"]')).toContainText('Please enter a valid email');
  });

  test('should remember login state across page refreshes', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-submit"]');
    
    // Wait for dashboard
    await expect(page).toHaveURL('/dashboard');
    
    // Refresh the page
    await page.reload();
    
    // Should still be authenticated and on dashboard
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="welcome-message"]')).toBeVisible();
  });

  test('should handle remember me functionality', async ({ page }) => {
    await page.goto('/login');
    
    // Check remember me checkbox
    await page.check('[data-testid="remember-me"]');
    
    // Login
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-submit"]');
    
    // Verify remember me preference is stored
    const rememberMe = await page.evaluate(() => {
      try {
        return localStorage.getItem('remember_me');
      } catch (e) {
        console.warn('Could not access localStorage:', e);
        return null;
      }
    });
    expect(rememberMe).toBe('true');
    
    // Close browser context and create new one to simulate browser restart
    await page.context().close();
    const newContext = await page.context().browser()?.newContext();
    const newPage = newContext ? await newContext.newPage() : page;
    
    // Navigate to app - should remember login
    await newPage.goto('/');
    
    // Should redirect to dashboard without login prompt
    await expect(newPage).toHaveURL('/dashboard');
  });

  test('should complete logout flow', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-submit"]');
    await expect(page).toHaveURL('/dashboard');
    
    // Open user menu
    await page.click('[data-testid="user-menu-trigger"]');
    
    // Click logout
    await page.click('[data-testid="logout-button"]');
    
    // Should redirect to login page
    await expect(page).toHaveURL('/login');
    
    // Should clear authentication tokens
    const authToken = await page.evaluate(() => {
      try {
        return localStorage.getItem('auth_token');
      } catch (e) {
        console.warn('Could not access localStorage:', e);
        return null;
      }
    });
    expect(authToken).toBeNull();
    
    // Trying to access protected page should redirect to login
    await page.goto('/dashboard');
    await expect(page).toHaveURL('/login');
  });

  test('should redirect unauthenticated users to login', async ({ page }) => {
    // Try to access protected pages without authentication
    const protectedRoutes = ['/dashboard', '/transactions', '/upload', '/reports'];
    
    for (const route of protectedRoutes) {
      await page.goto(route);
      await expect(page).toHaveURL('/login');
    }
  });

  test('should redirect authenticated users away from login page', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-submit"]');
    await expect(page).toHaveURL('/dashboard');
    
    // Try to access login page again
    await page.goto('/login');
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
  });

  test('should handle network errors gracefully', async ({ page }) => {
    // Intercept auth API and simulate network error
    await page.route('/api/v1/auth/token', route => {
      route.abort('failed');
    });
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-submit"]');
    
    // Should show network error
    await expect(page.locator('[data-testid="error-message"]')).toContainText(/network error|connection failed/i);
  });

  test('should handle session timeout', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-submit"]');
    await expect(page).toHaveURL('/dashboard');
    
    // Simulate expired token by intercepting API calls
    await page.route('/api/v1/**', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ detail: 'Token expired' }),
      });
    });
    
    // Try to navigate to another page
    await page.click('a[href="/transactions"]');
    
    // Should redirect to login due to expired session
    await expect(page).toHaveURL('/login');
    await expect(page.locator('[data-testid="error-message"]')).toContainText(/session expired|please login again/i);
  });

  test('should support keyboard navigation', async ({ page }) => {
    await page.goto('/login');
    
    // Tab through form elements
    await page.keyboard.press('Tab'); // Focus email field
    await expect(page.locator('[data-testid="email-input"]')).toBeFocused();
    
    await page.keyboard.press('Tab'); // Focus password field
    await expect(page.locator('[data-testid="password-input"]')).toBeFocused();
    
    await page.keyboard.press('Tab'); // Focus remember me checkbox
    await expect(page.locator('[data-testid="remember-me"]')).toBeFocused();
    
    await page.keyboard.press('Tab'); // Focus submit button
    await expect(page.locator('[data-testid="login-submit"]')).toBeFocused();
    
    // Fill form using keyboard
    await page.focus('[data-testid="email-input"]');
    await page.keyboard.type(TEST_USER.email);
    
    await page.keyboard.press('Tab');
    await page.keyboard.type(TEST_USER.password);
    
    // Submit using Enter key
    await page.keyboard.press('Enter');
    
    // Should login successfully
    await expect(page).toHaveURL('/dashboard');
  });

  test('should prevent concurrent login attempts', async ({ page }) => {
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    
    // Click submit multiple times quickly
    const submitButton = page.locator('[data-testid="login-submit"]');
    await Promise.all([
      submitButton.click(),
      submitButton.click(),
      submitButton.click(),
    ]);
    
    // Should disable button during submission
    await expect(submitButton).toBeDisabled();
    
    // Should only make one login request and succeed
    await expect(page).toHaveURL('/dashboard');
  });

  test('should handle password visibility toggle', async ({ page }) => {
    await page.goto('/login');
    
    const passwordInput = page.locator('[data-testid="password-input"]');
    const toggleButton = page.locator('[data-testid="password-toggle"]');
    
    // Initially password should be hidden
    await expect(passwordInput).toHaveAttribute('type', 'password');
    
    // Click toggle to show password
    await toggleButton.click();
    await expect(passwordInput).toHaveAttribute('type', 'text');
    
    // Click again to hide password
    await toggleButton.click();
    await expect(passwordInput).toHaveAttribute('type', 'password');
  });
});