#!/bin/bash

# Unified Code Index Generator
# Orchestrates dedicated Python and TypeScript analyzers

set -e

WORKSPACE_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
OUTPUT_FILE="$WORKSPACE_ROOT/.claude/memory/code_index.md"
TEMP_DIR="/tmp/code_index_$$"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔍 Generating unified code index...${NC}"

# Create temp directory
mkdir -p "$TEMP_DIR"

# Ensure we're in the workspace
cd "$WORKSPACE_ROOT"

# Run Python analyzer (use direct python for speed)
echo -e "${YELLOW}🐍 Running Python analyzer...${NC}"
if python3 scripts/analyze_python.py > "$TEMP_DIR/python_analysis.md"; then
    echo -e "${GREEN}✅ Python analysis complete${NC}"
    python_files=$(jq -r '.total_files' .tmp_python_analysis.json 2>/dev/null || echo "0")
else
    echo -e "${RED}❌ Python analysis failed${NC}"
    python_files=0
    echo "## Backend (FastAPI Python)" > "$TEMP_DIR/python_analysis.md"
    echo "**Python analysis failed**" >> "$TEMP_DIR/python_analysis.md"
fi

# Run TypeScript analyzer (use direct tsx for speed)
echo -e "${YELLOW}📘 Running TypeScript analyzer...${NC}"
if npx tsx scripts/analyze_typescript.ts > "$TEMP_DIR/typescript_analysis.md"; then
    echo -e "${GREEN}✅ TypeScript analysis complete${NC}"
    typescript_files=$(jq -r '.total_files' .tmp_typescript_analysis.json 2>/dev/null || echo "0")
else
    echo -e "${RED}❌ TypeScript analysis failed${NC}"
    typescript_files=0
    echo "## Frontend (React TypeScript)" > "$TEMP_DIR/typescript_analysis.md"
    echo "**TypeScript analysis failed**" >> "$TEMP_DIR/typescript_analysis.md"
fi

# Calculate totals
total_files=$((python_files + typescript_files))

# Generate unified markdown
echo -e "${BLUE}📝 Generating unified index...${NC}"

cat > "$OUTPUT_FILE" << EOF
# Codebase Index

This index shows the key source files and their main exports/definitions with usage analysis.

## Legend
- ✅ **ACTIVE**: Actively used code
- ⚠️ **UNUSED**: Likely unused code (review for removal)
- 🔄 **PARTIAL**: Partially used code (some functions/methods unused)
- ❌ **ORPHANED**: No incoming references found (high confidence unused)
- 🔀 **CONSOLIDATION**: Old/unified pattern detected

## Summary
- **Python files**: $python_files
- **TypeScript files**: $typescript_files  
- **Total analyzed**: $total_files

EOF

# Add Python analysis
if [[ -f "$TEMP_DIR/python_analysis.md" ]]; then
    cat "$TEMP_DIR/python_analysis.md" >> "$OUTPUT_FILE"
fi

# Add TypeScript analysis
if [[ -f "$TEMP_DIR/typescript_analysis.md" ]]; then
    cat "$TEMP_DIR/typescript_analysis.md" >> "$OUTPUT_FILE"
fi

# Add footer
cat >> "$OUTPUT_FILE" << EOF

---

**Generated**: $(date)
**Method**: Dedicated Python AST + TypeScript Compiler API analyzers
**Files analyzed**: $total_files ($python_files Python, $typescript_files TypeScript)

EOF

# Cleanup temp files
rm -rf "$TEMP_DIR"
rm -f .tmp_python_analysis.json .tmp_typescript_analysis.json

echo -e "${GREEN}✅ Unified code index generated at:${NC} $OUTPUT_FILE"
echo -e "${GREEN}📊 Total files analyzed:${NC} $total_files"
echo -e "${GREEN}   🐍 Python files:${NC} $python_files"  
echo -e "${GREEN}   📘 TypeScript files:${NC} $typescript_files"

# Optional: Show summary stats
if command -v jq &> /dev/null && [[ -f ".tmp_python_analysis.json" ]] && [[ -f ".tmp_typescript_analysis.json" ]]; then
    echo -e "${BLUE}📈 Quick stats available via JSON outputs${NC}"
fi