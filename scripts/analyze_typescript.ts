#!/usr/bin/env tsx

// TypeScript Code Analyzer using standard tools
// Uses ts-unused-exports for professional unused code detection

import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';

// Configuration
const WORKSPACE_ROOT = process.cwd();
const SRC_DIR = path.join(WORKSPACE_ROOT, 'apps/giki-ai-app/src');

// Usage indicators
const USAGE_INDICATORS = {
    'ACTIVE': '✅',
    'UNUSED': '⚠️', 
    'PARTIAL': '🔄',
    'ORPHANED': '❌',
    'CONSOLIDATION': '🔀'
};

interface UnusedExport {
    file: string;
    exports: string[];
}

function runTsUnusedExports(): UnusedExport[] {
    try {
        const output = execSync('cd apps/giki-ai-app && npx --yes ts-unused-exports tsconfig.json', {
            encoding: 'utf8',
            stdio: 'pipe'
        });
        
        // Parse the output to extract unused exports
        const lines = output.split('\n');
        const unusedExports: UnusedExport[] = [];
        
        for (const line of lines) {
            if (line.includes(' modules with unused exports')) continue;
            if (!line.includes(':')) continue;
            
            const [filePath, exportsStr] = line.split(': ');
            if (filePath && exportsStr) {
                const relativePath = filePath.replace('/Users/<USER>/giki-ai-workspace/', '');
                const exports = exportsStr.split(', ').map(e => e.trim()).filter(e => e.length > 0);
                
                unusedExports.push({
                    file: relativePath,
                    exports
                });
            }
        }
        
        return unusedExports;
    } catch (error) {
        console.error('Failed to run ts-unused-exports:', error.message);
        return [];
    }
}

function findTypeScriptFiles(dir: string): string[] {
    const files: string[] = [];
    
    function traverse(currentDir: string) {
        if (!fs.existsSync(currentDir)) return;
        
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                traverse(fullPath);
            } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
                files.push(fullPath);
            }
        }
    }
    
    traverse(dir);
    return files;
}

function analyzeFile(filePath: string, unusedExports: UnusedExport[]): any {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const relativePath = path.relative(WORKSPACE_ROOT, filePath);
        
        // Find if this file has unused exports
        const unused = unusedExports.find(u => u.file === relativePath);
        
        // Extract basic info
        const lines = content.split('\n');
        const exports = extractExports(content);
        const imports = extractImports(content);
        
        const isTestFile = filePath.includes('.test.') || filePath.includes('.spec.') || filePath.includes('/test/');
        const hasReactComponent = /export\s+(default\s+)?function\s+[A-Z]/.test(content) || 
                                 /export\s+(const|let)\s+[A-Z]/.test(content);
        
        return {
            exports,
            imports,
            hasReactComponent,
            isTestFile,
            lineCount: lines.length,
            unusedExports: unused ? unused.exports : [],
            hasUnusedExports: !!unused
        };
    } catch (error) {
        return {
            exports: [],
            imports: [],
            hasReactComponent: false,
            isTestFile: false,
            lineCount: 0,
            unusedExports: [],
            hasUnusedExports: false,
            error: error.message
        };
    }
}

function extractExports(content: string): string[] {
    const exports: string[] = [];
    
    // Match various export patterns
    const patterns = [
        /export\s+(?:default\s+)?(?:function|class|interface|type|const|let|var)\s+(\w+)/g,
        /export\s*\{\s*([^}]+)\s*\}/g,
        /export\s+default\s+(\w+)/g
    ];
    
    for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(content)) !== null) {
            if (match[1].includes(',')) {
                // Handle named exports like { foo, bar }
                const names = match[1].split(',').map(n => n.trim().split(' as ')[0]);
                exports.push(...names);
            } else {
                exports.push(match[1]);
            }
        }
    }
    
    return [...new Set(exports.filter(e => e && e !== 'default'))];
}

function extractImports(content: string): string[] {
    const imports: string[] = [];
    const importPattern = /import\s+(?:(?:\{[^}]+\}|\w+)(?:\s*,\s*(?:\{[^}]+\}|\w+))*|\*\s+as\s+\w+)\s+from\s+['"]([^'"]+)['"]/g;
    
    let match;
    while ((match = importPattern.exec(content)) !== null) {
        imports.push(match[1]);
    }
    
    return [...new Set(imports)];
}

function getFileUsageIndicator(fileInfo: any): string {
    const { isTestFile, hasUnusedExports, exports, imports, hasReactComponent } = fileInfo;
    
    if (isTestFile) return USAGE_INDICATORS.PARTIAL;
    
    // If file has unused exports, mark as potentially unused
    if (hasUnusedExports && exports.length > 0) {
        // If ALL exports are unused, mark as unused
        if (fileInfo.unusedExports.length === exports.length) {
            return USAGE_INDICATORS.UNUSED;
        } else {
            return USAGE_INDICATORS.PARTIAL; // Some exports unused
        }
    }
    
    if (exports.length === 0 && imports.length === 0) return USAGE_INDICATORS.ORPHANED;
    if (exports.length > 0 || hasReactComponent) return USAGE_INDICATORS.ACTIVE;
    
    return USAGE_INDICATORS.UNUSED;
}

function generateAnalysis(): string {
    console.error('🔍 Analyzing TypeScript files with ts-unused-exports...');
    
    // Get unused exports from standard tool
    const unusedExports = runTsUnusedExports();
    console.error(`Found ${unusedExports.length} files with unused exports`);
    
    const files = findTypeScriptFiles(SRC_DIR);
    const analysisResults: { [key: string]: any } = {};
    
    for (const file of files) {
        const relativePath = path.relative(WORKSPACE_ROOT, file);
        const analysis = analyzeFile(file, unusedExports);
        analysisResults[relativePath] = analysis;
    }
    
    // Group by directory structure
    const groupedResults: { [key: string]: Array<{ path: string; analysis: any }> } = {};
    
    for (const [filePath, analysis] of Object.entries(analysisResults)) {
        const dir = path.dirname(filePath);
        if (!groupedResults[dir]) {
            groupedResults[dir] = [];
        }
        groupedResults[dir].push({ path: filePath, analysis });
    }
    
    // Generate markdown output
    let output = `## Frontend (React TypeScript)\n\n`;
    output += `**Total TypeScript files analyzed**: ${files.length}\n`;
    output += `**Files with unused exports**: ${unusedExports.length}\n\n`;
    
    // Sort directories
    const sortedDirs = Object.keys(groupedResults).sort();
    
    for (const dir of sortedDirs) {
        const dirFiles = groupedResults[dir];
        output += `### ${dir}/\n`;
        
        // Sort files within directory
        dirFiles.sort((a, b) => path.basename(a.path).localeCompare(path.basename(b.path)));
        
        for (const { path: filePath, analysis } of dirFiles) {
            const fileName = path.basename(filePath);
            const indicator = getFileUsageIndicator(analysis);
            
            let definitions = analysis.exports.slice(0, 5).join(', '); // Limit to first 5
            const moreCount = analysis.exports.length > 5 ? ` +${analysis.exports.length - 5} more` : '';
            
            // Add unused exports info if present
            if (analysis.hasUnusedExports && analysis.unusedExports.length > 0) {
                const unusedCount = analysis.unusedExports.length;
                definitions += ` (${unusedCount} unused)`;
            }
            
            if (analysis.error) {
                output += `- \`${fileName}\`: *Analysis failed* ❌\n`;
            } else {
                output += `- \`${fileName}\`: ${definitions}${moreCount} ${indicator}\n`;
            }
        }
        output += '\n';
    }
    
    // Summary statistics
    const totalFiles = Object.keys(analysisResults).length;
    const activeFiles = Object.values(analysisResults).filter(a => getFileUsageIndicator(a) === USAGE_INDICATORS.ACTIVE).length;
    const orphanedFiles = Object.values(analysisResults).filter(a => getFileUsageIndicator(a) === USAGE_INDICATORS.ORPHANED).length;
    const unusedFiles = Object.values(analysisResults).filter(a => getFileUsageIndicator(a) === USAGE_INDICATORS.UNUSED).length;
    const partialFiles = Object.values(analysisResults).filter(a => getFileUsageIndicator(a) === USAGE_INDICATORS.PARTIAL).length;
    
    output += `### Summary\n`;
    output += `- **Total files**: ${totalFiles}\n`;
    output += `- **Active files**: ${activeFiles}\n`;
    output += `- **Files with unused exports**: ${unusedExports.length}\n`;
    output += `- **Potentially unused files**: ${unusedFiles}\n`;
    output += `- **Partial usage files**: ${partialFiles}\n`;
    output += `- **Orphaned files**: ${orphanedFiles}\n\n`;
    
    return output;
}

// Main execution
function main() {
    try {
        const analysis = generateAnalysis();
        console.log(analysis);
        
        // Also output JSON for other tools
        const files = findTypeScriptFiles(SRC_DIR);
        const unusedExports = runTsUnusedExports();
        
        const jsonOutput = {
            total_files: files.length,
            unused_exports_files: unusedExports.length,
            unused_exports: unusedExports
        };
        
        fs.writeFileSync('.tmp_typescript_analysis.json', JSON.stringify(jsonOutput, null, 2));
        
        return 0;
    } catch (error) {
        console.error('Error analyzing TypeScript files:', error.message);
        return 1;
    }
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
    process.exit(main());
}

export { analyzeFile, findTypeScriptFiles, generateAnalysis, main };