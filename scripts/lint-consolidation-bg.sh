#!/bin/bash
# Background consolidation linting script for giki.ai API
# Runs custom consolidation linting rules to enforce unified architecture patterns

set -e

# Script configuration
SCRIPT_NAME="Consolidation Linting"
LOG_FILE="logs/lint-consolidation.log"
API_DIR="apps/giki-ai-api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create logs directory if it doesn't exist
mkdir -p logs

echo -e "${BLUE}🔍 Starting ${SCRIPT_NAME}...${NC}"

# Change to API directory
cd "$API_DIR"

# Clear previous log
> "../../$LOG_FILE"

# Log start
echo "$(date): Starting consolidation linting..." > "../../$LOG_FILE"

# Function to run consolidation linting
run_consolidation_lint() {
    echo -e "${BLUE}Running consolidation architecture linting...${NC}"
    echo "$(date): Running consolidation linting..." >> "../../$LOG_FILE"
    
    # Run the custom consolidation linter
    if python scripts/lint_consolidation.py src/ >> "../../$LOG_FILE" 2>&1; then
        echo -e "${GREEN}✅ Consolidation linting passed${NC}"
        echo "$(date): Consolidation linting passed" >> "../../$LOG_FILE"
    else
        echo -e "${RED}❌ Consolidation linting failed${NC}"
        echo "$(date): Consolidation linting failed" >> "../../$LOG_FILE"
        
        # Show recent violations
        echo -e "${YELLOW}Recent violations:${NC}"
        tail -20 "../../$LOG_FILE" | grep -E "(❌|⚠️|ERROR|WARNING)" || echo "Check log for details"
        
        return 1
    fi
}

# Function to run standard ruff linting for comparison
run_standard_lint() {
    echo -e "${BLUE}Running standard ruff linting...${NC}"
    echo "$(date): Running standard ruff linting..." >> "../../$LOG_FILE"
    
    if uv run ruff check src/ --output-format=text >> "../../$LOG_FILE" 2>&1; then
        echo -e "${GREEN}✅ Standard linting passed${NC}"
        echo "$(date): Standard linting passed" >> "../../$LOG_FILE"
    else
        echo -e "${YELLOW}⚠️ Standard linting found issues${NC}"
        echo "$(date): Standard linting found issues" >> "../../$LOG_FILE"
        
        # Don't fail for standard linting issues in background mode
        return 0
    fi
}

# Function to check architectural patterns
check_architecture_patterns() {
    echo -e "${BLUE}Checking architectural patterns...${NC}"
    echo "$(date): Checking architectural patterns..." >> "../../$LOG_FILE"
    
    violations=0
    
    # Check for unified router usage
    echo "Checking unified router patterns..." >> "../../$LOG_FILE"
    if grep -r "router = APIRouter(" src/ --include="*.py" >> "../../$LOG_FILE" 2>&1; then
        echo -e "${RED}❌ Found direct APIRouter usage - should use UnifiedBaseRouter${NC}"
        echo "$(date): Found direct APIRouter usage" >> "../../$LOG_FILE"
        violations=$((violations + 1))
    fi
    
    # Check RouterFactory usage in main.py
    echo "Checking RouterFactory usage..." >> "../../$LOG_FILE"
    if ! grep -q "RouterFactory" src/giki_ai_api/core/main.py; then
        echo -e "${RED}❌ main.py should use RouterFactory${NC}"
        echo "$(date): main.py missing RouterFactory usage" >> "../../$LOG_FILE"
        violations=$((violations + 1))
    fi
    
    # Check for unified base router imports
    echo "Checking UnifiedBaseRouter imports..." >> "../../$LOG_FILE"
    unified_routers=$(find src/ -name "*unified*router*.py" | wc -l)
    if [ $unified_routers -lt 8 ]; then
        echo -e "${YELLOW}⚠️ Expected at least 8 unified routers, found $unified_routers${NC}"
        echo "$(date): Only $unified_routers unified routers found" >> "../../$LOG_FILE"
    else
        echo -e "${GREEN}✅ Found $unified_routers unified routers${NC}"
        echo "$(date): Found $unified_routers unified routers" >> "../../$LOG_FILE"
    fi
    
    # Summary
    if [ $violations -eq 0 ]; then
        echo -e "${GREEN}✅ Architecture patterns check passed${NC}"
        echo "$(date): Architecture patterns check passed" >> "../../$LOG_FILE"
        return 0
    else
        echo -e "${RED}❌ Found $violations architecture violations${NC}"
        echo "$(date): Found $violations architecture violations" >> "../../$LOG_FILE"
        return 1
    fi
}

# Function to generate consolidation report
generate_report() {
    echo -e "${BLUE}Generating consolidation report...${NC}"
    echo "$(date): Generating consolidation report..." >> "../../$LOG_FILE"
    
    # Count unified vs legacy patterns
    unified_routers=$(find src/ -name "*unified*router*.py" | wc -l)
    legacy_routers=$(find src/ -name "router.py" -not -path "*/unified*" | wc -l)
    total_routers=$((unified_routers + legacy_routers))
    
    if [ $total_routers -gt 0 ]; then
        progress=$((unified_routers * 100 / total_routers))
    else
        progress=0
    fi
    
    echo "" >> "../../$LOG_FILE"
    echo "=== CONSOLIDATION REPORT ===" >> "../../$LOG_FILE"
    echo "Unified Routers: $unified_routers" >> "../../$LOG_FILE"
    echo "Legacy Routers: $legacy_routers" >> "../../$LOG_FILE"
    echo "Migration Progress: $progress%" >> "../../$LOG_FILE"
    echo "=========================" >> "../../$LOG_FILE"
    
    echo -e "${BLUE}📊 Consolidation Status:${NC}"
    echo -e "  Unified Routers: ${GREEN}$unified_routers${NC}"
    echo -e "  Legacy Routers: ${YELLOW}$legacy_routers${NC}"
    echo -e "  Migration Progress: ${GREEN}$progress%${NC}"
}

# Main execution
main() {
    local exit_code=0
    
    # Run consolidation linting
    if ! run_consolidation_lint; then
        exit_code=1
    fi
    
    # Run standard linting (non-blocking)
    run_standard_lint
    
    # Check architecture patterns
    if ! check_architecture_patterns; then
        exit_code=1
    fi
    
    # Generate report
    generate_report
    
    # Final status
    echo "$(date): Consolidation linting completed with exit code $exit_code" >> "../../$LOG_FILE"
    
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}✅ All consolidation checks passed!${NC}"
        echo -e "${BLUE}📄 Full log: $LOG_FILE${NC}"
    else
        echo -e "${RED}❌ Consolidation checks failed${NC}"
        echo -e "${BLUE}📄 Check log for details: $LOG_FILE${NC}"
        
        # Show summary of violations
        echo -e "${YELLOW}Recent violations:${NC}"
        tail -30 "../../$LOG_FILE" | grep -E "(❌|⚠️|ERROR|WARNING|VIOLATION)" | head -10 || echo "Check log for full details"
    fi
    
    return $exit_code
}

# Trap errors
trap 'echo -e "${RED}❌ Consolidation linting script failed${NC}"; exit 1' ERR

# Run main function
main "$@"