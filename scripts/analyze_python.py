#!/usr/bin/env python3
"""
Python Code Analyzer using standard tools
Uses vulture for professional unused code detection
"""

import os
import subprocess
import json
from collections import defaultdict
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

# Usage indicators
USAGE_INDICATORS = {
    'ACTIVE': '✅',
    'UNUSED': '⚠️', 
    'PARTIAL': '🔄',
    'ORPHANED': '❌',
    'CONSOLIDATION': '🔀'
}

class PythonAnalyzer:
    def __init__(self, workspace_root: str):
        self.workspace_root = Path(workspace_root)
        
    def run_vulture(self) -> List[Dict]:
        """Run vulture to find unused code"""
        try:
            cmd = [
                'vulture', 
                str(self.workspace_root / 'apps/giki-ai-api/src/giki_ai_api/'),
                '--min-confidence', '80'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            vulture_issues = []
            if result.stderr:  # vulture outputs to stderr
                lines = result.stderr.strip().split('\n')
                for line in lines:
                    if ':' in line and '(' in line:
                        # Parse vulture output: file:line: message (confidence%)
                        parts = line.split(': ', 2)
                        if len(parts) >= 3:
                            file_line = parts[0]
                            message = parts[1]
                            confidence_part = parts[2] if len(parts) > 2 else ''
                            
                            # Extract confidence percentage
                            confidence = 0
                            if '(' in confidence_part and '%' in confidence_part:
                                try:
                                    conf_str = confidence_part.split('(')[1].split('%')[0]
                                    confidence = int(conf_str)
                                except:
                                    confidence = 80
                            
                            # Extract file path and line number
                            file_path = file_line.rsplit(':', 1)[0] if ':' in file_line else file_line
                            # Make it relative to workspace
                            if file_path.startswith('src/'):
                                file_path = f'apps/giki-ai-api/{file_path}'
                            
                            vulture_issues.append({
                                'file': file_path,
                                'message': message,
                                'confidence': confidence,
                                'line': file_line
                            })
            
            return vulture_issues
            
        except Exception as e:
            print(f"Error running vulture: {e}", file=os.sys.stderr)
            return []
    
    def find_python_files(self) -> List[Path]:
        """Find all Python files to analyze"""
        files = []
        
        # Only scan specific directories for speed
        scan_dirs = [
            self.workspace_root / "apps" / "giki-ai-api" / "src",
            self.workspace_root / "apps" / "giki-ai-api" / "scripts", 
            self.workspace_root / "scripts"
        ]
        
        for scan_dir in scan_dirs:
            if not scan_dir.exists():
                continue
                
            for root, dirs, filenames in os.walk(scan_dir):
                # Skip excluded directories
                dirs[:] = [d for d in dirs if not any([
                    'test' in d.lower(), '__pycache__' in d, '.git' in d, 
                    'node_modules' in d, 'migrations' in d, '.venv' in d,
                    'venv' in d, 'site-packages' in d, '.pytest_cache' in d
                ])]
                
                for filename in filenames:
                    if filename.endswith('.py'):
                        file_path = Path(root) / filename
                        # Skip test files and cache
                        path_str = str(file_path)
                        exclude_checks = [
                            'test' in path_str.lower(),
                            '__pycache__' in path_str,
                            '.pytest_cache' in path_str,
                            '.venv' in path_str,
                            'migrations/' in path_str,
                            path_str.endswith('.tmp'),
                        ]
                        
                        if not any(exclude_checks):
                            files.append(file_path)
        
        return sorted(files)
    
    def analyze_file_basic(self, file_path: Path) -> Dict:
        """Basic file analysis without AST parsing"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Simple pattern matching for exports/imports
            lines = content.split('\n')
            
            # Count imports
            import_count = len([line for line in lines if line.strip().startswith(('import ', 'from '))])
            
            # Count function/class definitions
            def_count = len([line for line in lines if line.strip().startswith(('def ', 'class '))])
            
            # Check for main execution
            has_main = '__name__ == "__main__"' in content or 'if __name__' in content
            
            return {
                'line_count': len(lines),
                'import_count': import_count,
                'definition_count': def_count,
                'has_main': has_main,
                'is_test': 'test' in str(file_path).lower()
            }
            
        except Exception as e:
            return {
                'line_count': 0,
                'import_count': 0,
                'definition_count': 0,
                'has_main': False,
                'is_test': False,
                'error': str(e)
            }
    
    def determine_usage_status(self, file_path: str, file_info: Dict, vulture_issues: List[Dict]) -> str:
        """Determine usage status based on vulture results"""
        # Check if this file has vulture issues
        file_issues = [issue for issue in vulture_issues if issue['file'] == file_path]
        
        if file_info.get('is_test'):
            return USAGE_INDICATORS['PARTIAL']
        
        if file_info.get('error'):
            return USAGE_INDICATORS['ORPHANED']
        
        # If vulture found unused code in this file
        if file_issues:
            # Count high-confidence issues
            high_confidence_issues = [i for i in file_issues if i['confidence'] >= 90]
            if high_confidence_issues:
                return USAGE_INDICATORS['UNUSED']
            else:
                return USAGE_INDICATORS['PARTIAL']
        
        # If file has definitions or imports, likely active
        if file_info.get('definition_count', 0) > 0 or file_info.get('import_count', 0) > 0:
            return USAGE_INDICATORS['ACTIVE']
        
        # Empty or minimal files
        if file_info.get('line_count', 0) < 5:
            return USAGE_INDICATORS['ORPHANED']
        
        return USAGE_INDICATORS['UNUSED']
    
    def analyze_files(self) -> Dict:
        """Analyze all Python files using vulture"""
        print("🔍 Analyzing Python files with vulture...", file=os.sys.stderr)
        
        # Get vulture results
        vulture_issues = self.run_vulture()
        print(f"Found {len(vulture_issues)} unused code issues", file=os.sys.stderr)
        
        files = self.find_python_files()
        results = {}
        
        for file_path in files:
            relative_path = str(file_path.relative_to(self.workspace_root))
            file_info = self.analyze_file_basic(file_path)
            file_info['usage_status'] = self.determine_usage_status(relative_path, file_info, vulture_issues)
            
            # Add vulture issues for this file
            file_issues = [issue for issue in vulture_issues if issue['file'] == relative_path]
            file_info['vulture_issues'] = file_issues
            file_info['unused_count'] = len(file_issues)
            
            results[relative_path] = file_info
        
        return results
    
    def generate_markdown(self, analysis_results: Dict) -> str:
        """Generate markdown output"""
        vulture_files_count = len([f for f in analysis_results.values() if f.get('unused_count', 0) > 0])
        
        output = "## Backend (FastAPI Python)\n\n"
        output += f"**Total Python files analyzed**: {len(analysis_results)}\n"
        output += f"**Files with unused code (vulture)**: {vulture_files_count}\n\n"
        
        # Group by directory
        grouped = defaultdict(list)
        for file_path, analysis in analysis_results.items():
            dir_path = str(Path(file_path).parent)
            grouped[dir_path].append((file_path, analysis))
        
        # Sort and output
        for directory in sorted(grouped.keys()):
            files = grouped[directory]
            output += f"### {directory}/\n"
            
            for file_path, analysis in sorted(files):
                filename = Path(file_path).name
                
                # Create description
                definitions_count = analysis.get('definition_count', 0)
                unused_count = analysis.get('unused_count', 0)
                
                if definitions_count > 0:
                    desc = f"{definitions_count} definitions"
                    if unused_count > 0:
                        desc += f" ({unused_count} unused)"
                elif unused_count > 0:
                    desc = f"{unused_count} unused items"
                elif analysis.get('import_count', 0) > 0:
                    desc = f"{analysis.get('import_count')} imports"
                else:
                    desc = ""
                
                status = analysis.get('usage_status', USAGE_INDICATORS['UNUSED'])
                
                if analysis.get('error'):
                    output += f"- `{filename}`: *Analysis failed* ❌\n"
                else:
                    output += f"- `{filename}`: {desc} {status}\n"
            
            output += "\n"
        
        # Summary
        total_files = len(analysis_results)
        active_files = sum(1 for a in analysis_results.values() 
                          if a.get('usage_status') == USAGE_INDICATORS['ACTIVE'])
        unused_files = sum(1 for a in analysis_results.values() 
                          if a.get('usage_status') == USAGE_INDICATORS['UNUSED'])
        orphaned_files = sum(1 for a in analysis_results.values() 
                            if a.get('usage_status') == USAGE_INDICATORS['ORPHANED'])
        
        output += "### Summary\n"
        output += f"- **Total files**: {total_files}\n"
        output += f"- **Active files**: {active_files}\n"
        output += f"- **Files with unused code**: {vulture_files_count}\n"
        output += f"- **Potentially unused files**: {unused_files}\n"
        output += f"- **Orphaned files**: {orphaned_files}\n\n"
        
        return output

def main():
    """Main execution"""
    workspace_root = os.getcwd()
    analyzer = PythonAnalyzer(workspace_root)
    
    try:
        results = analyzer.analyze_files()
        markdown = analyzer.generate_markdown(results)
        print(markdown)
        
        # Also output JSON for other tools
        vulture_issues = analyzer.run_vulture()
        json_output = {
            'total_files': len(results),
            'vulture_issues_count': len(vulture_issues),
            'vulture_issues': vulture_issues,
            'analysis_results': results
        }
        
        with open('.tmp_python_analysis.json', 'w') as f:
            json.dump(json_output, f, indent=2)
            
    except Exception as e:
        print(f"Error analyzing Python files: {e}", file=os.sys.stderr)
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())