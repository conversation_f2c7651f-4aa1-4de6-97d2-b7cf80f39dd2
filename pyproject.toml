[build-system]
requires = ["hatchling>=1.21.1"]
build-backend = "hatchling.build"

[project]
name = "giki-ai-workspace"
version = "0.1.0"
description = "Monorepo for Giki AI projects"
readme = "README.md"
requires-python = ">=3.12,<3.13" # Upgraded to Python 3.12
license = {text = "Proprietary"} # Corrected license
authors = [
    {name = "Giki AI", email = "<EMAIL>"}
]
dependencies = [
    "aiohttp>=3.12.13",
    "anthropic>=0.55.0",
    "argon2-cffi>=25.1.0",
    "asyncpg>=0.30.0",
    "chromadb>=1.0.13",
    "google-api-python-client>=2.169.0",
    "google-auth-httplib2>=0.2.0",
    "google-auth-oauthlib>=1.2.2",
    "google-cloud-aiplatform>=1.96.0",
    "google-adk>=1.3.0",
    "a2a-sdk>=0.2.2",
    "langchain>=0.3.26",
    "langgraph>=0.5.0",
    "numpy>=2.2.5",
    "openai>=1.93.0",
    "openpyxl>=3.1.5",
    "psutil>=7.0.0",
    "requests>=2.32.3",
    "sentence-transformers>=4.1.0",
    "sse-starlette>=2.3.6",
    "tiktoken>=0.9.0",
    "httpx>=0.28.1",
    "pyjwt>=2.10.1",
    "grpcio>=1.67.0",
    "jinja2>=3.1.6",
]

[tool.hatch.build.targets.wheel]
packages = ["apps", "packages"]  # Include both apps and packages directories

[project.optional-dependencies]
dev = [
    "pytest==8.2.2",
    "pytest-asyncio==0.23.7",
    "httpx", # Removed version pinning
    "ruff", # Kept lower bound
    "pytest-mock",
    "pytest-cov", # Added for coverage reporting
    "black",
]

# uv workspace configuration for managing multiple Python projects
[tool.uv.workspace]
# Define Python projects managed by uv workspace
members = [
    "apps/giki-ai-api",
    "apps/giki-ai-old-api", # Included for now, review if still needed
    "packages/giki-ai-auth",
    "packages/giki-ai-db",
    "packages/giki-mcp-lib",
    "packages/giki-mcp-lib/just-prompt",
]
[tool.uv.sources]
# Declare local packages as workspace sources for uv
giki-ai-db = { workspace = true }
giki-ai-auth = { workspace = true }

# Project descriptions
# These are solely for documentation purposes
# Each project should have its own proper pyproject.toml
[tool.workspace-info.giki-ai-mcp]
description = "MCP Server for Giki AI"
type = "mcp-server"
frontend = "n/a"

[tool.workspace-info.giki-ai-api]
description = "AI-powered transaction processing API"
type = "application"
frontend = "apps/giki-ai-app"

# Development tool configurations that apply workspace-wide
# Removed black, isort, pylint configs - using ruff defined in project files

[tool.pytest.ini_options]
markers = [
    "integration: marks tests as integration tests (require external services like Zoho)",
]
pythonpath = ["."]
log_cli = true
log_cli_level = "INFO"
log_file = "test_output/zoho_books_tests.log"
log_file_level = "DEBUG"
log_file_format = "%(asctime)s [%(levelname)8s] %(name)s: %(message)s (%(filename)s:%(lineno)s)"
log_file_date_format = "%Y-%m-%d %H:%M:%S"
