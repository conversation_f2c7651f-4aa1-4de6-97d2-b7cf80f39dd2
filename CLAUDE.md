# CLAUDE.md - Behavioral Enforcement & Never Give Up Development

## BEHAVIORAL ENFORCEMENT SYSTEM

### Essential Memory Imports (BEHAVIORAL PRIORITY)
@.claude/memory/behavioral-enforcement.md
@.claude/memory/development-patterns.md
@.claude/memory/testing-patterns.md

### Code Index Generation
!scripts/generate_code_index.sh

### Project Context (MIS-First Platform)
**giki.ai** - MIS-First Management Information System Platform  
**Tech Stack**: React + FastAPI + PostgreSQL + Vertex AI (pnpm monorepo)  
**Architecture**: Unified MIS with progressive enhancement capabilities

## NEVER GIVE UP DEVELOPMENT PHILOSOPHY (ABSOLUTE AUTHORITY)

### Core Anti-Fragmentation Principle
**NEVER CREATE MULTIPLE VERSIONS - ALWAYS MODIFY EXISTING FILES**  
Enhance existing files rather than creating simplified/alternate/fixed versions. This prevents workspace fragmentation and maintains single sources of truth across ALL file types.

### Abandonment Detection & Enforcement (AUTOMATIC TRIGGERS)
**CRITICAL VIOLATION PHRASES** - These trigger immediate enforcement:
- "instead of continuing" → **IMMEDIATE `/never-give-up` execution**
- "let me focus on what's working" → **MANDATORY todo creation + continue blocked work**
- "this is good enough for now" → **VIOLATION, create todo + continue until complete**
- "we'll fix this later" → **FORBIDDEN, create todo + address now or next item**
- "skip this for now" → **VIOLATION, mandatory todo creation required**

**COMPLEX PROBLEM TRIGGERS** - These require extended thinking:
- "complex" + "issue" → **MANDATORY `/debug-complex` command execution**
- "debugging" + "difficult" → **Extended thinking mode activation required**
- "auth flow" + "integration" → **Automatic detailed analysis requirement**

### Mandatory Todo-Driven Development (NO EXCEPTIONS)
```typescript
// ABSOLUTE RULE: Every session must follow this pattern
const developmentWorkflow = {
  onIssueFound: "Immediately create todo with TodoWrite tool",
  onBlocker: "Add todo, continue with next item, return to resolve", 
  onCompletion: "Only when todo list is empty or all items completed",
  onSessionEnd: "Update docs/00-TODOS-PERSISTENT.yaml for recovery"
};
```

**TODO CREATION REQUIREMENTS:**
- **EVERY ISSUE BECOMES A TODO**: The moment any problem, bug, improvement, or task is identified
- **NO MENTAL NOTES**: TodoWrite execution mandatory within 30 seconds of issue discovery
- **SPECIFIC DESCRIPTIONS**: Use format "[ACTION]: [SPECIFIC_TASK] - [CONTEXT_OR_REASON]"
- **SYSTEMATIC COMPLETION**: Continue development until ALL todos resolved to completion

### Session Recovery Protocol (MANDATORY EXECUTION)
**SESSION END REQUIREMENTS:**
1. Update all todo statuses in persistent yaml file
2. Document current work context for next session recovery
3. Record session achievements and discoveries made
4. Set clear recovery instructions for continuing work
5. Execute `/session-save` command for automatic state persistence

## WORKFLOW ENFORCEMENT CHECKPOINTS

### Quality Gates (BLOCKING PROGRESSION)
**Behavioral Quality Gates (NON-NEGOTIABLE):**
- **Todo Compliance**: All discovered issues documented in todo system
- **Work Completion**: Current work completed or properly transitioned to todo
- **Session Recovery**: Session state fully documented and recoverable
- **No Abandonment**: No work abandoned without proper todo creation

**Success Criteria (REQUIRED FOR COMPLETION):**
- **Todo List Empty**: No pending todos of any priority level
- **All Features Working**: End-to-end functionality verified  
- **No Known Issues**: Every identified problem has been resolved
- **Quality Standards Met**: All lint, test, and build processes passing

### Violation Response Protocol (AUTOMATIC ENFORCEMENT)
**WHEN VIOLATIONS DETECTED:**
1. **Immediate Stop** - Halt current work until violation resolved
2. **Execute Enforcement Command** - Run `/never-give-up` or `/debug-complex`
3. **Create Mandatory Todo** - Use TodoWrite for abandoned issue
4. **Document Context** - Record exactly what was being worked on
5. **Continue Systematically** - Complete current work or properly transition

## CRITICAL DEVELOPMENT PRINCIPLES

### Platform & Branch Strategy
**CRITICAL**: Always detect platform first to determine correct development branch
- **master** - macOS/Darwin development (primary)
- **poppy** - Pop!_OS/Linux development 
- **windy** - Windows development (future)

```bash
uname -a                    # Darwin = master, Linux = poppy, Windows = windy
git branch --show-current   # Verify current branch matches platform
```

### Zero Tolerance Policies
- **NO FAKE DATA**: NEVER create mock data, placeholder values, simulated responses
- **NO NX COMMANDS**: All development operations use direct pnpm scripts from workspace root
- **NO ABANDONMENT**: Never abandon, skip, or leave incomplete any development task
- **NO EXCEPTIONS**: Every discovered issue MUST become a tracked todo

### Essential Command Patterns (BACKGROUND-FIRST)
```bash
# Quality (timeout-resistant only)
pnpm lint:api:bg           # Background Python linting 
pnpm lint:app:bg           # Background frontend linting (never direct)

# Testing (auto-starts services)
pnpm test:visual           # Auto-starts all services, background execution
pnpm test:e2e              # Auto-starts all services, background execution
pnpm test:api:bg unit      # Background API unit tests

# Development (auto-service management)
pnpm serve:api             # Auto-starts PostgreSQL, Redis, API
pnpm db                    # Test database connection
pnpm logs                  # Monitor system status
```

### API Testing & Validation Patterns (NO MANUAL CURL COMMANDS)
**CRITICAL**: Use existing test infrastructure instead of manual curl commands to avoid approval workflows

```bash
# API Testing with Authentication
python scripts/auth-helper.py --test-transactions  # Test transaction endpoints
python scripts/auth-helper.py --test-auth          # Test authentication system
python scripts/auth-helper.py --get-token <email>  # Get auth tokens for testing

# Backend API Testing through pnpm scripts
pnpm test:api:bg unit                               # Run API unit tests in background
pnpm test:api:bg integration                        # Run API integration tests
pnpm test:api:bg --testNamePattern="transaction"    # Run specific API tests

# NEVER USE: Manual curl commands (require approval workflow)
# ❌ curl -X GET "http://localhost:8000/api/v1/transactions" 
# ✅ python scripts/auth-helper.py --test-transactions
```

**Why avoid curl commands:**
- Manual curl commands require manual approval in Claude Code
- Creates workflow bottlenecks during development
- Existing test infrastructure provides better error handling and authentication
- auth-helper.py automatically handles JWT tokens and proper headers

## ESSENTIAL DOCUMENTATION CONTEXT

### Project Information Sources (Numbered Docs Only)
- **Current Status**: docs/02-CURRENT-STATUS.md (test accounts, project state)
- **System Architecture**: docs/03-SYSTEM-ARCHITECTURE.md (MIS-first architecture)
- **API Reference**: docs/04-API-REFERENCE.md (endpoint specifications)
- **Customer Journeys**: docs/05-CUSTOMER-JOURNEYS.md (user workflows)
- **Design Standards**: docs/06-DESIGN-SYSTEM.md (brand colors, icons, layout)
- **Page Specifications**: docs/07-PAGE-SPECIFICATIONS.md (functional requirements)
- **Deployment Guide**: docs/08-DEPLOYMENT-GUIDE.md (production setup)
- **Testing Strategy**: docs/09-TESTING-STRATEGY.md (MIS-focused validation)
- **Todo Persistence**: docs/00-TODOS-PERSISTENT.yaml (session recovery)

**DOCUMENTATION RULE**: All project documentation MUST be within numbered docs/ structure - NO new docs outside this system

### MIS-First Product Architecture
**UNIFIED PRINCIPLE**: Every customer gets a complete MIS with hierarchical categorization and progressive enhancement opportunities, focused on business value through accuracy improvements.

---

**BEHAVIORAL ENFORCEMENT AUTHORITY**: These rules override all other development preferences and habits. Compliance is mandatory and non-negotiable.

**Updated**: 2025-07-13 | **Method**: Laser-focused behavioral enforcement system with specialized memory architecture

