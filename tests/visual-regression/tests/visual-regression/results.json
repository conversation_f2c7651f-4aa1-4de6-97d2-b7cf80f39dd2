{"config": {"configFile": "/Users/<USER>/giki-ai-workspace/tests/visual-regression/playwright.config.ts", "rootDir": "/Users/<USER>/giki-ai-workspace/tests/visual-regression/tests/visual-regression", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "tests/visual-regression/reports"}], ["json", {"outputFile": "tests/visual-regression/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/giki-ai-workspace/tests/visual-regression/tests/visual-regression/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium-desktop", "name": "chromium-desktop", "testDir": "/Users/<USER>/giki-ai-workspace/tests/visual-regression/tests/visual-regression", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/giki-ai-workspace/tests/visual-regression/tests/visual-regression/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium-tablet", "name": "chromium-tablet", "testDir": "/Users/<USER>/giki-ai-workspace/tests/visual-regression/tests/visual-regression", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/giki-ai-workspace/tests/visual-regression/tests/visual-regression/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium-mobile", "name": "chromium-mobile", "testDir": "/Users/<USER>/giki-ai-workspace/tests/visual-regression/tests/visual-regression", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.2", "workers": 4, "webServer": {"command": "pnpm serve:app", "url": "http://localhost:4200", "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [{"message": "Error: No tests found", "stack": "Error: No tests found"}], "stats": {"startTime": "2025-07-13T09:52:02.542Z", "duration": 25.114000000000033, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}