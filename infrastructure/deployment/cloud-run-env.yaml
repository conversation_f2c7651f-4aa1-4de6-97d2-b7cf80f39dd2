# Environment Configuration for Cloud Run Deployment
# Updated for Unified Configuration System

# Core Environment
GIKI_ENVIRONMENT: "production"
ENVIRONMENT: "production"
DEBUG: "false"

# API Configuration  
API_V1_STR: "/api/v1"
CORS_ALLOWED_ORIGINS: "http://localhost:4200,http://localhost:3000,https://giki-ai-frontend-************.us-central1.run.app,https://app-giki-ai.web.app,https://rezolve-poc.web.app"

# Google Cloud Platform
GOOGLE_CLOUD_PROJECT: "rezolve-poc"
VERTEX_PROJECT_ID: "rezolve-poc"
VERTEX_LOCATION: "us-central1"
GCP_LOCATION: "us-central1"
GOOGLE_APPLICATION_CREDENTIALS: "/secrets/service-account/key.json"

# Vertex AI Configuration
VERTEX_AI_LOCATION: "us-central1"
VERTEX_AI_CATEGORIZATION_MODEL: "gemini-2.0-flash-001"
VERTEX_AI_CHAT_MODEL: "gemini-2.0-flash-001"
VERTEX_AI_FILE_MODEL: "gemini-2.0-flash-001"
VERTEX_AI_REASONING_MODEL: "gemini-2.0-flash-001"
VERTEX_AI_GEMINI_MODEL_ID: "gemini-2.0-flash-001"

# Performance & Features
FEATURE_ENABLE_PERFORMANCE_MONITORING: "true"
FEATURE_ENABLE_REAL_TIME_SYNC: "true"
FEATURE_ENABLE_ADVANCED_REPORTING: "true"
METRICS_ENABLED: "true"

# Security & JWT
ALGORITHM: "RS256"
ACCESS_TOKEN_EXPIRE_MINUTES: "30"

# Production Database Configuration  
DB_POOL_SIZE: "10"
DB_MAX_OVERFLOW: "20"
DB_POOL_TIMEOUT: "30"
DB_POOL_RECYCLE: "3600"
DB_ECHO: "false"

# Redis Configuration
REDIS_ENABLED: "true"
REDIS_HOST: "localhost"
REDIS_PORT: "6379"
REDIS_DB: "0"
REDIS_SSL: "false"
EOF < /dev/null