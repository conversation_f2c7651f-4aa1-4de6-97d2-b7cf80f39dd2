#!/bin/bash
# Infrastructure Deployment Script
# Deploys Terraform infrastructure for specified environment

set -e

# Set workspace Python for gsutil and gcloud tools
export CLOUDSDK_GSUTIL_PYTHON="/Users/<USER>/giki-ai-workspace/.venv/bin/python"
export CLOUDSDK_PYTHON="/Users/<USER>/giki-ai-workspace/.venv/bin/python"

ENVIRONMENT=${1:-development}
PROJECT_ID=${PROJECT_ID:-rezolve-poc}
REGION=${REGION:-us-central1}

echo "🚀 Deploying infrastructure for environment: $ENVIRONMENT"

# Validate environment
if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
    echo "❌ Error: Environment must be 'development' or 'production'"
    exit 1
fi

# Validate unified configuration environment file exists
ENV_CONFIG_FILE="$SCRIPT_DIR/../environments/$ENVIRONMENT/.env.$ENVIRONMENT"
if [[ ! -f "$ENV_CONFIG_FILE" ]]; then
    echo "⚠️  Warning: Unified configuration file not found: $ENV_CONFIG_FILE"
    echo "   This file is used by the unified configuration system"
    echo "   Application may fall back to environment variables"
else
    echo "✅ Unified configuration file found: $ENV_CONFIG_FILE"
fi

# Service account configuration for infrastructure deployment
TERRAFORM_SERVICE_ACCOUNT="dev-giki-ai-service-account@${PROJECT_ID}.iam.gserviceaccount.com"

# Ensure correct service account authentication for Terraform
CURRENT_ACCOUNT=$(gcloud config get-value account 2>/dev/null)
if [ "$CURRENT_ACCOUNT" != "$TERRAFORM_SERVICE_ACCOUNT" ]; then
    echo "🔑 Switching to Terraform service account: $TERRAFORM_SERVICE_ACCOUNT"
    gcloud config set account "$TERRAFORM_SERVICE_ACCOUNT"
fi

# Verify authentication
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
    echo "❌ Error: Not authenticated with gcloud"
    echo "💡 Available service accounts should include: $TERRAFORM_SERVICE_ACCOUNT"
    gcloud auth list
    exit 1
fi

# Set project
gcloud config set project $PROJECT_ID

# Enable required APIs (excluding Cloud SQL which requires organization access)
echo "📋 Enabling required Google Cloud APIs..."
gcloud services enable \
    run.googleapis.com \
    secretmanager.googleapis.com \
    storage.googleapis.com \
    aiplatform.googleapis.com \
    firebase.googleapis.com \
    iam.googleapis.com

# Create Terraform state bucket if it doesn't exist
BUCKET_NAME="giki-ai-terraform-state"
if ! gsutil ls -b gs://$BUCKET_NAME >/dev/null 2>&1; then
    echo "📦 Creating Terraform state bucket..."
    gsutil mb -p $PROJECT_ID -l $REGION gs://$BUCKET_NAME
    gsutil versioning set on gs://$BUCKET_NAME
fi

# Navigate to environment directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENV_DIR="$SCRIPT_DIR/../environments/$ENVIRONMENT"

if [[ ! -d "$ENV_DIR" ]]; then
    echo "❌ Error: Environment directory not found: $ENV_DIR"
    exit 1
fi

cd "$ENV_DIR"

# Initialize Terraform
echo "🔧 Initializing Terraform..."
terraform init

# Plan deployment
echo "📋 Planning Terraform deployment..."
terraform plan \
    -var="project_id=$PROJECT_ID" \
    -var="region=$REGION" \
    -out=tfplan

# Apply deployment
echo "🚀 Applying Terraform configuration..."
terraform apply tfplan

# Clean up plan file
rm -f tfplan

echo "✅ Infrastructure deployment completed for $ENVIRONMENT environment"

# Output important information
echo ""
echo "📋 Deployment Summary:"
echo "   Environment: $ENVIRONMENT"
echo "   Project ID:  $PROJECT_ID"
echo "   Region:      $REGION"
echo ""
echo "🔑 Next Steps:"
echo "   1. Update nx deployment configuration to use new service accounts"
echo "   2. Update application configuration to use Secret Manager"
echo "   3. Deploy applications using: nx deploy giki-ai-api --configuration=$ENVIRONMENT"
echo ""