# Service Account Directory Structure

This directory contains service account configurations for different environments.

## Directory Structure

```
service-accounts/
├── development/      # Development environment service accounts
├── production/       # Production environment service accounts
└── README.md        # This file
```

## Important Notes

- Actual service account JSON files are ignored by git for security
- Each environment should have its own service account with appropriate permissions
- Never commit actual service account keys to version control
- Use environment variables or secure secret management for production

## Cross-Platform Development

For cross-platform development:
1. Each developer needs to obtain their own service account key
2. Place the key in the appropriate directory
3. Set GOOGLE_APPLICATION_CREDENTIALS environment variable
4. See SERVICE-ACCOUNT-TRANSFER.md for secure transfer methods