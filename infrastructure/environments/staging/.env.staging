# giki.ai Staging Environment Configuration
# This file contains staging-specific settings for pre-production testing

# AI Configuration
GEMINI_API_KEY=${GEMINI_API_KEY}
GEMINI_MODEL=gemini-2.5-pro
GOOGLE_CLOUD_PROJECT=rezolve-poc
GOOGLE_GENAI_USE_VERTEXAI=true
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=/secrets/service-account/key.json

# Database Configuration - PostgreSQL (Staging)
DATABASE_URL=${DATABASE_URL}
TEST_DATABASE_URL=${TEST_DATABASE_URL}

# Cloud SQL Details (for reference)
CLOUD_SQL_CONNECTION_NAME=rezolve-poc:us-central1:giki-ai-postgres-staging
CLOUD_SQL_IP=************
CLOUD_SQL_DATABASE=giki_ai_staging_db
CLOUD_SQL_USERNAME=giki_ai_user
CLOUD_SQL_PASSWORD=${CLOUD_SQL_PASSWORD}

# Performance settings for staging (balanced between dev and prod)
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_ECHO=false
DB_POOL_PRE_PING=true

# Environment Settings
ENVIRONMENT=staging
DEBUG=false
API_HOST=0.0.0.0
API_PORT=8080

# Frontend Configuration
VITE_API_BASE_URL=https://giki-ai-api-staging-************.us-central1.run.app/api/v1
VITE_ENVIRONMENT=staging

# Google Cloud Configuration
VERTEX_SERVICE_ACCOUNT_KEY_PATH=/secrets/service-account/key.json
VERTEX_LOCATION=us-central1
VERTEX_PROJECT_ID=rezolve-poc

# Security Settings - Staging (Use environment variables)
SECRET_KEY=${SECRET_KEY}
AUTH_SECRET_KEY=${AUTH_SECRET_KEY}
ALGORITHM=RS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# AI Model Configuration
GCS_BUCKET_NAME_RAG=${GCS_BUCKET_NAME_RAG}
FILE_INGESTION_AI_MODEL_NAME=gemini-2.0-flash-001
CATEGORIZATION_MODEL_ID=gemini-2.0-flash-001
VERTEX_CHAT_MODEL_NAME=gemini-2.0-flash-001
VERTEX_AI_GEMINI_MODEL_ID=gemini-2.0-flash-001

# App Configuration
APP_HOST=0.0.0.0
APP_PORT=8080

# Redis Configuration
REDIS_ENABLED=true
REDIS_URL=${REDIS_URL}
REDIS_DB=0

# Feature Flags - Staging enables beta features for testing
FEATURE_ENABLE_BETA=true
FEATURE_ENABLE_PERFORMANCE_MONITORING=true
FEATURE_ENABLE_REAL_TIME_SYNC=true
FEATURE_ENABLE_ADVANCED_REPORTING=true

# Monitoring Configuration
METRICS_ENABLED=true
LOG_LEVEL=INFO
LOG_FORMAT=json

# CORS Configuration for staging
CORS_ALLOWED_ORIGINS=http://localhost:4200,http://localhost:3000,https://giki-ai-staging-frontend.web.app