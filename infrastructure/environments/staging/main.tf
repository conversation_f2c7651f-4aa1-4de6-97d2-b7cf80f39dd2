# Staging Environment Infrastructure Configuration
# This configuration creates staging-specific resources

terraform {
  required_version = ">= 1.0"
  backend "gcs" {
    bucket = "giki-ai-terraform-state"
    prefix = "staging"
  }
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Configure the Google Cloud Provider
provider "google" {
  project = var.project_id
  region  = var.region
}

# Local values for resource naming
locals {
  environment = "staging"
  name_prefix = "giki-ai-${local.environment}"
  
  common_labels = {
    environment = local.environment
    project     = "giki-ai"
    managed_by  = "terraform"
  }
}

# Service Account Module
module "service_accounts" {
  source = "../../modules/service-accounts"
  
  project_id   = var.project_id
  environment  = local.environment
  name_prefix  = local.name_prefix
  
  labels = local.common_labels
}

# Secrets Management Module
module "secrets" {
  source = "../../modules/secrets"
  
  project_id   = var.project_id
  environment  = local.environment
  name_prefix  = local.name_prefix
  
  labels = local.common_labels
}

# Cloud SQL Module (if needed for staging)
module "cloud_sql" {
  source = "../../modules/cloud-sql"
  
  project_id   = var.project_id
  region       = var.region
  environment  = local.environment
  name_prefix  = local.name_prefix
  
  # Staging-specific database configuration
  database_version = "POSTGRES_15"
  tier            = "db-f1-micro"  # Smaller instance for staging
  disk_size       = 20             # Smaller disk for staging
  
  labels = local.common_labels
}

# Outputs for other configurations
output "service_account_email" {
  description = "Email of the staging service account"
  value       = module.service_accounts.email
}

output "database_connection_name" {
  description = "Cloud SQL connection name for staging"
  value       = module.cloud_sql.connection_name
  sensitive   = false
}

output "database_ip" {
  description = "Cloud SQL instance IP address"
  value       = module.cloud_sql.ip_address
  sensitive   = false
}