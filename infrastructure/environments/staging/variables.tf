# Staging Environment Variables
# This file defines variables for the staging environment

variable "project_id" {
  description = "The GCP project ID for staging environment"
  type        = string
  default     = "rezolve-poc"
}

variable "region" {
  description = "The GCP region for staging resources"
  type        = string
  default     = "us-central1"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "staging"
}

variable "database_tier" {
  description = "Cloud SQL instance tier for staging"
  type        = string
  default     = "db-f1-micro"
}

variable "database_disk_size" {
  description = "Cloud SQL disk size in GB for staging"
  type        = number
  default     = 20
}

variable "enable_backup" {
  description = "Enable automated backups for staging database"
  type        = bool
  default     = true
}

variable "backup_retention_days" {
  description = "Number of days to retain backups in staging"
  type        = number
  default     = 7  # Shorter retention for staging
}

variable "high_availability" {
  description = "Enable high availability for staging (usually false for cost savings)"
  type        = bool
  default     = false
}

variable "authorized_networks" {
  description = "Authorized networks for Cloud SQL access in staging"
  type = list(object({
    name  = string
    value = string
  }))
  default = [
    {
      name  = "allow-all-for-staging"
      value = "0.0.0.0/0"  # More permissive for staging testing
    }
  ]
}