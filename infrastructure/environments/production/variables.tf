variable "project_id" {
  description = "GCP Project ID"
  type        = string
  default     = "rezolve-poc"
}

variable "region" {
  description = "GCP Region"
  type        = string
  default     = "us-central1"
}

# Unified Configuration Variables for Production
variable "secret_key" {
  description = "Application secret key (production)"
  type        = string
  sensitive   = true
}

variable "auth_secret_key" {
  description = "Authentication secret key (production)"
  type        = string
  sensitive   = true
}

variable "admin_api_key" {
  description = "Admin API key (production)"
  type        = string
  sensitive   = true
}