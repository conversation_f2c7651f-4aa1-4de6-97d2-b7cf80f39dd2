# giki.ai Production Environment Configuration
# This file contains production-specific settings

# AI Configuration
GEMINI_API_KEY=${GEMINI_API_KEY}
GEMINI_MODEL=gemini-2.5-pro
GOOGLE_CLOUD_PROJECT=rezolve-poc
GOOGLE_GENAI_USE_VERTEXAI=true
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=/secrets/service-account/key.json

# Database Configuration - PostgreSQL (Production)
DATABASE_URL=${DATABASE_URL}
TEST_DATABASE_URL=${TEST_DATABASE_URL}

# Cloud SQL Details (for reference)
CLOUD_SQL_CONNECTION_NAME=rezolve-poc:us-central1:giki-ai-postgres-prod
CLOUD_SQL_IP=************
CLOUD_SQL_DATABASE=giki_ai_db
CLOUD_SQL_USERNAME=giki_ai_user
CLOUD_SQL_PASSWORD=${CLOUD_SQL_PASSWORD}

# Performance settings for production
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_ECHO=false
DB_POOL_PRE_PING=true

# Environment Settings
ENVIRONMENT=production
DEBUG=false
API_HOST=0.0.0.0
API_PORT=8080

# Frontend Configuration
VITE_API_BASE_URL=https://giki-ai-api-************.us-central1.run.app/api/v1
VITE_ENVIRONMENT=production

# Google Cloud Configuration
VERTEX_SERVICE_ACCOUNT_KEY_PATH=/secrets/service-account/key.json
VERTEX_LOCATION=us-central1
VERTEX_PROJECT_ID=rezolve-poc

# Security Settings - Production (Use environment variables)
SECRET_KEY=${SECRET_KEY}
AUTH_SECRET_KEY=${AUTH_SECRET_KEY}
ALGORITHM=RS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
ADMIN_API_KEY=${ADMIN_API_KEY}

# AI Model Configuration
GCS_BUCKET_NAME_RAG=${GCS_BUCKET_NAME_RAG}
FILE_INGESTION_AI_MODEL_NAME=gemini-2.0-flash-001
CATEGORIZATION_MODEL_ID=gemini-2.0-flash-001
VERTEX_CHAT_MODEL_NAME=gemini-2.0-flash-001
VERTEX_AI_GEMINI_MODEL_ID=gemini-2.0-flash-001

# App Configuration
APP_HOST=0.0.0.0
APP_PORT=8080