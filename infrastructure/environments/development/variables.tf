variable "project_id" {
  description = "GCP Project ID"
  type        = string
  default     = "rezolve-poc"
}

variable "region" {
  description = "GCP Region"
  type        = string
  default     = "us-central1"
}

variable "gemini_api_key" {
  description = "Gemini API key for development"
  type        = string
  default     = ""
  sensitive   = true
}

# Unified Configuration Variables
variable "secret_key" {
  description = "Application secret key"
  type        = string
  sensitive   = true
  default     = "dev_secret_key_12345_not_for_production"
}

variable "auth_secret_key" {
  description = "Authentication secret key"
  type        = string
  sensitive   = true
  default     = "dev_auth_secret_key_12345_not_for_production_persistent"
}

variable "admin_api_key" {
  description = "Admin API key"
  type        = string
  sensitive   = true
  default     = "dev_admin_key_12345_not_for_production"
}