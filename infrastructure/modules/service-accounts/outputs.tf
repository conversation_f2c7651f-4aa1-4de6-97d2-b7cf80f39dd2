output "api_service_account_email" {
  description = "Email of the API service account"
  value       = google_service_account.api.email
}

output "frontend_service_account_email" {
  description = "Email of the frontend service account"
  value       = google_service_account.frontend.email
}

output "api_service_account_id" {
  description = "ID of the API service account"
  value       = google_service_account.api.account_id
}

output "frontend_service_account_id" {
  description = "ID of the frontend service account"
  value       = google_service_account.frontend.account_id
}

# Service account keys removed for security
# Use existing service accounts instead:
# - <EMAIL>  
# - <EMAIL>