variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "region" {
  description = "GCP Region"
  type        = string
  default     = "us-central1"
}

variable "environment" {
  description = "Environment name (development, production)"
  type        = string
  validation {
    condition     = contains(["development", "production"], var.environment)
    error_message = "Environment must be either 'development' or 'production'."
  }
}

variable "api_roles" {
  description = "IAM roles for the API service account"
  type        = list(string)
  default     = [
    "roles/cloudsql.client",
    "roles/aiplatform.user",
    "roles/secretmanager.secretAccessor",
    "roles/storage.objectUser"
  ]
}

variable "frontend_roles" {
  description = "IAM roles for the frontend service account"
  type        = list(string)
  default     = [
    "roles/firebase.sdkAdminServiceAccount"
  ]
}

# Additional roles for production environment
variable "additional_prod_roles" {
  description = "Additional IAM roles for production environment"
  type        = list(string)
  default     = [
    "roles/logging.logWriter",
    "roles/monitoring.metricWriter",
    "roles/cloudtrace.agent"
  ]
}