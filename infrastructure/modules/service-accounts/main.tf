# Service Accounts Module
# Creates environment-specific service accounts with proper IAM roles

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

locals {
  # Environment-specific service account names
  api_service_account_id    = "giki-ai-${var.environment}-api"
  frontend_service_account_id = "giki-ai-${var.environment}-frontend"
  
  # Common labels
  labels = {
    environment = var.environment
    project     = "giki-ai"
    managed_by  = "terraform"
  }
}

# API Service Account
resource "google_service_account" "api" {
  account_id   = local.api_service_account_id
  display_name = "Giki AI API Service Account (${title(var.environment)})"
  description  = "Service account for Giki AI API in ${var.environment} environment"
  project      = var.project_id
}

# Frontend Service Account  
resource "google_service_account" "frontend" {
  account_id   = local.frontend_service_account_id
  display_name = "Giki AI Frontend Service Account (${title(var.environment)})"
  description  = "Service account for Giki AI Frontend deployments in ${var.environment} environment"
  project      = var.project_id
}

# API Service Account IAM Roles
resource "google_project_iam_member" "api_roles" {
  for_each = toset(var.api_roles)
  
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.api.email}"
}

# Frontend Service Account IAM Roles
resource "google_project_iam_member" "frontend_roles" {
  for_each = toset(var.frontend_roles)
  
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.frontend.email}"
}

# Service account keys should be managed separately for security
# Use existing service accounts: <EMAIL>
# and <EMAIL>